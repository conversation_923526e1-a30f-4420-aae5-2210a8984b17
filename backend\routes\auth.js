const express = require('express');
const router = express.Router();
const {
  register,
  login,
  getProfile,
  updateProfile,
  verifyEmail,
  forgotPassword,
  resetPassword,
  resendVerification
} = require('../controllers/authController');
const {
  validateUserRegistration,
  validateUserLogin,
  validateForgotPassword,
  validateResetPassword,
  validateEmailVerification
} = require('../middleware/validation');
const rateLimit = require('express-rate-limit');
const { authenticateToken, adminOnly } = require('../middleware/auth');

// Rate limiting for authentication endpoints
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: process.env.NODE_ENV === 'development' ? 100 : 5, // 100 requests in dev, 5 in production
  message: {
    success: false,
    message: 'Too many authentication attempts, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false
});

const passwordResetLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: process.env.NODE_ENV === 'development' ? 50 : 3, // 50 requests in dev, 3 in production
  message: {
    success: false,
    message: 'Too many password reset attempts, please try again later.'
  }
});

// Public routes
router.post('/register', authLimiter, validateUserRegistration, register);
router.post('/login', authLimiter, validateUserLogin, login);
router.post('/verify-email', validateEmailVerification, verifyEmail);
router.post('/forgot-password', passwordResetLimiter, validateForgotPassword, forgotPassword);
router.post('/reset-password', validateResetPassword, resetPassword);
router.post('/resend-verification', authLimiter, resendVerification);

// Protected routes
router.get('/profile', authenticateToken, getProfile);
router.put('/profile', authenticateToken, updateProfile);

// Alias for profile endpoint (commonly used as /me)
router.get('/me', authenticateToken, getProfile);

module.exports = router;
