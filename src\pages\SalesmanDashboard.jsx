import React from 'react';
import { Link } from 'react-router-dom';
import { BarChart2Icon, DollarSignIcon, ShoppingCartIcon, TrendingUpIcon, UsersIcon, AwardIcon } from 'lucide-react';
import { useData } from '../contexts/DataContext';
import { useAuth } from '../contexts/AuthContext';

const SalesmanDashboard = () => {
  const {
    user
  } = useAuth();
  const {
    orders,
    salesmen,
    getRecentOrders,
    customers
  } = useData();

  // Get current salesman data
  // In a real app, this would be fetched based on the logged-in user's ID
  const currentSalesman = salesmen.length > 0 ? salesmen[0] : {
    id: user?.user_id || 1,
    name: user?.name || 'Salesman',
    commission: 0,
    currentStage: 1,
    progress: 0,
    bottlesSold: 0,
    target: 1000,
    status: 'active'
  };

  // Get orders assigned to this salesman
  const mySalesOrders = orders.filter(order =>
    order.salesmanId === currentSalesman.id ||
    (order.salesman && order.salesman.id === currentSalesman.id)
  );
  const recentOrders = getRecentOrders(5).filter(order =>
    order.salesmanId === currentSalesman.id ||
    (order.salesman && order.salesman.id === currentSalesman.id)
  );

  // Calculate total sales and commission
  const totalSales = mySalesOrders.reduce((sum, order) => sum + order.totalAmount, 0);
  const totalPaid = mySalesOrders.reduce((sum, order) => sum + order.paidAmount, 0);
  const pendingPayments = mySalesOrders.reduce((sum, order) => {
    // FIXED: Exclude cancelled orders from pending payments calculation
    if (order.status === 'cancelled') return sum;
    return sum + (order.totalAmount - order.paidAmount);
  }, 0);

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">
            Salesman Dashboard
          </h1>
          <p className="text-gray-600">Welcome back, {user?.name}</p>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-sm font-medium text-gray-500">TOTAL SALES</h3>
            <ShoppingCartIcon size={20} className="text-blue-600" />
          </div>
          <p className="text-2xl font-bold text-gray-900">
            {formatCurrency(totalSales)}
          </p>
          <div className="mt-2 flex items-center text-sm">
            <span className="text-blue-600">+8%</span>
            <span className="text-gray-600 ml-1">from last month</span>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-sm font-medium text-gray-500">COMMISSION</h3>
            <DollarSignIcon size={20} className="text-green-600" />
          </div>
          <p className="text-2xl font-bold text-gray-900">
            {formatCurrency(currentSalesman.commission)}
          </p>
          <div className="mt-2 flex items-center text-sm">
            <Link to="/sales/commissions" className="text-blue-600 hover:underline">
              View details
            </Link>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-sm font-medium text-gray-500">
              PENDING PAYMENTS
            </h3>
            <DollarSignIcon size={20} className="text-yellow-600" />
          </div>
          <p className="text-2xl font-bold text-gray-900">
            {formatCurrency(pendingPayments)}
          </p>
          <div className="mt-2 flex items-center text-sm">
            <Link to="/sales/credits" className="text-blue-600 hover:underline">
              Follow up
            </Link>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-sm font-medium text-gray-500">ORDERS</h3>
            <ShoppingCartIcon size={20} className="text-indigo-600" />
          </div>
          <p className="text-2xl font-bold text-gray-900">
            {mySalesOrders.length}
          </p>
          <div className="mt-2 flex items-center text-sm">
            <Link to="/sales/orders" className="text-blue-600 hover:underline">
              View orders
            </Link>
          </div>
        </div>
      </div>

      {/* Commission Progress */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center">
            <AwardIcon size={20} className="text-blue-600 mr-2" />
            <h2 className="text-lg font-medium text-gray-800">
              Commission Progress
            </h2>
          </div>
          <Link to="/sales/commissions" className="text-sm text-blue-600 hover:text-blue-800">
            View Details
          </Link>
        </div>
        <div className="mb-4">
          <div className="flex justify-between mb-1">
            <span className="text-sm font-medium text-gray-700">
              Stage {currentSalesman.currentStage} Progress
            </span>
            <span className="text-sm font-medium text-gray-700">
              {currentSalesman.progress}%
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div className="bg-blue-600 h-2.5 rounded-full" style={{
              width: `${currentSalesman.progress}%`
            }}></div>
          </div>
          <div className="flex justify-between mt-1 text-xs text-gray-500">
            <span>{currentSalesman.bottlesSold} bottles sold</span>
            <span>Target: {currentSalesman.target} bottles</span>
          </div>
        </div>
        <div className="bg-blue-50 p-4 rounded-lg">
          <div className="flex items-center">
            <div className="mr-4">
              <div className="bg-blue-100 p-3 rounded-full">
                <TrendingUpIcon size={24} className="text-blue-600" />
              </div>
            </div>
            <div>
              <p className="font-medium text-blue-800">
                You're {currentSalesman.target - currentSalesman.bottlesSold}{' '}
                bottles away from reaching Stage{' '}
                {currentSalesman.currentStage + 1}
              </p>
              <p className="text-sm text-blue-700 mt-1">
                Complete Stage {currentSalesman.currentStage + 1} to increase
                your commission rate and earn a bonus!
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Orders */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 className="text-lg font-medium text-gray-800">Recent Orders</h2>
            <Link to="/sales/orders" className="text-sm text-blue-600 hover:text-blue-800">
              View All
            </Link>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Order #
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Customer
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {recentOrders.map(order => (
                  <tr key={order.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">
                      {order.orderNumber}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {order.customer.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {order.date}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatCurrency(order.totalAmount)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {(() => {
                        // FIXED: Apply cancelled order payment status logic
                        const isCancelledOrder = order.status === 'cancelled';
                        const actualPaymentStatus = isCancelledOrder ? 'cancelled' : order.paymentStatus;

                        const getStatusBadgeClass = (status) => {
                          switch (status) {
                            case 'paid': return 'bg-green-100 text-green-800';
                            case 'partial': return 'bg-blue-100 text-blue-800';
                            case 'pending': return 'bg-yellow-100 text-yellow-800';
                            case 'cancelled': return 'bg-red-100 text-red-800';
                            default: return 'bg-gray-100 text-gray-800';
                          }
                        };

                        const getStatusText = (status) => {
                          switch (status) {
                            case 'paid': return 'Paid';
                            case 'partial': return 'Partially Paid';
                            case 'pending': return 'Pending';
                            case 'cancelled': return 'Cancelled';
                            default: return 'Unknown';
                          }
                        };

                        return (
                          <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(actualPaymentStatus)}`}>
                            {getStatusText(actualPaymentStatus)}
                          </span>
                        );
                      })()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Customer Management */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 className="text-lg font-medium text-gray-800">Top Customers</h2>
            <Link to="/customers" className="text-sm text-blue-600 hover:text-blue-800">
              View All
            </Link>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {customers.sort((a, b) => b.totalSpent - a.totalSpent).slice(0, 5).map(customer => (
                <div key={customer.id} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="bg-blue-100 p-2 rounded-full">
                      <UsersIcon size={16} className="text-blue-600" />
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-gray-900">
                        {customer.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {customer.contact}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">
                      {formatCurrency(customer.totalSpent)}
                    </p>
                    <p className="text-xs text-gray-500">
                      {customer.totalOrders} orders
                    </p>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-6">
              <Link to="/sales/orders" className="flex items-center justify-center w-full py-2 px-4 border border-blue-300 rounded-md shadow-sm text-sm font-medium text-blue-700 bg-blue-50 hover:bg-blue-100">
                <ShoppingCartIcon size={16} className="mr-2" />
                Create New Order
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SalesmanDashboard;
