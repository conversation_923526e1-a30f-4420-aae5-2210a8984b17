import { useState, useMemo } from 'react';
import {
  ClockIcon,
  SearchIcon,
  FilterIcon,
  DownloadIcon,
  CalendarIcon,
  UserIcon,
  PackageIcon,
  TruckIcon,
  ShoppingCartIcon,
  EyeIcon
} from 'lucide-react';

// Utility function to get quantity direction and styling
const getQuantityIndicator = (transaction) => {
  const { type, quantity, notes } = transaction;

  // Convert quantity to number to ensure proper comparison
  let qty = Number(quantity) || 0;

  // Determine if this transaction increases or decreases stock
  const increaseTypes = ['in', 'restock', 'return', 'transfer_in'];
  const decreaseTypes = ['out', 'sale', 'transfer_out', 'waste', 'damaged'];

  let isIncrease = false;
  let isDecrease = false;

  // Check transaction type first
  if (increaseTypes.includes(type)) {
    isIncrease = true;
  } else if (decreaseTypes.includes(type)) {
    isDecrease = true;
  } else if (type === 'adjustment') {
    // For adjustments, determine direction based on context and calculate actual difference
    if (notes) {
      const notesLower = notes.toLowerCase();

      // Look for stock change patterns like "70 → 80" or "Stock adjustment: 70 → 80"
      const stockPattern = notesLower.match(/(\d+)\s*→\s*(\d+)/);
      if (stockPattern) {
        const [, before, after] = stockPattern;
        const beforeQty = parseInt(before);
        const afterQty = parseInt(after);
        const actualDifference = afterQty - beforeQty;

        if (actualDifference > 0) {
          isIncrease = true;
          // Override quantity with actual difference for display
          qty = Math.abs(actualDifference);
        } else if (actualDifference < 0) {
          isDecrease = true;
          // Override quantity with actual difference for display
          qty = Math.abs(actualDifference);
        }
      } else if (notesLower.includes('stock restock') ||
                 notesLower.includes('increase') ||
                 notesLower.includes('add') ||
                 notesLower.includes('restock')) {
        isIncrease = true;
      } else if (notesLower.includes('decrease') ||
                 notesLower.includes('reduce') ||
                 notesLower.includes('remove') ||
                 notesLower.includes('sold') ||
                 notesLower.includes('sale') ||
                 notesLower.includes('out')) {
        isDecrease = true;
      } else {
        // For adjustments without clear patterns, we need to be more careful
        // Check if the notes contain any indication of direction
        if (notesLower.includes('updated') || notesLower.includes('edit') || notesLower.includes('changed')) {
          // For product updates, the quantity field might represent the new stock level, not the change
          // We should treat this as neutral unless we can determine the direction
          // Look for negative quantities which clearly indicate decreases
          if (qty < 0) {
            isDecrease = true;
            qty = Math.abs(qty); // Make quantity positive for display
          } else {
            // For positive quantities in updates, we can't reliably determine direction
            // without knowing the previous stock level, so treat as neutral
            isIncrease = false;
            isDecrease = false;
          }
        } else {
          // For other adjustment types, use the quantity sign as direction indicator
          if (qty > 0) {
            isIncrease = true;
          } else if (qty < 0) {
            isDecrease = true;
            qty = Math.abs(qty); // Make quantity positive for display
          }
        }
      }
    } else {
      // No notes, use quantity value
      if (qty > 0) {
        isIncrease = true;
      } else if (qty < 0) {
        isDecrease = true;
      }
    }
  }

  // Set display properties based on direction
  let direction = '';
  let colorClass = '';

  if (isIncrease) {
    direction = '+';
    colorClass = 'text-green-600 font-medium';
  } else if (isDecrease) {
    direction = '-';
    colorClass = 'text-red-600 font-medium';
  } else {
    // Neutral or unknown - show quantity without direction
    direction = '';
    colorClass = 'text-gray-600';
  }

  // Build display quantity with single symbol (no duplicate icons)
  const displayQuantity = `${direction}${Math.abs(qty)} units`;

  // Enhanced debug logging for development
  if (process.env.NODE_ENV === 'development') {
    console.log(`🔍 InventoryHistory Quantity Indicator Debug:`, {
      originalQuantity: quantity,
      processedQuantity: qty,
      type,
      notes: notes?.substring(0, 100) + (notes?.length > 100 ? '...' : ''),
      isIncrease,
      isDecrease,
      direction,
      displayQuantity,
      colorClass,
      // Test case verification
      expectedDirection: type === 'in' ? '+' : type === 'out' ? '-' : 'varies'
    });
  }

  return {
    direction,
    colorClass,
    displayQuantity, // This already includes the direction symbol
    isIncrease,
    isDecrease
  };
};

function InventoryHistory({ inventoryTransactions, products, customers, users }) {
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const [dateFrom, setDateFrom] = useState('');
  const [dateTo, setDateTo] = useState('');
  const [userFilter, setUserFilter] = useState('');
  const [productFilter, setProductFilter] = useState('');
  const [sortBy, setSortBy] = useState('date');
  const [sortOrder, setSortOrder] = useState('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedTransaction, setSelectedTransaction] = useState(null);
  const itemsPerPage = 50;





  // Get unique values for filters
  const uniqueUsers = useMemo(() => {
    const userIds = [...new Set(inventoryTransactions.map(t => t.recordedBy || t.user))];
    return userIds.filter(Boolean);
  }, [inventoryTransactions]);

  const uniqueProducts = useMemo(() => {
    const productNames = [...new Set(inventoryTransactions.map(t => t.productName))];
    return productNames.filter(Boolean);
  }, [inventoryTransactions]);

  // Filter and sort transactions
  const filteredTransactions = useMemo(() => {
    if (!inventoryTransactions || inventoryTransactions.length === 0) {
      return [];
    }

    let filtered = inventoryTransactions.filter(transaction => {
      // Handle different data structure formats from backend
      const productName = transaction.productName || transaction.product_name || '';
      const supplier = transaction.supplier || transaction.supplier_name || '';
      const customer = transaction.customer || transaction.customer_name || '';
      const invoiceNumber = transaction.invoiceNumber || transaction.invoice_number || '';
      const referenceNumber = transaction.referenceNumber || transaction.reference_number || '';

      const matchesSearch = productName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           supplier.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           referenceNumber.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesType = !typeFilter || transaction.type === typeFilter;

      const transactionDate = transaction.date || transaction.transaction_date || transaction.createdAt;
      const matchesDateFrom = !dateFrom || new Date(transactionDate) >= new Date(dateFrom);
      const matchesDateTo = !dateTo || new Date(transactionDate) <= new Date(dateTo);

      const recordedBy = transaction.recordedBy || transaction.recorded_by || transaction.user;
      const matchesUser = !userFilter || recordedBy === userFilter;

      const matchesProduct = !productFilter || productName === productFilter;

      return matchesSearch && matchesType && matchesDateFrom && matchesDateTo && matchesUser && matchesProduct;
    });

    // Sort transactions
    filtered.sort((a, b) => {
      let aValue, bValue;

      switch (sortBy) {
        case 'date':
          aValue = new Date(a.date);
          bValue = new Date(b.date);
          break;
        case 'product':
          aValue = a.productName?.toLowerCase() || '';
          bValue = b.productName?.toLowerCase() || '';
          break;
        case 'quantity':
          aValue = a.quantity;
          bValue = b.quantity;
          break;
        case 'totalCost':
          aValue = a.totalCost;
          bValue = b.totalCost;
          break;
        case 'type':
          aValue = a.type;
          bValue = b.type;
          break;
        default:
          aValue = a[sortBy];
          bValue = b[sortBy];
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return filtered;
  }, [inventoryTransactions, searchTerm, typeFilter, dateFrom, dateTo, userFilter, productFilter, sortBy, sortOrder]);

  // Pagination
  const totalPages = Math.ceil(filteredTransactions.length / itemsPerPage);
  const paginatedTransactions = filteredTransactions.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const getTransactionIcon = (type) => {
    switch (type) {
      case 'in':
        return <TruckIcon size={16} className="text-green-600" />;
      case 'out':
        return <ShoppingCartIcon size={16} className="text-red-600" />;
      case 'adjustment':
        return <PackageIcon size={16} className="text-blue-600" />;
      default:
        return <PackageIcon size={16} className="text-gray-600" />;
    }
  };

  const getTransactionTypeLabel = (type) => {
    switch (type) {
      case 'in':
        return 'Inventory In';
      case 'out':
        return 'Inventory Out';
      case 'adjustment':
        return 'Stock Adjustment';
      default:
        return type;
    }
  };

  const getTransactionTypeColor = (type) => {
    switch (type) {
      case 'in':
        return 'bg-green-100 text-green-800';
      case 'out':
        return 'bg-red-100 text-red-800';
      case 'adjustment':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const exportToCSV = () => {
    const headers = [
      'Date/Time', 'Transaction Type', 'Product', 'Quantity', 'Unit Price',
      'Total Cost', 'Supplier/Customer', 'User', 'Reference Numbers', 'Notes'
    ];
    const csvData = filteredTransactions.map(transaction => [
      transaction.date,
      getTransactionTypeLabel(transaction.type),
      transaction.productName,
      transaction.quantity,
      transaction.unitPrice || '',
      transaction.totalCost || '',
      transaction.supplier || transaction.customer || '',
      transaction.recordedBy || transaction.user || '',
      `${transaction.invoiceNumber || ''} ${transaction.referenceNumber || ''}`.trim(),
      transaction.notes || ''
    ]);

    const csvContent = [headers, ...csvData]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `inventory_history_${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };



  // Safety check for data
  if (!inventoryTransactions) {
    return (
      <div className="flex items-center justify-center h-64 bg-yellow-50 border border-yellow-200 rounded-lg">
        <div className="text-center">
          <ClockIcon size={48} className="text-yellow-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-yellow-700 mb-2">No Transaction Data</h3>
          <p className="text-yellow-600">Inventory transactions data is not available.</p>
        </div>
      </div>
    );
  }

  if (inventoryTransactions.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="text-center">
          <ClockIcon size={48} className="text-blue-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-blue-700 mb-2">No Transactions Found</h3>
          <p className="text-blue-600">No inventory transactions have been recorded yet.</p>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <ClockIcon size={20} className="text-blue-600 mr-2" />
          <h2 className="text-lg font-medium text-gray-800">Inventory History</h2>
        </div>
        <div className="text-sm text-gray-600">
          {filteredTransactions.length} transactions found
        </div>
      </div>

      {/* Advanced Filters */}
      <div className="bg-gray-50 rounded-lg p-4 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Search</label>
            <div className="relative">
              <SearchIcon size={16} className="absolute left-3 top-3 text-gray-400" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search transactions..."
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Transaction Type</label>
            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Types</option>
              <option value="in">Inventory In</option>
              <option value="out">Inventory Out</option>
              <option value="adjustment">Stock Adjustment</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Date From</label>
            <div className="relative">
              <CalendarIcon size={16} className="absolute left-3 top-3 text-gray-400" />
              <input
                type="date"
                value={dateFrom}
                onChange={(e) => setDateFrom(e.target.value)}
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Date To</label>
            <div className="relative">
              <CalendarIcon size={16} className="absolute left-3 top-3 text-gray-400" />
              <input
                type="date"
                value={dateTo}
                onChange={(e) => setDateTo(e.target.value)}
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Product</label>
            <select
              value={productFilter}
              onChange={(e) => setProductFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Products</option>
              {uniqueProducts.map(product => (
                <option key={product} value={product}>{product}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">User</label>
            <select
              value={userFilter}
              onChange={(e) => setUserFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Users</option>
              {uniqueUsers.map(userId => (
                <option key={userId} value={userId}>{userId}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
            <div className="flex space-x-2">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="date">Date</option>
                <option value="product">Product</option>
                <option value="type">Type</option>
                <option value="quantity">Quantity</option>
                <option value="totalCost">Total Cost</option>
              </select>
              <button
                onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                className="px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-100"
                title={`Sort ${sortOrder === 'asc' ? 'Descending' : 'Ascending'}`}
              >
                {sortOrder === 'asc' ? '↑' : '↓'}
              </button>
            </div>
          </div>

          <div className="flex items-end">
            <button
              onClick={exportToCSV}
              className="w-full flex items-center justify-center px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
            >
              <DownloadIcon size={16} className="mr-2" />
              Export
            </button>
          </div>
        </div>

        <div className="flex justify-between items-center">
          <div className="text-sm text-gray-600">
            Showing {paginatedTransactions.length} of {filteredTransactions.length} transactions
          </div>
          <button
            onClick={() => {
              setSearchTerm('');
              setTypeFilter('');
              setDateFrom('');
              setDateTo('');
              setUserFilter('');
              setProductFilter('');
              setCurrentPage(1);
            }}
            className="text-sm text-blue-600 hover:text-blue-800"
          >
            Clear Filters
          </button>
        </div>
      </div>

      {/* Transactions Table */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        {/* Simple fallback if no paginated transactions */}
        {paginatedTransactions.length === 0 ? (
          <div className="p-8 text-center">
            <div className="bg-gray-50 rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-700 mb-4">No Transactions to Display</h3>
              <div className="text-sm text-gray-600 space-y-2">
                <div>Total inventory transactions: {inventoryTransactions?.length || 0}</div>
                <div>Filtered transactions: {filteredTransactions?.length || 0}</div>
                <div>Paginated transactions: {paginatedTransactions?.length || 0}</div>
                <div>Current page: {currentPage}</div>
                <div>Items per page: {itemsPerPage}</div>
              </div>
              {inventoryTransactions?.length > 0 && (
                <div className="mt-4 p-4 bg-blue-50 rounded text-left">
                  <h4 className="font-medium text-blue-800 mb-2">Sample Transaction Data:</h4>
                  <div className="text-xs text-blue-700">
                    <div>Product: {inventoryTransactions[0].productName || 'Unknown'}</div>
                    <div>Type: {inventoryTransactions[0].type || 'Unknown'}</div>
                    <div>Quantity: {inventoryTransactions[0].quantity || 'Unknown'}</div>
                    <div>Date: {inventoryTransactions[0].date || 'Unknown'}</div>
                  </div>
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date/Time</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Price</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Cost</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier/Customer</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {paginatedTransactions.map((transaction) => {
                // Handle different data structure formats
                const transactionId = transaction.id || transaction.transaction_id;
                const transactionDate = transaction.date || transaction.transaction_date || transaction.createdAt;
                const productName = transaction.productName || transaction.product_name || 'Unknown Product';
                const unitPrice = transaction.unitPrice || transaction.unit_price;
                const totalCost = transaction.totalCost || transaction.total_cost;
                const supplier = transaction.supplier || transaction.supplier_name;
                const customer = transaction.customer || transaction.customer_name;
                const recordedBy = transaction.recordedBy || transaction.recorded_by || transaction.user;

                return (
                  <tr key={transactionId} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {transactionDate ? new Date(transactionDate).toLocaleDateString() : 'N/A'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {getTransactionIcon(transaction.type)}
                        <span className={`ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getTransactionTypeColor(transaction.type)}`}>
                          {getTransactionTypeLabel(transaction.type)}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{productName}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    {(() => {
                      const indicator = getQuantityIndicator(transaction);
                      return (
                        <span className={indicator.colorClass}>
                          {indicator.displayQuantity}
                        </span>
                      );
                    })()}
                  </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {unitPrice ? `$${unitPrice.toFixed(2)}` : '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                      {totalCost ? `$${totalCost.toFixed(2)}` : '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {supplier || customer || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {recordedBy || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        onClick={() => setSelectedTransaction(transaction)}
                        className="text-blue-600 hover:text-blue-900"
                        title="View Details"
                      >
                        <EyeIcon size={16} />
                      </button>
                    </td>
                  </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Previous
              </button>
              <button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Next
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Showing <span className="font-medium">{(currentPage - 1) * itemsPerPage + 1}</span> to{' '}
                  <span className="font-medium">{Math.min(currentPage * itemsPerPage, filteredTransactions.length)}</span> of{' '}
                  <span className="font-medium">{filteredTransactions.length}</span> results
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  {Array.from({ length: Math.min(totalPages, 10) }, (_, i) => {
                    const page = Math.max(1, Math.min(totalPages - 9, currentPage - 5)) + i;
                    return (
                      <button
                        key={page}
                        onClick={() => setCurrentPage(page)}
                        className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                          page === currentPage
                            ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                            : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                        }`}
                      >
                        {page}
                      </button>
                    );
                  })}
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Transaction Detail Modal */}
      {selectedTransaction && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">Transaction Details</h3>
                <button
                  onClick={() => setSelectedTransaction(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>
              <div className="space-y-3 text-sm">
                <div><strong>Date:</strong> {selectedTransaction.date}</div>
                <div><strong>Type:</strong> {getTransactionTypeLabel(selectedTransaction.type)}</div>
                <div><strong>Product:</strong> {selectedTransaction.productName}</div>
                <div className="flex items-center">
                  <strong className="mr-2">Quantity:</strong>
                  {(() => {
                    const indicator = getQuantityIndicator(selectedTransaction);
                    return (
                      <span className={indicator.colorClass}>
                        {indicator.displayQuantity}
                      </span>
                    );
                  })()}
                </div>
                {selectedTransaction.unitPrice && <div><strong>Unit Price:</strong> ${selectedTransaction.unitPrice.toFixed(2)}</div>}
                {selectedTransaction.totalCost && <div><strong>Total Cost:</strong> ${selectedTransaction.totalCost.toFixed(2)}</div>}
                {selectedTransaction.supplier && <div><strong>Supplier:</strong> {selectedTransaction.supplier}</div>}
                {selectedTransaction.customer && <div><strong>Customer:</strong> {selectedTransaction.customer}</div>}

                {/*
                  ENHANCEMENT: Add salesman information to inventory history detail view
                  - Shows salesman name for "out" transactions linked to orders with assigned salesmen
                  - Shows "Direct Sale" for "out" transactions linked to orders without assigned salesmen
                  - Shows nothing for "out" transactions not linked to any order
                  - Only applies to "out" transaction types
                */}
                {selectedTransaction.type === 'out' && (() => {
                  // Check if transaction is linked to an order with salesman information
                  const order = selectedTransaction.order;
                  const salesman = order?.salesman;

                  // Debug logging
                  console.log('🔍 Transaction detail - Order data:', {
                    transactionType: selectedTransaction.type,
                    hasOrder: !!order,
                    orderData: order,
                    hasSalesman: !!salesman,
                    salesmanData: salesman
                  });

                  if (order) {
                    if (salesman?.full_name) {
                      return <div><strong>Salesman:</strong> {salesman.full_name}</div>;
                    } else {
                      return <div><strong>Salesman:</strong> Direct Sale</div>;
                    }
                  }
                  // If no order is linked, don't show salesman info
                  return null;
                })()}

                {selectedTransaction.invoiceNumber && <div><strong>Invoice Number:</strong> {selectedTransaction.invoiceNumber}</div>}
                {selectedTransaction.referenceNumber && <div><strong>Reference Number:</strong> {selectedTransaction.referenceNumber}</div>}
                {selectedTransaction.batchNumber && <div><strong>Batch Number:</strong> {selectedTransaction.batchNumber}</div>}
                {selectedTransaction.storageLocation && <div><strong>Storage Location:</strong> {selectedTransaction.storageLocation}</div>}
                {selectedTransaction.notes && <div><strong>Notes:</strong> {selectedTransaction.notes}</div>}
                <div><strong>Recorded By:</strong> {selectedTransaction.recordedBy || selectedTransaction.user || 'Unknown'}</div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default InventoryHistory;
