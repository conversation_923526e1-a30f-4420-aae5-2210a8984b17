import React, { useState, useEffect } from 'react';
import { 
  BellIcon, 
  XIcon, 
  AlertTriangleIcon,
  TrendingUpIcon,
  CheckIcon
} from 'lucide-react';

const NotificationBanner = ({ user }) => {
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showAll, setShowAll] = useState(false);

  // Fetch unread notifications
  const fetchNotifications = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('auth_token');
      const response = await fetch(
        'http://localhost:3001/api/notifications?unread_only=true&limit=5',
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setNotifications(result.data.notifications || []);
        }
      }
    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  // Mark notification as read
  const markAsRead = async (notificationId) => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch(
        `http://localhost:3001/api/notifications/${notificationId}/read`,
        {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.ok) {
        setNotifications(prev => 
          prev.filter(n => n.notification_id !== notificationId)
        );
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  // Mark all as read
  const markAllAsRead = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch(
        'http://localhost:3001/api/notifications/mark-all-read',
        {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.ok) {
        setNotifications([]);
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  // Fetch notifications on component mount and periodically
  useEffect(() => {
    if (user?.role === 'admin') {
      fetchNotifications();
      
      // Poll for new notifications every 30 seconds
      const interval = setInterval(fetchNotifications, 30000);
      return () => clearInterval(interval);
    }
  }, [user]);

  // Don't show banner if no notifications or user is not admin
  if (!user || user.role !== 'admin' || notifications.length === 0) {
    return null;
  }

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'stage_progression':
        return <TrendingUpIcon size={20} className="text-orange-600" />;
      default:
        return <BellIcon size={20} className="text-blue-600" />;
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'urgent':
        return 'border-red-500 bg-red-50';
      case 'high':
        return 'border-orange-500 bg-orange-50';
      case 'medium':
        return 'border-blue-500 bg-blue-50';
      default:
        return 'border-gray-500 bg-gray-50';
    }
  };

  const formatTimeAgo = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now - date) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  const visibleNotifications = showAll ? notifications : notifications.slice(0, 2);

  return (
    <div className="mb-6">
      {visibleNotifications.map((notification) => (
        <div
          key={notification.notification_id}
          className={`border-l-4 p-4 mb-3 rounded-r-lg shadow-sm ${getPriorityColor(notification.priority)}`}
        >
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 mt-0.5">
                {getNotificationIcon(notification.type)}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium text-gray-900">
                    {notification.title}
                  </h4>
                  <span className="text-xs text-gray-500">
                    {formatTimeAgo(notification.created_at)}
                  </span>
                </div>
                
                <p className="mt-1 text-sm text-gray-700">
                  {notification.message}
                </p>
                
                {notification.data && notification.data.sales_amount && (
                  <div className="mt-2 text-xs text-gray-600">
                    <span className="font-medium">Sales Amount:</span> ${notification.data.sales_amount.toLocaleString()}
                    {notification.data.current_stage_name && (
                      <>
                        {' • '}
                        <span className="font-medium">Current Stage:</span> {notification.data.current_stage_name}
                      </>
                    )}
                  </div>
                )}
              </div>
            </div>
            
            <div className="flex items-center space-x-2 ml-4">
              <button
                onClick={() => markAsRead(notification.notification_id)}
                className="p-1 text-gray-400 hover:text-green-600 transition-colors"
                title="Mark as read"
              >
                <CheckIcon size={16} />
              </button>
              
              <button
                onClick={() => markAsRead(notification.notification_id)}
                className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                title="Dismiss"
              >
                <XIcon size={16} />
              </button>
            </div>
          </div>
        </div>
      ))}
      
      {notifications.length > 2 && (
        <div className="flex items-center justify-between mt-3">
          <button
            onClick={() => setShowAll(!showAll)}
            className="text-sm text-blue-600 hover:text-blue-800 font-medium"
          >
            {showAll ? 'Show Less' : `Show All (${notifications.length})`}
          </button>
          
          {notifications.length > 0 && (
            <button
              onClick={markAllAsRead}
              className="text-sm text-gray-600 hover:text-gray-800 font-medium"
            >
              Mark All as Read
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default NotificationBanner;
