# 🚀 **FULL-<PERSON><PERSON><PERSON> INVENTORY MANAGEMENT SYSTEM SETUP GUIDE**

## **📋 OVERVIEW**

This guide will help you transform your React inventory management system into a full-stack application with:
- **Backend**: Node.js/Express with PostgreSQL database
- **Frontend**: React with API integration
- **Authentication**: JWT-based user authentication
- **Real-time Data**: Persistent storage with proper synchronization

---

## **🔧 PREREQUISITES**

### **Required Software:**
- **Node.js** (v16 or higher) - [Download](https://nodejs.org/)
- **PostgreSQL** (v12 or higher) - [Download](https://www.postgresql.org/download/)
- **Git** - [Download](https://git-scm.com/)
- **Code Editor** (VS Code recommended)

### **Optional Tools:**
- **pgAdmin** - PostgreSQL administration tool
- **Postman** - API testing tool

---

## **📊 STEP 1: DATABASE SETUP**

### **1.1 Install PostgreSQL**
```bash
# Windows (using Chocolatey)
choco install postgresql

# macOS (using Homebrew)
brew install postgresql

# Ubuntu/Debian
sudo apt-get install postgresql postgresql-contrib
```

### **1.2 Create Database**
```sql
-- Connect to PostgreSQL as superuser
psql -U postgres

-- Create database and user
CREATE DATABASE inventory_management;
CREATE USER inventory_user WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE inventory_management TO inventory_user;

-- Exit psql
\q
```

### **1.3 Verify Connection**
```bash
psql -U inventory_user -d inventory_management -h localhost
```

---

## **🖥️ STEP 2: BACKEND SETUP**

### **2.1 Navigate to Backend Directory**
```bash
cd backend
```

### **2.2 Install Dependencies**
```bash
npm install
```

### **2.3 Environment Configuration**
```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your database credentials
```

**Edit `.env` file:**
```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=inventory_management
DB_USER=inventory_user
DB_PASSWORD=your_secure_password
DB_URL=postgresql://inventory_user:your_secure_password@localhost:5432/inventory_management

# Server Configuration
PORT=3001
NODE_ENV=development

# JWT Configuration (Generate a secure random string)
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_random_123456789
JWT_EXPIRES_IN=7d

# CORS Configuration
FRONTEND_URL=http://localhost:5177
```

### **2.4 Run Database Migrations**
```bash
npm run migrate
```

### **2.5 Seed Database with Default Data**
```bash
npm run seed
```

### **2.6 Start Backend Server**
```bash
# Development mode (with auto-restart)
npm run dev

# Production mode
npm start
```

**✅ Backend should now be running on http://localhost:3001**

---

## **🎨 STEP 3: FRONTEND SETUP**

### **3.1 Navigate to Frontend Directory**
```bash
cd ..  # Go back to root directory
```

### **3.2 Environment Configuration**
```bash
# Copy environment template
cp .env.example .env

# Edit .env file
```

**Edit `.env` file:**
```env
# Backend API Configuration
VITE_API_BASE_URL=http://localhost:3001/api

# Development Configuration
VITE_NODE_ENV=development
```

### **3.3 Install Additional Dependencies**
```bash
npm install
```

### **3.4 Start Frontend Development Server**
```bash
npm run dev
```

**✅ Frontend should now be running on http://localhost:5177**

---

## **🔐 STEP 4: AUTHENTICATION SETUP**

### **4.1 Default Login Credentials**
After seeding the database, you can use these credentials:

**Admin Account:**
- Email: `<EMAIL>`
- Password: `admin123`

**Salesman Account:**
- Email: `<EMAIL>`
- Password: `salesman123`

### **4.2 Test Authentication**
1. Open http://localhost:5177
2. Login with admin credentials
3. Verify you can access all features

---

## **🧪 STEP 5: TESTING THE SYSTEM**

### **5.1 API Health Check**
```bash
curl http://localhost:3001/health
```

### **5.2 Test API Endpoints**
```bash
# Login and get token
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'

# Use token to access protected endpoint
curl -X GET http://localhost:3001/api/products \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### **5.3 Frontend Testing Workflow**
1. **Login as Admin**
2. **Add Products** via Inventory In
3. **Add Customers** via Customers page
4. **Create Orders** via Sales Orders
5. **Verify Data Synchronization** across all pages

---

## **📁 STEP 6: PROJECT STRUCTURE**

```
inventory-management-system/
├── backend/                    # Node.js/Express API
│   ├── config/                # Database configuration
│   ├── controllers/           # API controllers
│   ├── middleware/            # Authentication, validation, etc.
│   ├── models/                # Database models (Sequelize)
│   ├── routes/                # API routes
│   ├── scripts/               # Migration and seed scripts
│   ├── .env                   # Environment variables
│   ├── package.json           # Backend dependencies
│   └── server.js              # Main server file
├── src/                       # React frontend
│   ├── components/            # React components
│   ├── contexts/              # React contexts
│   ├── pages/                 # Page components
│   ├── services/              # API service layer
│   └── ...
├── .env                       # Frontend environment variables
├── package.json               # Frontend dependencies
└── README.md                  # Project documentation
```

---

## **🔄 STEP 7: DATA MIGRATION (FROM OLD SYSTEM)**

### **7.1 Switch to API-Based DataContext**

Replace the old DataContext with the new API-based version:

```jsx
// In your main App.jsx or index.jsx
import { ApiDataProvider } from './contexts/ApiDataContext';

// Wrap your app with ApiDataProvider instead of DataProvider
<ApiDataProvider>
  <App />
</ApiDataProvider>
```

### **7.2 Update Component Imports**
```jsx
// Change from:
import { useData } from './contexts/DataContext';

// To:
import { useApiData as useData } from './contexts/ApiDataContext';
```

---

## **🚀 STEP 8: PRODUCTION DEPLOYMENT**

### **8.1 Backend Deployment**
```bash
# Set production environment
export NODE_ENV=production

# Install PM2 for process management
npm install -g pm2

# Start with PM2
pm2 start server.js --name "inventory-api"

# Save PM2 configuration
pm2 save
pm2 startup
```

### **8.2 Frontend Deployment**
```bash
# Build for production
npm run build

# Serve with a static file server
npm install -g serve
serve -s dist -l 3000
```

---

## **📊 STEP 9: MONITORING & MAINTENANCE**

### **9.1 Health Monitoring**
- **Backend Health**: http://localhost:3001/health
- **Database Connection**: Check server logs
- **API Performance**: Monitor response times

### **9.2 Backup Strategy**
```bash
# Database backup
pg_dump -U inventory_user inventory_management > backup.sql

# Restore from backup
psql -U inventory_user inventory_management < backup.sql
```

---

## **🎯 EXPECTED RESULTS**

After completing this setup, you will have:

✅ **Persistent Data Storage** - All data saved to PostgreSQL database
✅ **Real-time Synchronization** - Changes immediately reflected across all components
✅ **User Authentication** - Secure login system with role-based access
✅ **RESTful API** - Professional API architecture
✅ **Production Ready** - Scalable, maintainable codebase
✅ **Data Integrity** - Proper relationships and constraints
✅ **Error Handling** - Comprehensive error management
✅ **Security** - JWT authentication, input validation, SQL injection protection

---

## **🆘 TROUBLESHOOTING**

### **Common Issues:**

**Database Connection Error:**
- Check PostgreSQL is running: `sudo service postgresql status`
- Verify credentials in `.env` file
- Test connection: `psql -U inventory_user -d inventory_management`

**Backend Won't Start:**
- Check port 3001 is available: `lsof -i :3001`
- Verify all environment variables are set
- Check server logs for specific errors

**Frontend API Errors:**
- Verify backend is running on port 3001
- Check CORS configuration
- Verify API base URL in frontend `.env`

**Authentication Issues:**
- Clear browser localStorage
- Verify JWT secret is set in backend `.env`
- Check token expiration settings

---

## **📞 SUPPORT**

If you encounter any issues during setup:
1. Check the troubleshooting section above
2. Review server and browser console logs
3. Verify all environment variables are correctly set
4. Ensure all services are running on correct ports

**Your full-stack inventory management system is now ready for production use! 🎉**
