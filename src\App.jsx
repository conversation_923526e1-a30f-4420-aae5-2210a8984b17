import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { DataProvider } from './contexts/DataContext';
import { ApiDataProvider } from './contexts/ApiDataContext';

// Import components
import Layout from './components/Layout';
import Login from './pages/Login';
import Signup from './pages/Signup';
import VerifyEmail from './pages/VerifyEmail';
import ForgotPassword from './pages/ForgotPassword';
import ResetPassword from './pages/ResetPassword';
import AdminDashboard from './pages/AdminDashboard';
import SalesmanDashboard from './pages/SalesmanDashboard';
import InventoryManagement from './pages/InventoryManagement';
import SalesOrders from './pages/SalesOrders';
import SalesCommissions from './pages/SalesCommissions';
import SalesCredits from './pages/SalesCredits';
import Expenses from './pages/Expenses';
import FinancesSalaries from './pages/FinancesSalaries';
import FinancesBilling from './pages/FinancesBilling';
import Customers from './pages/Customers';
import Reports from './pages/Reports';
import Notifications from './pages/Notifications';
import Settings from './pages/Settings';
import WorkflowTest from './pages/WorkflowTest';
import DatabaseMonitor from './components/DatabaseMonitor';
import SimpleMonitor from './components/SimpleMonitor';

export function App() {
  return (
    <Router>
      <AuthProvider>
        <DataProvider>
          <ApiDataProvider>
            <Routes>
              {/* Public Authentication Routes */}
              <Route path="/login" element={<Login />} />
              <Route path="/signup" element={<Signup />} />
              <Route path="/verify-email" element={<VerifyEmail />} />
              <Route path="/forgot-password" element={<ForgotPassword />} />
              <Route path="/reset-password" element={<ResetPassword />} />
              <Route path="/debug/monitor" element={<DatabaseMonitor />} />
              <Route path="/debug/simple" element={<SimpleMonitor />} />

              {/* Protected Application Routes */}
              <Route path="/" element={<Layout />}>
                {/* Default route - handled by Layout component */}
                <Route index element={<div />} />

                {/* Admin Routes */}
                <Route path="admin/dashboard" element={<AdminDashboard />} />
                <Route path="inventory/*" element={<InventoryManagement />} />
                <Route path="inventory/in" element={<InventoryManagement />} />
                <Route path="inventory/out" element={<InventoryManagement />} />
                <Route path="inventory/history" element={<InventoryManagement />} />
                <Route path="sales/orders" element={<SalesOrders />} />
                <Route path="sales/commissions" element={<SalesCommissions />} />
                <Route path="sales/credits" element={<SalesCredits />} />
                <Route path="finances/expenses" element={<Expenses />} />
                <Route path="finances/salaries" element={<FinancesSalaries />} />
                <Route path="finances/billing" element={<FinancesBilling />} />
                <Route path="customers" element={<Customers />} />
                <Route path="reports" element={<Reports />} />
                <Route path="notifications" element={<Notifications />} />
                <Route path="settings" element={<Settings />} />
                <Route path="workflow-test" element={<WorkflowTest />} />
                <Route path="monitor/database" element={<DatabaseMonitor />} />

                {/* Salesman Routes */}
                <Route path="salesman/dashboard" element={<SalesmanDashboard />} />
              </Route>
            </Routes>
          </ApiDataProvider>
        </DataProvider>
      </AuthProvider>
    </Router>
  );
}
