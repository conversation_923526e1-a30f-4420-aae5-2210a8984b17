const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const SalaryHistory = sequelize.define('SalaryHistory', {
  history_id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  employee_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'employees',
      key: 'employee_id'
    }
  },
  assignment_id: {
    type: DataTypes.UUID,
    references: {
      model: 'salary_assignments',
      key: 'assignment_id'
    }
  },
  payment_id: {
    type: DataTypes.UUID,
    references: {
      model: 'salary_payments',
      key: 'payment_id'
    }
  },
  action_type: {
    type: DataTypes.ENUM(
      'salary_assigned',
      'salary_updated',
      'salary_revised',
      'payment_processed',
      'payment_failed',
      'bonus_added',
      'deduction_added',
      'status_changed',
      'employee_promoted',
      'employee_terminated'
    ),
    allowNull: false
  },
  previous_values: {
    type: DataTypes.JSONB,
    defaultValue: {}
  },
  new_values: {
    type: DataTypes.JSONB,
    defaultValue: {}
  },
  change_reason: {
    type: DataTypes.STRING
  },
  effective_date: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  amount_involved: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0
  },
  description: {
    type: DataTypes.TEXT
  },
  created_by: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'user_id'
    }
  },
  approved_by: {
    type: DataTypes.UUID,
    references: {
      model: 'users',
      key: 'user_id'
    }
  },
  approval_date: {
    type: DataTypes.DATE
  }
}, {
  tableName: 'salary_history',
  indexes: [
    {
      fields: ['employee_id']
    },
    {
      fields: ['action_type']
    },
    {
      fields: ['effective_date']
    },
    {
      fields: ['created_at']
    }
  ]
});

module.exports = SalaryHistory;
