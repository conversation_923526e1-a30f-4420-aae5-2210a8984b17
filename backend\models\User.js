const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');
const bcrypt = require('bcryptjs');

const User = sequelize.define('User', {
  user_id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  email: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    validate: {
      isEmail: true
    }
  },
  password_hash: {
    type: DataTypes.STRING,
    allowNull: false
  },
  role: {
    type: DataTypes.ENUM('admin', 'salesman'),
    allowNull: false,
    defaultValue: 'salesman'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  last_login: {
    type: DataTypes.DATE
  },
  email_verified: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  email_verification_token: {
    type: DataTypes.STRING
  },
  email_verification_expires: {
    type: DataTypes.DATE
  },
  password_reset_token: {
    type: DataTypes.STRING
  },
  password_reset_expires: {
    type: DataTypes.DATE
  },
  failed_login_attempts: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  account_locked_until: {
    type: DataTypes.DATE
  }
}, {
  tableName: 'users',
  hooks: {
    beforeCreate: async (user) => {
      if (user.password_hash) {
        user.password_hash = await bcrypt.hash(user.password_hash, 12);
      }
    },
    beforeUpdate: async (user) => {
      if (user.changed('password_hash')) {
        user.password_hash = await bcrypt.hash(user.password_hash, 12);
      }
    }
  },
  indexes: [
    {
      fields: ['email']
    },
    {
      fields: ['email_verification_token']
    },
    {
      fields: ['password_reset_token']
    }
  ]
});

// Instance methods
User.prototype.validatePassword = async function(password) {
  return await bcrypt.compare(password, this.password_hash);
};

User.prototype.generateEmailVerificationToken = function() {
  const crypto = require('crypto');
  const token = crypto.randomBytes(32).toString('hex');
  this.email_verification_token = token;
  this.email_verification_expires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
  return token;
};

User.prototype.generatePasswordResetToken = function() {
  const crypto = require('crypto');
  const token = crypto.randomBytes(32).toString('hex');
  this.password_reset_token = token;
  this.password_reset_expires = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes
  return token;
};

User.prototype.isAccountLocked = function() {
  return this.account_locked_until && this.account_locked_until > new Date();
};

User.prototype.incrementFailedLogins = async function() {
  this.failed_login_attempts += 1;

  // Lock account after more attempts in development (20 vs 5) for shorter time (5 vs 30 minutes)
  const maxAttempts = process.env.NODE_ENV === 'development' ? 20 : 5;
  const lockDuration = process.env.NODE_ENV === 'development' ? 5 * 60 * 1000 : 30 * 60 * 1000;

  if (this.failed_login_attempts >= maxAttempts) {
    this.account_locked_until = new Date(Date.now() + lockDuration);
  }

  await this.save();
};

User.prototype.resetFailedLogins = async function() {
  this.failed_login_attempts = 0;
  this.account_locked_until = null;
  await this.save();
};

User.prototype.toJSON = function() {
  const values = Object.assign({}, this.get());
  delete values.password_hash;
  delete values.email_verification_token;
  delete values.password_reset_token;
  delete values.failed_login_attempts;
  delete values.account_locked_until;
  return values;
};

module.exports = User;
