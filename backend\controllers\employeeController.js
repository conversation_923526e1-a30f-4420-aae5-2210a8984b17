const { Employee, <PERSON>aryAssignment, SalaryPayment, SalaryHistory, User } = require('../models');
const { Op } = require('sequelize');
const chalk = require('chalk');
const moment = require('moment');

// Get all employees with pagination and filtering
const getEmployees = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 50,
      search = '',
      status = '',
      department = '',
      position = '',
      sortBy = 'created_at',
      sortOrder = 'DESC'
    } = req.query;

    console.log(chalk.blue(`👥 Fetching employees - Page: ${page}, Limit: ${limit}`));

    const offset = (page - 1) * limit;
    const whereClause = {};

    // Add search filters
    if (search) {
      whereClause[Op.or] = [
        { full_name: { [Op.iLike]: `%${search}%` } },
        { email: { [Op.iLike]: `%${search}%` } },
        { employee_number: { [Op.iLike]: `%${search}%` } }
      ];
    }

    if (status) whereClause.status = status;
    if (department) whereClause.department = { [Op.iLike]: `%${department}%` };
    if (position) whereClause.position = { [Op.iLike]: `%${position}%` };

    const { count, rows: employees } = await Employee.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: SalaryAssignment,
          as: 'salaryAssignments',
          where: { is_active: true },
          required: false,
          limit: 1,
          order: [['effective_date', 'DESC']]
        },
        {
          model: Employee,
          as: 'manager',
          attributes: ['employee_id', 'full_name', 'position']
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [[sortBy, sortOrder.toUpperCase()]],
      distinct: true
    });

    console.log(`✅ Found ${employees.length} employees (${count} total)`);

    // Log data change for monitoring
    if (global.logDataChange) {
      global.logDataChange('READ', 'employees', { count: employees.length }, req.user?.user_id);
    }

    res.json({
      success: true,
      data: {
        employees,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / limit),
          totalItems: count,
          itemsPerPage: parseInt(limit)
        }
      }
    });
  } catch (error) {
    console.error('❌ Error fetching employees:', error);
    next(error);
  }
};

// Get single employee by ID
const getEmployee = async (req, res, next) => {
  try {
    const { id } = req.params;
    console.log(chalk.blue(`👤 Fetching employee: ${id}`));

    const employee = await Employee.findByPk(id, {
      include: [
        {
          model: SalaryAssignment,
          as: 'salaryAssignments',
          include: [
            {
              model: User,
              as: 'creator',
              attributes: ['user_id', 'email']
            }
          ]
        },
        {
          model: SalaryPayment,
          as: 'salaryPayments',
          limit: 10,
          order: [['payment_date', 'DESC']]
        },
        {
          model: Employee,
          as: 'manager',
          attributes: ['employee_id', 'full_name', 'position']
        },
        {
          model: Employee,
          as: 'subordinates',
          attributes: ['employee_id', 'full_name', 'position']
        }
      ]
    });

    if (!employee) {
      return res.status(404).json({
        success: false,
        message: 'Employee not found'
      });
    }

    console.log(`✅ Found employee: ${employee.full_name}`);

    // Log data change for monitoring
    if (global.logDataChange) {
      global.logDataChange('READ', 'employees', employee, req.user?.user_id);
    }

    res.json({
      success: true,
      data: employee
    });
  } catch (error) {
    console.error('❌ Error fetching employee:', error);
    next(error);
  }
};

// Create new employee
const createEmployee = async (req, res, next) => {
  try {
    const {
      full_name,
      email,
      phone,
      position,
      department,
      hire_date,
      employee_type = 'full_time',
      manager_id,
      address = {},
      emergency_contact = {},
      bank_details = {},
      notes
    } = req.body;

    console.log(chalk.green(`👤 Creating new employee: ${full_name}`));

    // Check if email already exists
    const existingEmployee = await Employee.findOne({ where: { email } });
    if (existingEmployee) {
      return res.status(400).json({
        success: false,
        message: 'Employee with this email already exists'
      });
    }

    // CRITICAL FIX: Generate employee number manually if not provided
    let employee_number = req.body.employee_number;
    if (!employee_number) {
      try {
        const year = new Date().getFullYear();
        const count = await Employee.count({
          where: {
            employee_number: {
              [Op.like]: `EMP-${year}-%`
            }
          }
        });
        employee_number = `EMP-${year}-${String(count + 1).padStart(4, '0')}`;
        console.log(`✅ Generated employee number: ${employee_number}`);
      } catch (error) {
        console.error('❌ Error generating employee number:', error);
        // Fallback to timestamp-based number
        const timestamp = Date.now().toString().slice(-4);
        employee_number = `EMP-${new Date().getFullYear()}-${timestamp}`;
        console.log(`⚠️ Using fallback employee number: ${employee_number}`);
      }
    }

    const employee = await Employee.create({
      employee_number,
      full_name,
      email,
      phone,
      position,
      department,
      hire_date: hire_date || new Date(),
      employee_type,
      manager_id,
      address,
      emergency_contact,
      bank_details,
      notes,
      created_by: req.user.user_id
    });

    // Create salary history entry
    await SalaryHistory.create({
      employee_id: employee.employee_id,
      action_type: 'salary_assigned',
      description: `Employee ${employee.full_name} created`,
      created_by: req.user.user_id
    });

    console.log(`✅ Employee created: ${employee.employee_number}`);

    // Log data change for monitoring
    if (global.logDataChange) {
      global.logDataChange('CREATE', 'employees', employee, req.user?.user_id);
    }

    res.status(201).json({
      success: true,
      data: employee,
      message: 'Employee created successfully'
    });
  } catch (error) {
    console.error('❌ Error creating employee:', error);
    next(error);
  }
};

// Update employee
const updateEmployee = async (req, res, next) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    console.log(chalk.yellow(`👤 Updating employee: ${id}`));

    const employee = await Employee.findByPk(id);
    if (!employee) {
      return res.status(404).json({
        success: false,
        message: 'Employee not found'
      });
    }

    // Store previous values for history
    const previousValues = {
      full_name: employee.full_name,
      email: employee.email,
      position: employee.position,
      department: employee.department,
      status: employee.status
    };

    // Update employee
    updateData.updated_by = req.user.user_id;
    await employee.update(updateData);

    // Create history entry
    await SalaryHistory.create({
      employee_id: employee.employee_id,
      action_type: 'salary_updated',
      previous_values: previousValues,
      new_values: updateData,
      description: `Employee ${employee.full_name} updated`,
      created_by: req.user.user_id
    });

    console.log(`✅ Employee updated: ${employee.full_name}`);

    // Log data change for monitoring
    if (global.logDataChange) {
      global.logDataChange('UPDATE', 'employees', employee, req.user?.user_id);
    }

    res.json({
      success: true,
      data: employee,
      message: 'Employee updated successfully'
    });
  } catch (error) {
    console.error('❌ Error updating employee:', error);
    next(error);
  }
};

// Delete employee (soft delete)
const deleteEmployee = async (req, res, next) => {
  try {
    const { id } = req.params;
    console.log(chalk.red(`👤 Deleting employee: ${id}`));

    const employee = await Employee.findByPk(id);
    if (!employee) {
      return res.status(404).json({
        success: false,
        message: 'Employee not found'
      });
    }

    // Soft delete by updating status
    await employee.update({
      status: 'terminated',
      termination_date: new Date(),
      updated_by: req.user.user_id
    });

    // Deactivate current salary assignments
    await SalaryAssignment.update(
      { is_active: false, end_date: new Date() },
      { where: { employee_id: id, is_active: true } }
    );

    // Create history entry
    await SalaryHistory.create({
      employee_id: employee.employee_id,
      action_type: 'employee_terminated',
      description: `Employee ${employee.full_name} terminated`,
      created_by: req.user.user_id
    });

    console.log(`✅ Employee terminated: ${employee.full_name}`);

    // Log data change for monitoring
    if (global.logDataChange) {
      global.logDataChange('DELETE', 'employees', employee, req.user?.user_id);
    }

    res.json({
      success: true,
      message: 'Employee terminated successfully'
    });
  } catch (error) {
    console.error('❌ Error deleting employee:', error);
    next(error);
  }
};

// Get employee departments
const getDepartments = async (req, res, next) => {
  try {
    console.log(chalk.blue('🏢 Fetching departments'));

    const departments = await Employee.findAll({
      attributes: ['department'],
      group: ['department'],
      where: {
        status: 'active',
        department: { [Op.ne]: null }
      }
    });

    const departmentList = departments.map(d => d.department).filter(Boolean);

    res.json({
      success: true,
      data: departmentList
    });
  } catch (error) {
    console.error('❌ Error fetching departments:', error);
    next(error);
  }
};

// Get employee positions
const getPositions = async (req, res, next) => {
  try {
    console.log(chalk.blue('💼 Fetching positions'));

    const positions = await Employee.findAll({
      attributes: ['position'],
      group: ['position'],
      where: {
        status: 'active',
        position: { [Op.ne]: null }
      }
    });

    const positionList = positions.map(p => p.position).filter(Boolean);

    res.json({
      success: true,
      data: positionList
    });
  } catch (error) {
    console.error('❌ Error fetching positions:', error);
    next(error);
  }
};

module.exports = {
  getEmployees,
  getEmployee,
  createEmployee,
  updateEmployee,
  deleteEmployee,
  getDepartments,
  getPositions
};
