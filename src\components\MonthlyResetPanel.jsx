import React, { useState, useEffect } from 'react';
import { 
  RefreshCwIcon, 
  AlertTriangleIcon, 
  CheckCircleIcon,
  CalendarIcon,
  ArchiveIcon,
  ClockIcon
} from 'lucide-react';

const MonthlyResetPanel = ({ user }) => {
  const [resetStatus, setResetStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);

  // Fetch reset status
  const fetchResetStatus = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('auth_token');
      const response = await fetch(
        'http://localhost:3001/api/commissions/reset-status',
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setResetStatus(result.data);
        }
      }
    } catch (error) {
      console.error('Error fetching reset status:', error);
    } finally {
      setLoading(false);
    }
  };

  // Process monthly reset
  const processMonthlyReset = async () => {
    if (!window.confirm('Are you sure you want to process the monthly commission reset? This will archive current data and cannot be undone.')) {
      return;
    }

    try {
      setProcessing(true);
      const token = localStorage.getItem('auth_token');
      const response = await fetch(
        'http://localhost:3001/api/commissions/monthly-reset',
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({})
        }
      );

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          alert('Monthly commission reset completed successfully!');
          fetchResetStatus(); // Refresh status
        } else {
          alert('Failed to process monthly reset: ' + result.message);
        }
      } else {
        alert('Failed to process monthly reset. Please try again.');
      }
    } catch (error) {
      console.error('Error processing monthly reset:', error);
      alert('Error processing monthly reset. Please try again.');
    } finally {
      setProcessing(false);
    }
  };

  useEffect(() => {
    if (user?.role === 'admin') {
      fetchResetStatus();
      
      // Check status every 5 minutes
      const interval = setInterval(fetchResetStatus, 5 * 60 * 1000);
      return () => clearInterval(interval);
    }
  }, [user]);

  // Don't show panel if not admin
  if (user?.role !== 'admin') {
    return null;
  }

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">Checking reset status...</span>
        </div>
      </div>
    );
  }

  if (!resetStatus) {
    return null;
  }

  const currentMonthName = new Date(resetStatus.current_year, resetStatus.current_month - 1).toLocaleString('default', { month: 'long', year: 'numeric' });
  const lastMonthName = new Date(resetStatus.current_year, resetStatus.current_month - 2).toLocaleString('default', { month: 'long', year: 'numeric' });

  return (
    <div className="bg-white rounded-lg shadow-sm border-l-4 border-blue-500 p-6 mb-6">
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            {resetStatus.reset_needed ? (
              <AlertTriangleIcon size={24} className="text-orange-600" />
            ) : (
              <CheckCircleIcon size={24} className="text-green-600" />
            )}
          </div>
          
          <div className="flex-1">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <CalendarIcon size={20} className="mr-2" />
              Monthly Commission Reset
            </h3>
            
            <div className="mt-2 space-y-2">
              <div className="flex items-center text-sm text-gray-600">
                <ClockIcon size={16} className="mr-2" />
                Current Period: {currentMonthName}
              </div>
              
              <div className="grid grid-cols-2 gap-4 mt-3">
                <div className="bg-gray-50 rounded-lg p-3">
                  <div className="text-xs font-medium text-gray-500 uppercase">Current Month</div>
                  <div className="text-lg font-semibold text-gray-900">
                    {resetStatus.current_month_assignments}
                  </div>
                  <div className="text-xs text-gray-500">Commission assignments</div>
                </div>
                
                <div className="bg-gray-50 rounded-lg p-3">
                  <div className="text-xs font-medium text-gray-500 uppercase">Last Month</div>
                  <div className="text-lg font-semibold text-gray-900">
                    {resetStatus.last_month_assignments}
                  </div>
                  <div className="text-xs text-gray-500">
                    {resetStatus.last_month_archived > 0 ? 'Archived' : 'Pending archive'}
                  </div>
                </div>
              </div>
              
              {resetStatus.reset_needed ? (
                <div className="mt-4 p-3 bg-orange-50 border border-orange-200 rounded-md">
                  <div className="flex items-center">
                    <AlertTriangleIcon size={16} className="text-orange-600 mr-2" />
                    <span className="text-sm font-medium text-orange-800">
                      Monthly reset required
                    </span>
                  </div>
                  <p className="text-sm text-orange-700 mt-1">
                    {lastMonthName} commission data needs to be archived before continuing with the new month.
                  </p>
                </div>
              ) : (
                <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
                  <div className="flex items-center">
                    <CheckCircleIcon size={16} className="text-green-600 mr-2" />
                    <span className="text-sm font-medium text-green-800">
                      System up to date
                    </span>
                  </div>
                  <p className="text-sm text-green-700 mt-1">
                    All commission data is properly archived and current.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          <button
            onClick={fetchResetStatus}
            disabled={loading}
            className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            title="Refresh status"
          >
            <RefreshCwIcon size={18} className={loading ? 'animate-spin' : ''} />
          </button>
          
          {resetStatus.reset_needed && (
            <button
              onClick={processMonthlyReset}
              disabled={processing}
              className="flex items-center px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 disabled:opacity-50"
            >
              {processing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Processing...
                </>
              ) : (
                <>
                  <ArchiveIcon size={16} className="mr-2" />
                  Process Reset
                </>
              )}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default MonthlyResetPanel;
