// Initialize default commission stages
import fetch from 'node-fetch';

const API_BASE_URL = 'http://localhost:3001/api';

async function initializeCommissionStages() {
  try {
    console.log('🔐 Authenticating...');
    
    // Step 1: Authenticate as admin
    const authResponse = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Admin123!'
      })
    });

    const authResult = await authResponse.json();
    
    if (!authResult.success) {
      console.error('❌ Authentication failed:', authResult.message);
      return;
    }

    const token = authResult.data.token;
    console.log('✅ Authentication successful');

    // Step 2: Check existing commission stages first
    console.log('📊 Checking existing commission stages...');

    const stagesResponse = await fetch(`${API_BASE_URL}/commissions/stages`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const stagesResult = await stagesResponse.json();
    const existingStages = stagesResult.success ? stagesResult.data : [];

    console.log(`Found ${existingStages.length} existing commission stages`);

    // Define all desired stages
    const allDesiredStages = [
      {
        stage_number: 1,
        stage_name: 'Bronze',
        min_sales_amount: 0,
        max_sales_amount: 10000,
        commission_percentage: 0.02, // 2%
        bonus_amount: 100,
        calculation_period: 'monthly',
        eligible_categories: [],
        notes: 'Entry level commission tier'
      },
      {
        stage_number: 2,
        stage_name: 'Silver',
        min_sales_amount: 10001,
        max_sales_amount: 25000,
        commission_percentage: 0.03, // 3%
        bonus_amount: 250,
        calculation_period: 'monthly',
        eligible_categories: [],
        notes: 'Mid-tier commission with increased rate'
      },
      {
        stage_number: 3,
        stage_name: 'Gold',
        min_sales_amount: 25001,
        max_sales_amount: 50000,
        commission_percentage: 0.04, // 4%
        bonus_amount: 500,
        calculation_period: 'monthly',
        eligible_categories: [],
        notes: 'High-performance commission tier'
      },
      {
        stage_number: 4,
        stage_name: 'Platinum',
        min_sales_amount: 50001,
        max_sales_amount: 999999, // Very high limit instead of null
        commission_percentage: 0.05, // 5%
        bonus_amount: 1000,
        calculation_period: 'monthly',
        eligible_categories: [],
        notes: 'Premium commission tier for top performers'
      }
    ];

    // Filter out stages that already exist
    const existingStageNumbers = existingStages.map(s => s.stage_number);
    const missingStages = allDesiredStages.filter(s => !existingStageNumbers.includes(s.stage_number));

    if (missingStages.length === 0) {
      console.log('✅ All commission stages already exist!');
      console.log('📊 Existing stages:');
      existingStages.forEach(stage => {
        console.log(`   - ${stage.stage_name} (Stage ${stage.stage_number}): ${(stage.commission_percentage * 100).toFixed(1)}% commission, $${stage.bonus_amount} bonus`);
      });
      return;
    }

    console.log(`📊 Creating ${missingStages.length} missing commission stages...`);

    const defaultStages = {
      stages: missingStages
    };

    const createResponse = await fetch(`${API_BASE_URL}/commissions/stages`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(defaultStages)
    });

    console.log('Create response status:', createResponse.status);
    const createResult = await createResponse.json();
    console.log('Create result:', createResult);

    if (createResult.success) {
      console.log('✅ Commission stages created successfully!');
      console.log('📊 Created stages:');
      createResult.data.forEach(stage => {
        console.log(`   - ${stage.stage_name} (Stage ${stage.stage_number}): ${(stage.commission_percentage * 100).toFixed(1)}% commission, $${stage.bonus_amount} bonus`);
      });
    } else {
      console.error('❌ Failed to create commission stages:', createResult.message);
      if (createResult.errors) {
        console.error('Validation errors:', createResult.errors);
      }
    }

  } catch (error) {
    console.error('❌ Error initializing commission stages:', error.message);
  }
}

// Run the initialization
initializeCommissionStages();
