const express = require('express');
const router = express.Router();
const { Expense } = require('../models');
const { authenticateToken, adminOr<PERSON><PERSON><PERSON> } = require('../middleware/auth');
const { validateUUID } = require('../middleware/validation');
const chalk = require('chalk');
const moment = require('moment');

// All routes require authentication
router.use(authenticateToken);

// Get all expenses
router.get('/', adminOr<PERSON><PERSON><PERSON>, async (req, res) => {
  try {
    console.log(chalk.blue(`\n💰 [${moment().format('YYYY-MM-DD HH:mm:ss.SSS')}] FETCHING ALL EXPENSES`));

    const expenses = await Expense.findAll({
      order: [['expense_date', 'DESC'], ['created_at', 'DESC']]
    });

    console.log(`✅ Found ${expenses.length} expenses`);

    // Transform data for frontend compatibility
    const transformedExpenses = expenses.map(expense => ({
      id: expense.expense_id,
      category: expense.category,
      description: expense.description,
      amount: parseFloat(expense.amount),
      expenseDate: expense.expense_date?.split('T')[0] || expense.created_at?.split('T')[0],
      paymentMethod: expense.payment_method,
      vendor: expense.vendor,
      receiptNumber: expense.receipt_number,
      notes: expense.notes,
      status: expense.status || 'completed',
      createdAt: expense.created_at,
      updatedAt: expense.updated_at
    }));

    // Log data change for monitoring
    if (global.logDataChange) {
      global.logDataChange('READ', 'expenses', { count: expenses.length }, req.user?.user_id);
    }

    res.json({
      success: true,
      data: transformedExpenses,
      count: expenses.length
    });
  } catch (error) {
    console.error('❌ Error fetching expenses:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch expenses',
      error: error.message
    });
  }
});

// Get expense by ID
router.get('/:id', adminOrSalesman, validateUUID('id'), async (req, res) => {
  try {
    const { id } = req.params;
    console.log(chalk.blue(`💰 Fetching expense with ID: ${id}`));

    const expense = await Expense.findByPk(id);

    if (!expense) {
      return res.status(404).json({
        success: false,
        message: 'Expense not found'
      });
    }

    console.log(`✅ Found expense: ${expense.expense_id}`);

    // Transform data for frontend compatibility
    const transformedExpense = {
      id: expense.expense_id,
      category: expense.category,
      description: expense.description,
      amount: parseFloat(expense.amount),
      expenseDate: expense.expense_date?.split('T')[0] || expense.created_at?.split('T')[0],
      paymentMethod: expense.payment_method,
      vendor: expense.vendor,
      receiptNumber: expense.receipt_number,
      notes: expense.notes,
      status: expense.status || 'completed',
      createdAt: expense.created_at,
      updatedAt: expense.updated_at
    };

    // Log data change for monitoring
    if (global.logDataChange) {
      global.logDataChange('READ', 'expenses', transformedExpense, req.user?.user_id);
    }

    res.json({
      success: true,
      data: transformedExpense
    });
  } catch (error) {
    console.error('❌ Error fetching expense:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch expense',
      error: error.message
    });
  }
});

// Create new expense
router.post('/', adminOrSalesman, async (req, res) => {
  try {
    const {
      category,
      description,
      amount,
      expense_date,
      payment_method,
      vendor,
      receipt_number,
      notes
    } = req.body;

    console.log(chalk.green(`💰 Creating new expense: ${description}`));

    // Validate required fields
    if (!category || !description || !amount) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: category, description, amount'
      });
    }

    // Create expense
    const expense = await Expense.create({
      category,
      description,
      amount: parseFloat(amount),
      expense_date: expense_date || new Date(),
      payment_method: payment_method || 'cash',
      vendor: vendor || null,
      receipt_number: receipt_number || null,
      notes: notes || null,
      status: 'completed',
      recorded_by: req.user.user_id
    });

    console.log(`✅ Expense created successfully: ${expense.expense_id}`);

    // Log data change for monitoring
    if (global.logDataChange) {
      global.logDataChange('CREATE', 'expenses', expense, req.user?.user_id);
    }

    res.status(201).json({
      success: true,
      data: {
        id: expense.expense_id,
        category: expense.category,
        description: expense.description,
        amount: parseFloat(expense.amount),
        expenseDate: expense.expense_date?.split('T')[0],
        paymentMethod: expense.payment_method,
        vendor: expense.vendor,
        receiptNumber: expense.receipt_number,
        notes: expense.notes,
        status: expense.status
      },
      message: 'Expense created successfully'
    });
  } catch (error) {
    console.error('❌ Error creating expense:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create expense',
      error: error.message
    });
  }
});

module.exports = router;
