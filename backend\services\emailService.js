const nodemailer = require('nodemailer');

class EmailService {
  constructor() {
    // For development, we'll use a mock email service
    // In production, configure with real SMTP settings
    this.transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST || 'smtp.ethereal.email',
      port: process.env.SMTP_PORT || 587,
      secure: false,
      auth: {
        user: process.env.SMTP_USER || '<EMAIL>',
        pass: process.env.SMTP_PASS || 'ethereal.pass'
      }
    });

    // For development, we'll just log emails to console
    this.isDevelopment = process.env.NODE_ENV === 'development';
  }

  async sendEmail(to, subject, html, text) {
    try {
      if (this.isDevelopment) {
        // In development, just log the email content
        console.log('\n📧 EMAIL SENT (Development Mode)');
        console.log('To:', to);
        console.log('Subject:', subject);
        console.log('Content:', text || html);
        console.log('---\n');
        return { success: true, messageId: 'dev-' + Date.now() };
      }

      const mailOptions = {
        from: process.env.FROM_EMAIL || '<EMAIL>',
        to,
        subject,
        html,
        text
      };

      const result = await this.transporter.sendMail(mailOptions);
      return { success: true, messageId: result.messageId };
    } catch (error) {
      console.error('Email sending failed:', error);
      return { success: false, error: error.message };
    }
  }

  async sendEmailVerification(user, token) {
    const verificationUrl = `${process.env.FRONTEND_URL}/verify-email?token=${token}`;

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Verify Your Email</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #3B82F6; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .button { display: inline-block; background: #3B82F6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>ZIDAN Enterprises</h1>
            <h2>Email Verification</h2>
          </div>
          <div class="content">
            <p>Hello,</p>
            <p>Thank you for registering with ZIDAN Enterprises Inventory Management System. Please verify your email address by clicking the button below:</p>
            <p style="text-align: center;">
              <a href="${verificationUrl}" class="button">Verify Email Address</a>
            </p>
            <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #3B82F6;">${verificationUrl}</p>
            <p><strong>This link will expire in 24 hours.</strong></p>
            <p>If you didn't create an account with us, please ignore this email.</p>
          </div>
          <div class="footer">
            <p>© 2024 ZIDAN Enterprises. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
      ZIDAN Enterprises - Email Verification

      Hello,

      Thank you for registering with ZIDAN Enterprises Inventory Management System.
      Please verify your email address by visiting this link:

      ${verificationUrl}

      This link will expire in 24 hours.

      If you didn't create an account with us, please ignore this email.

      © 2024 ZIDAN Enterprises. All rights reserved.
    `;

    return await this.sendEmail(
      user.email,
      'Verify Your Email - ZIDAN Enterprises',
      html,
      text
    );
  }

  async sendPasswordReset(user, token) {
    const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${token}`;

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Reset Your Password</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #DC2626; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .button { display: inline-block; background: #DC2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
          .warning { background: #FEF2F2; border-left: 4px solid #DC2626; padding: 10px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>ZIDAN Enterprises</h1>
            <h2>Password Reset Request</h2>
          </div>
          <div class="content">
            <p>Hello,</p>
            <p>We received a request to reset your password for your ZIDAN Enterprises account. Click the button below to reset your password:</p>
            <p style="text-align: center;">
              <a href="${resetUrl}" class="button">Reset Password</a>
            </p>
            <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #DC2626;">${resetUrl}</p>
            <div class="warning">
              <p><strong>⚠️ Important Security Information:</strong></p>
              <ul>
                <li>This link will expire in 30 minutes</li>
                <li>If you didn't request this password reset, please ignore this email</li>
                <li>Never share this link with anyone</li>
              </ul>
            </div>
          </div>
          <div class="footer">
            <p>© 2024 ZIDAN Enterprises. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
      ZIDAN Enterprises - Password Reset Request

      Hello,

      We received a request to reset your password for your ZIDAN Enterprises account.
      Click this link to reset your password:

      ${resetUrl}

      IMPORTANT SECURITY INFORMATION:
      - This link will expire in 30 minutes
      - If you didn't request this password reset, please ignore this email
      - Never share this link with anyone

      © 2024 ZIDAN Enterprises. All rights reserved.
    `;

    return await this.sendEmail(
      user.email,
      'Reset Your Password - ZIDAN Enterprises',
      html,
      text
    );
  }

  async sendWelcomeEmail(user) {
    const loginUrl = `${process.env.FRONTEND_URL}/login`;

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Welcome to ZIDAN Enterprises</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #10B981; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .button { display: inline-block; background: #10B981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎉 Welcome to ZIDAN Enterprises!</h1>
          </div>
          <div class="content">
            <p>Hello ${user.full_name || user.email},</p>
            <p>Welcome to ZIDAN Enterprises Inventory Management System! Your account has been successfully verified and is now active.</p>
            <p>You can now access all the features of our inventory management system:</p>
            <ul>
              <li>📦 Product Management</li>
              <li>👥 Customer Management</li>
              <li>📋 Order Processing</li>
              <li>💰 Financial Tracking</li>
              <li>📊 Analytics & Reports</li>
            </ul>
            <p style="text-align: center;">
              <a href="${loginUrl}" class="button">Login to Your Account</a>
            </p>
            <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
          </div>
          <div class="footer">
            <p>© 2024 ZIDAN Enterprises. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
      Welcome to ZIDAN Enterprises!

      Hello ${user.full_name || user.email},

      Welcome to ZIDAN Enterprises Inventory Management System! Your account has been successfully verified and is now active.

      You can now access all the features of our inventory management system:
      - Product Management
      - Customer Management
      - Order Processing
      - Financial Tracking
      - Analytics & Reports

      Login to your account: ${loginUrl}

      If you have any questions or need assistance, please don't hesitate to contact our support team.

      © 2024 ZIDAN Enterprises. All rights reserved.
    `;

    return await this.sendEmail(
      user.email,
      'Welcome to ZIDAN Enterprises! 🎉',
      html,
      text
    );
  }
}

module.exports = new EmailService();
