const { Product, Supplier, Category, InventoryTransaction, sequelize } = require('../models');
const { Op } = require('sequelize');

// Get all products
const getProducts = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 50,
      category,
      search,
      low_stock,
      active_only = 'true'
    } = req.query;

    const offset = (page - 1) * limit;
    const where = {};

    // Filter by active status
    if (active_only === 'true') {
      where.is_active = true;
    }

    // Filter by category
    if (category) {
      where.category = category;
    }

    // Search by name
    if (search) {
      where.name = {
        [Op.iLike]: `%${search}%`
      };
    }

    // Filter low stock products
    if (low_stock === 'true') {
      where[Op.and] = [
        { current_stock: { [Op.lte]: sequelize.col('min_stock_level') } }
      ];
    }

    const { count, rows: products } = await Product.findAndCountAll({
      where,
      include: [
        {
          model: Supplier,
          as: 'supplier',
          attributes: ['supplier_id', 'name']
        },
        {
          model: Category,
          as: 'categoryInfo',
          attributes: ['category_id', 'name', 'description']
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['name', 'ASC']]
    });

    res.json({
      success: true,
      data: {
        products,
        pagination: {
          current_page: parseInt(page),
          total_pages: Math.ceil(count / limit),
          total_items: count,
          items_per_page: parseInt(limit)
        }
      }
    });
  } catch (error) {
    next(error);
  }
};

// Get single product
const getProduct = async (req, res, next) => {
  try {
    const { id } = req.params;

    const product = await Product.findByPk(id, {
      include: [
        {
          model: Supplier,
          as: 'supplier'
        },
        {
          model: Category,
          as: 'categoryInfo',
          attributes: ['category_id', 'name', 'description']
        },
        {
          model: InventoryTransaction,
          as: 'transactions',
          limit: 10,
          order: [['transaction_date', 'DESC']]
        }
      ]
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    res.json({
      success: true,
      data: { product }
    });
  } catch (error) {
    next(error);
  }
};

// Create new product
const createProduct = async (req, res, next) => {
  try {
    console.log('🔍 DEBUG: Full request body:', JSON.stringify(req.body, null, 2));
    console.log('🔍 DEBUG: Request body keys:', Object.keys(req.body));

    const {
      name,
      category,
      sku,
      barcode,
      description,
      unit_price,
      cost_price,
      current_stock = 0,
      min_stock_level = 10,
      max_stock_level = 1000,
      unit_of_measure = 'pieces',
      supplier_id,
      storage_location,
      batch_number,
      expiration_date,
      // Transaction details for inventory transaction
      invoice_number,
      reference_number,
      notes
    } = req.body;

    // Handle category - find or create category
    let categoryId = null;
    if (category && category.trim() !== '' && category.trim() !== 'Select category') {
      console.log('📂 Processing category:', category);

      // Try to find existing category - SQLite compatible with case-insensitive search
      let categoryRecord = await Category.findOne({
        where: sequelize.where(
          sequelize.fn('LOWER', sequelize.col('name')),
          sequelize.fn('LOWER', category.trim())
        )
      });

      // If category doesn't exist, create it
      if (!categoryRecord) {
        console.log('📂 Category not found, creating new:', category.trim());
        categoryRecord = await Category.create({
          name: category.trim(),
          is_active: true
        });
        console.log('✅ Category created:', categoryRecord);
      }

      categoryId = categoryRecord.category_id;
      console.log('✅ Category resolved:', categoryRecord.name, '(ID:', categoryId, ')');
    } else {
      // If no valid category provided, throw an error
      return res.status(400).json({
        success: false,
        message: 'A valid category is required. Please select or create a category.'
      });
    }

    console.log(`📝 Creating product with category_id: ${categoryId}`);

    const product = await Product.create({
      name,
      category: category.trim(), // Populate the category name for backward compatibility
      category_id: categoryId, // Use the resolved category_id for foreign key relationship
      sku,
      barcode,
      description,
      unit_price,
      cost_price,
      current_stock,
      min_stock_level,
      max_stock_level,
      unit_of_measure,
      supplier_id,
      storage_location,
      batch_number,
      expiration_date: expiration_date ? new Date(expiration_date) : null,
      last_restocked: current_stock > 0 ? new Date() : null
    });

    // Create initial inventory transaction if stock > 0
    if (current_stock > 0) {
      await InventoryTransaction.create({
        type: 'in',
        product_id: product.product_id,
        quantity: current_stock,
        unit_price: cost_price || unit_price,
        total_cost: (cost_price || unit_price) * current_stock,
        supplier_id,
        reference_number,
        invoice_number,
        notes: notes || 'Initial stock entry',
        batch_number,
        expiration_date: expiration_date ? new Date(expiration_date) : null,
        storage_location,
        recorded_by: req.user.user_id,
        transaction_date: new Date()
      });
    }

    // Get product with supplier and category info
    const createdProduct = await Product.findByPk(product.product_id, {
      include: [
        {
          model: Supplier,
          as: 'supplier',
          attributes: ['supplier_id', 'name']
        },
        {
          model: Category,
          as: 'categoryInfo',
          attributes: ['category_id', 'name', 'description']
        }
      ]
    });

    // Log data change
    if (global.logDataChange) {
      global.logDataChange('CREATE', 'products', createdProduct.toJSON(), req.user.user_id);
    }

    res.status(201).json({
      success: true,
      message: 'Product created successfully',
      data: { product: createdProduct }
    });
  } catch (error) {
    next(error);
  }
};

// Update product
const updateProduct = async (req, res, next) => {
  try {
    console.log('🔄 DEBUG: Update product request:', { id: req.params.id, body: req.body });

    const { id } = req.params;
    const {
      name,
      description,
      category,
      sku,
      barcode,
      unit_price,
      cost_price,
      current_stock,
      min_stock_level,
      max_stock_level,
      storage_location
    } = req.body;

    const product = await Product.findByPk(id);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    console.log('📦 Found product to update:', product.name);

    // Handle category - find or create if category name is provided
    let categoryId = product.category_id; // Keep existing category_id by default
    if (category && category.trim() !== '' && category.trim() !== 'Select category') {
      console.log('📂 Processing category update:', category);

      let categoryRecord = await Category.findOne({
        where: sequelize.where(
          sequelize.fn('LOWER', sequelize.col('name')),
          sequelize.fn('LOWER', category.trim())
        )
      });

      if (!categoryRecord) {
        console.log('📂 Creating new category:', category.trim());
        categoryRecord = await Category.create({
          name: category.trim(),
          is_active: true
        });
      }

      categoryId = categoryRecord.category_id;
      console.log('✅ Category resolved for update:', categoryRecord.name, '(ID:', categoryId, ')');
    }

    // Prepare update data
    const updateData = {
      name: name || product.name,
      description: description !== undefined ? description : product.description,
      category: category || product.category, // Keep both fields for compatibility
      category_id: categoryId,
      sku: sku !== undefined ? sku : product.sku,
      barcode: barcode !== undefined ? barcode : product.barcode,
      unit_price: unit_price !== undefined ? parseFloat(unit_price) : product.unit_price,
      cost_price: cost_price !== undefined ? parseFloat(cost_price) : product.cost_price,
      current_stock: current_stock !== undefined ? parseInt(current_stock) : product.current_stock,
      min_stock_level: min_stock_level !== undefined ? parseInt(min_stock_level) : product.min_stock_level,
      max_stock_level: max_stock_level !== undefined ? parseInt(max_stock_level) : product.max_stock_level,
      storage_location: storage_location !== undefined ? storage_location : product.storage_location
    };

    console.log('📝 Updating product with data:', updateData);

    // Check for stock changes before updating
    const oldStock = product.current_stock;
    const newStock = updateData.current_stock;
    const stockChanged = oldStock !== newStock;
    const stockDifference = newStock - oldStock;

    await product.update(updateData);

    // Note: Inventory transactions for stock changes should be created
    // through the dedicated inventory controller endpoints to avoid duplicates.
    // The frontend should call the inventory updateStock endpoint separately
    // if a transaction record is needed for stock adjustments.

    if (stockChanged && stockDifference !== 0) {
      console.log(`📦 Stock changed: ${oldStock} → ${newStock} (${stockDifference > 0 ? '+' : ''}${stockDifference})`);
      console.log(`📦 Note: Use inventory/updateStock endpoint to create transaction records`);
    }

    // Get updated product with supplier and category info
    const updatedProduct = await Product.findByPk(id, {
      include: [
        {
          model: Supplier,
          as: 'supplier',
          attributes: ['supplier_id', 'name']
        },
        {
          model: Category,
          as: 'categoryInfo',
          attributes: ['category_id', 'name', 'description']
        }
      ]
    });

    console.log('✅ Product updated successfully:', updatedProduct.name);

    // Log data change
    if (global.logDataChange) {
      global.logDataChange('UPDATE', 'products', {
        id: id,
        changes: updateData,
        updated_product: updatedProduct.toJSON()
      }, req.user?.user_id || 'system');
    }

    res.json({
      success: true,
      message: 'Product updated successfully',
      data: { product: updatedProduct }
    });
  } catch (error) {
    console.error('❌ Error updating product:', error);
    next(error);
  }
};

// Delete product (soft delete)
const deleteProduct = async (req, res, next) => {
  try {
    const { id } = req.params;

    const product = await Product.findByPk(id);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    await product.update({ is_active: false });

    // Log data change
    if (global.logDataChange) {
      global.logDataChange('DELETE', 'products', {
        id: id,
        product_name: product.name,
        soft_delete: true
      }, req.user.user_id);
    }

    res.json({
      success: true,
      message: 'Product deleted successfully'
    });
  } catch (error) {
    next(error);
  }
};

// Get product categories
const getCategories = async (req, res, next) => {
  try {
    console.log('📂 Fetching all categories...');

    // Get categories from Category model
    const categories = await Category.findAll({
      where: { is_active: true },
      order: [['name', 'ASC']]
    });

    console.log(`✅ Found ${categories.length} categories`);

    // Log data change for monitoring
    if (global.logDataChange) {
      global.logDataChange('READ', 'categories', { count: categories.length }, req.user?.user_id);
    }

    res.json({
      success: true,
      data: {
        categories: categories,
        count: categories.length
      }
    });
  } catch (error) {
    console.error('❌ Error fetching categories:', error);
    next(error);
  }
};

// Get suppliers
const getSuppliers = async (req, res, next) => {
  try {
    console.log('🏭 Fetching all suppliers...');

    const suppliers = await Supplier.findAll({
      where: { is_active: true },
      order: [['name', 'ASC']]
    });

    console.log(`✅ Found ${suppliers.length} suppliers`);

    // Log data change for monitoring
    if (global.logDataChange) {
      global.logDataChange('READ', 'suppliers', { count: suppliers.length }, req.user?.user_id);
    }

    res.json({
      success: true,
      data: {
        suppliers: suppliers,
        count: suppliers.length
      }
    });
  } catch (error) {
    console.error('❌ Error fetching suppliers:', error);
    next(error);
  }
};

// Create new supplier with comprehensive error handling and race condition prevention
const createSupplier = async (req, res, next) => {
  let transaction;

  try {
    console.log('🏭 Starting supplier creation process...');

    // Basic validation
    if (!req.body.name || req.body.name.trim().length < 2) {
      return res.status(400).json({
        success: false,
        message: 'Supplier name is required and must be at least 2 characters'
      });
    }

    const {
      name,
      contact_person,
      phone,
      email,
      address,
      payment_terms
    } = req.body;

    const supplierName = name.trim();
    console.log(`🏭 Creating new supplier: ${supplierName}`);

    // Start database transaction to prevent race conditions (SQLite compatible)
    transaction = await sequelize.transaction({
      isolationLevel: null // Remove isolation level for SQLite compatibility
    });

    // Check if supplier with same name already exists (with transaction lock) - SQLite compatible
    const existingSupplier = await Supplier.findOne({
      where: sequelize.where(
        sequelize.fn('LOWER', sequelize.col('name')),
        sequelize.fn('LOWER', supplierName)
      ),
      transaction,
      lock: true // Lock the row to prevent concurrent creation
    });

    if (existingSupplier) {
      await transaction.rollback();
      console.log(`⚠️ Supplier already exists: ${existingSupplier.name} (ID: ${existingSupplier.supplier_id})`);

      // Return existing supplier instead of error for better UX
      return res.status(200).json({
        success: true,
        message: 'Supplier already exists',
        data: existingSupplier,
        existing: true
      });
    }

    // Create new supplier within transaction
    const supplier = await Supplier.create({
      name: supplierName,
      contact_person: contact_person?.trim() || null,
      phone: phone?.trim() || null,
      email: email?.trim() || null,
      address: address?.trim() || null,
      payment_terms: payment_terms?.trim() || null,
      is_active: true
    }, { transaction });

    // Commit transaction
    await transaction.commit();

    console.log(`✅ Supplier created successfully: ${supplier.name} (ID: ${supplier.supplier_id})`);

    // Log data change for monitoring
    if (global.logDataChange) {
      global.logDataChange('CREATE', 'suppliers', supplier.toJSON(), req.user?.user_id);
    }

    res.status(201).json({
      success: true,
      message: 'Supplier created successfully',
      data: supplier,
      existing: false
    });

  } catch (error) {
    console.error('❌ Error creating supplier:', error);

    // Rollback transaction if it exists
    if (transaction) {
      try {
        await transaction.rollback();
        console.log('🔄 Transaction rolled back successfully');
      } catch (rollbackError) {
        console.error('❌ Error rolling back transaction:', rollbackError);
      }
    }

    // Handle specific database errors
    if (error.name === 'SequelizeUniqueConstraintError') {
      return res.status(409).json({
        success: false,
        message: 'Supplier with this name already exists',
        error: 'DUPLICATE_SUPPLIER'
      });
    }

    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({
        success: false,
        message: 'Invalid supplier data',
        error: error.errors?.map(e => e.message).join(', ') || 'Validation failed'
      });
    }

    // Generic error response
    res.status(500).json({
      success: false,
      message: 'Failed to create supplier',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
};

// Create new category
const createCategory = async (req, res, next) => {
  let transaction;

  try {
    // Basic validation
    if (!req.body.name || req.body.name.trim().length < 2) {
      return res.status(400).json({
        success: false,
        message: 'Category name is required and must be at least 2 characters'
      });
    }

    const categoryName = req.body.name.trim();
    console.log(`📂 Creating new category: ${categoryName}`);

    // Start database transaction (SQLite compatible)
    transaction = await sequelize.transaction({
      isolationLevel: null // Remove isolation level for SQLite compatibility
    });

    // Check if category already exists (with transaction lock) - SQLite compatible
    const existingCategory = await Category.findOne({
      where: sequelize.where(
        sequelize.fn('LOWER', sequelize.col('name')),
        sequelize.fn('LOWER', categoryName)
      ),
      transaction,
      lock: true
    });

    if (existingCategory) {
      await transaction.rollback();
      console.log(`⚠️ Category already exists: ${existingCategory.name} (ID: ${existingCategory.category_id})`);

      // Return existing category instead of error for better UX
      return res.status(200).json({
        success: true,
        message: 'Category already exists',
        data: existingCategory,
        existing: true
      });
    }

    // Create new category within transaction
    const category = await Category.create({
      name: categoryName,
      description: req.body.description?.trim() || null,
      is_active: true
    }, { transaction });

    await transaction.commit();
    console.log(`✅ Category created successfully: ${category.name} (ID: ${category.category_id})`);

    // Log data change for monitoring
    if (global.logDataChange) {
      global.logDataChange('CREATE', 'categories', category, req.user?.user_id);
    }

    res.status(201).json({
      success: true,
      message: 'Category created successfully',
      data: category
    });

  } catch (error) {
    console.error('❌ Error creating category:', error);

    // Rollback transaction if it exists
    if (transaction) {
      try {
        await transaction.rollback();
        console.log('🔄 Transaction rolled back successfully');
      } catch (rollbackError) {
        console.error('❌ Error rolling back transaction:', rollbackError);
      }
    }

    // Handle specific database errors
    if (error.name === 'SequelizeUniqueConstraintError') {
      return res.status(409).json({
        success: false,
        message: 'Category with this name already exists',
        error: 'DUPLICATE_CATEGORY'
      });
    }

    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({
        success: false,
        message: 'Invalid category data',
        error: error.errors?.map(e => e.message).join(', ') || 'Validation failed'
      });
    }

    next(error);
  }
};

module.exports = {
  getProducts,
  getProduct,
  createProduct,
  updateProduct,
  deleteProduct,
  getCategories,
  getSuppliers,
  createSupplier,
  createCategory
};
