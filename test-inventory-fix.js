// Test script to verify inventory transaction creation fix
import fetch from 'node-fetch';

const API_BASE = 'http://localhost:3001/api';

// Test data
const testData = {
  // First, we need to create a product to test with
  product: {
    name: 'Test Product for Inventory',
    category: 'Test Category',
    sku: 'TEST-001',
    unit_price: 10.00,
    cost_price: 8.00,
    current_stock: 0,
    min_stock_level: 5,
    supplier_id: 'acd5bd78-4da5-4f6b-bce7-ecb155c4c36f', // Coca Cola supplier ID from logs
    is_active: true
  },

  // Then test inventory transaction
  inventoryTransaction: {
    type: 'in',
    quantity: 100,
    unit_price: 10.00,
    supplier_id: 'acd5bd78-4da5-4f6b-bce7-ecb155c4c36f',
    reference_number: 'TEST-REF-001',
    invoice_number: 'TEST-INV-001',
    notes: 'Test inventory transaction'
  }
};

async function testInventoryFix() {
  try {
    console.log('🧪 Testing Inventory Transaction Fix...');

    // Step 1: Login to get auth token
    console.log('1. Logging in...');
    const loginResponse = await fetch(`${API_BASE}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Ihs@n2553.'
      })
    });

    const loginResult = await loginResponse.json();
    if (!loginResult.success) {
      throw new Error(`Login failed: ${loginResult.message}`);
    }

    const token = loginResult.data.token;
    console.log('✅ Login successful');

    // Step 2: Create a test product
    console.log('2. Creating test product...');
    const productResponse = await fetch(`${API_BASE}/products`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(testData.product)
    });

    const productResult = await productResponse.json();
    if (!productResult.success) {
      throw new Error(`Product creation failed: ${productResult.message}`);
    }

    const productId = productResult.data.product_id;
    console.log(`✅ Product created with ID: ${productId}`);

    // Step 3: Create inventory transaction
    console.log('3. Creating inventory transaction...');
    const transactionData = {
      ...testData.inventoryTransaction,
      product_id: productId
    };

    const transactionResponse = await fetch(`${API_BASE}/inventory/transactions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(transactionData)
    });

    const transactionResult = await transactionResponse.json();

    if (!transactionResult.success) {
      console.error('❌ Transaction creation failed:', transactionResult.message);
      console.error('Response:', transactionResult);
      return false;
    }

    console.log('✅ Inventory transaction created successfully!');
    console.log('Transaction ID:', transactionResult.data.transaction.transaction_id);
    console.log('Product stock updated:', transactionResult.data.product);

    // Step 4: Verify transaction was created
    console.log('4. Verifying transaction...');
    const verifyResponse = await fetch(`${API_BASE}/inventory/transactions`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    const verifyResult = await verifyResponse.json();
    if (verifyResult.success && verifyResult.data.transactions.length > 0) {
      console.log('✅ Transaction verification successful!');
      console.log(`Found ${verifyResult.data.transactions.length} transactions`);
    } else {
      console.log('⚠️ No transactions found in verification');
    }

    console.log('\n🎉 INVENTORY FIX TEST COMPLETED SUCCESSFULLY!');
    return true;

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return false;
  }
}

// Run the test
testInventoryFix().then(success => {
  process.exit(success ? 0 : 1);
});
