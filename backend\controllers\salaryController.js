const { Employee, SalaryAssignment, SalaryPayment, SalaryHistory, User } = require('../models');
const { Op } = require('sequelize');
const chalk = require('chalk');
const moment = require('moment');

// Get salary assignments for an employee
const getSalaryAssignments = async (req, res, next) => {
  try {
    const { employeeId } = req.params;
    console.log(chalk.blue(`💰 Fetching salary assignments for employee: ${employeeId}`));

    const assignments = await SalaryAssignment.findAll({
      where: { employee_id: employeeId },
      include: [
        {
          model: Employee,
          as: 'employee',
          attributes: ['employee_id', 'full_name', 'employee_number']
        },
        {
          model: User,
          as: 'creator',
          attributes: ['user_id', 'email']
        },
        {
          model: User,
          as: 'approver',
          attributes: ['user_id', 'email']
        }
      ],
      order: [['effective_date', 'DESC']]
    });

    console.log(`✅ Found ${assignments.length} salary assignments`);

    res.json({
      success: true,
      data: assignments
    });
  } catch (error) {
    console.error('❌ Error fetching salary assignments:', error);
    next(error);
  }
};

// Create salary assignment
const createSalaryAssignment = async (req, res, next) => {
  try {
    const {
      employee_id,
      base_salary,
      allowances = {},
      bonuses = {},
      deductions = {},
      currency = 'USD',
      pay_frequency = 'monthly',
      effective_date,
      overtime_rate = 1.5,
      notes
    } = req.body;

    console.log(chalk.green(`💰 Creating salary assignment for employee: ${employee_id}`));

    // Verify employee exists
    const employee = await Employee.findByPk(employee_id);
    if (!employee) {
      return res.status(404).json({
        success: false,
        message: 'Employee not found'
      });
    }

    const assignment = await SalaryAssignment.create({
      employee_id,
      base_salary,
      allowances,
      bonuses,
      deductions,
      currency,
      pay_frequency,
      effective_date: effective_date || new Date(),
      overtime_rate,
      notes,
      created_by: req.user.user_id
    });

    // Create history entry
    await SalaryHistory.create({
      employee_id,
      assignment_id: assignment.assignment_id,
      action_type: 'salary_assigned',
      new_values: {
        base_salary,
        allowances,
        bonuses,
        deductions
      },
      amount_involved: base_salary,
      description: `New salary assignment created for ${employee.full_name}`,
      created_by: req.user.user_id
    });

    console.log(`✅ Salary assignment created: ${assignment.assignment_id}`);

    // Log data change for monitoring
    if (global.logDataChange) {
      global.logDataChange('CREATE', 'salary_assignments', assignment, req.user?.user_id);
    }

    res.status(201).json({
      success: true,
      data: assignment,
      message: 'Salary assignment created successfully'
    });
  } catch (error) {
    console.error('❌ Error creating salary assignment:', error);
    next(error);
  }
};

// Update salary assignment
const updateSalaryAssignment = async (req, res, next) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    console.log(chalk.yellow(`💰 Updating salary assignment: ${id}`));

    const assignment = await SalaryAssignment.findByPk(id, {
      include: [
        {
          model: Employee,
          as: 'employee',
          attributes: ['employee_id', 'full_name']
        }
      ]
    });

    if (!assignment) {
      return res.status(404).json({
        success: false,
        message: 'Salary assignment not found'
      });
    }

    // Store previous values for history
    const previousValues = {
      base_salary: assignment.base_salary,
      allowances: assignment.allowances,
      bonuses: assignment.bonuses,
      deductions: assignment.deductions
    };

    // Update assignment
    await assignment.update(updateData);

    // Create history entry
    await SalaryHistory.create({
      employee_id: assignment.employee_id,
      assignment_id: assignment.assignment_id,
      action_type: 'salary_revised',
      previous_values: previousValues,
      new_values: updateData,
      amount_involved: updateData.base_salary || assignment.base_salary,
      description: `Salary assignment updated for ${assignment.employee.full_name}`,
      created_by: req.user.user_id
    });

    console.log(`✅ Salary assignment updated: ${assignment.assignment_id}`);

    // Log data change for monitoring
    if (global.logDataChange) {
      global.logDataChange('UPDATE', 'salary_assignments', assignment, req.user?.user_id);
    }

    res.json({
      success: true,
      data: assignment,
      message: 'Salary assignment updated successfully'
    });
  } catch (error) {
    console.error('❌ Error updating salary assignment:', error);
    next(error);
  }
};

// Process salary payment
const processSalaryPayment = async (req, res, next) => {
  try {
    const {
      employee_id,
      assignment_id,
      pay_period_start,
      pay_period_end,
      working_days = 30,
      present_days = 30,
      absent_days = 0,
      leave_days = 0,
      overtime_hours = 0,
      payment_method = 'bank_transfer',
      notes
    } = req.body;

    console.log(chalk.green(`💳 Processing salary payment for employee: ${employee_id}`));

    // Get employee and assignment
    const employee = await Employee.findByPk(employee_id);
    const assignment = await SalaryAssignment.findByPk(assignment_id);

    if (!employee || !assignment) {
      return res.status(404).json({
        success: false,
        message: 'Employee or salary assignment not found'
      });
    }

    // Calculate salary components
    const grossSalary = parseFloat(assignment.base_salary);
    const totalAllowances = Object.values(assignment.allowances || {}).reduce((sum, val) => sum + parseFloat(val || 0), 0);
    const totalBonuses = Object.values(assignment.bonuses || {}).reduce((sum, val) => sum + parseFloat(val || 0), 0);
    const totalDeductions = Object.values(assignment.deductions || {}).reduce((sum, val) => sum + parseFloat(val || 0), 0);
    
    // Calculate overtime
    const hourlyRate = grossSalary / (working_days * 8); // Assuming 8 hours per day
    const overtimeAmount = overtime_hours * hourlyRate * assignment.overtime_rate;
    
    // Calculate net salary
    const netSalary = grossSalary + totalAllowances + totalBonuses + overtimeAmount - totalDeductions;

    const payment = await SalaryPayment.create({
      employee_id,
      assignment_id,
      pay_period_start,
      pay_period_end,
      payment_date: new Date(),
      gross_salary: grossSalary,
      total_allowances: totalAllowances,
      total_bonuses: totalBonuses,
      total_deductions: totalDeductions,
      net_salary: netSalary,
      overtime_hours,
      overtime_amount: overtimeAmount,
      working_days,
      present_days,
      absent_days,
      leave_days,
      payment_method,
      status: 'completed',
      notes,
      processed_by: req.user.user_id
    });

    // Create history entry
    await SalaryHistory.create({
      employee_id,
      assignment_id,
      payment_id: payment.payment_id,
      action_type: 'payment_processed',
      amount_involved: netSalary,
      description: `Salary payment processed for ${employee.full_name} - ${payment.payment_number}`,
      created_by: req.user.user_id
    });

    console.log(`✅ Salary payment processed: ${payment.payment_number}`);

    // Log data change for monitoring
    if (global.logDataChange) {
      global.logDataChange('CREATE', 'salary_payments', payment, req.user?.user_id);
    }

    res.status(201).json({
      success: true,
      data: payment,
      message: 'Salary payment processed successfully'
    });
  } catch (error) {
    console.error('❌ Error processing salary payment:', error);
    next(error);
  }
};

// Get salary payments
const getSalaryPayments = async (req, res, next) => {
  try {
    const {
      employee_id,
      page = 1,
      limit = 50,
      status = '',
      startDate = '',
      endDate = ''
    } = req.query;

    console.log(chalk.blue(`💳 Fetching salary payments`));

    const offset = (page - 1) * limit;
    const whereClause = {};

    if (employee_id) whereClause.employee_id = employee_id;
    if (status) whereClause.status = status;
    if (startDate && endDate) {
      whereClause.payment_date = {
        [Op.between]: [new Date(startDate), new Date(endDate)]
      };
    }

    const { count, rows: payments } = await SalaryPayment.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Employee,
          as: 'employee',
          attributes: ['employee_id', 'full_name', 'employee_number', 'position']
        },
        {
          model: SalaryAssignment,
          as: 'assignment',
          attributes: ['assignment_id', 'base_salary', 'pay_frequency']
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['payment_date', 'DESC']]
    });

    console.log(`✅ Found ${payments.length} salary payments`);

    res.json({
      success: true,
      data: {
        payments,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / limit),
          totalItems: count,
          itemsPerPage: parseInt(limit)
        }
      }
    });
  } catch (error) {
    console.error('❌ Error fetching salary payments:', error);
    next(error);
  }
};

module.exports = {
  getSalaryAssignments,
  createSalaryAssignment,
  updateSalaryAssignment,
  processSalaryPayment,
  getSalaryPayments
};
