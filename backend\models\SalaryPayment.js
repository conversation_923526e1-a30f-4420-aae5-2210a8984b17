const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const SalaryPayment = sequelize.define('SalaryPayment', {
  payment_id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  payment_number: {
    type: DataTypes.STRING,
    unique: true,
    allowNull: false
  },
  employee_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'employees',
      key: 'employee_id'
    }
  },
  assignment_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'salary_assignments',
      key: 'assignment_id'
    }
  },
  pay_period_start: {
    type: DataTypes.DATE,
    allowNull: false
  },
  pay_period_end: {
    type: DataTypes.DATE,
    allowNull: false
  },
  payment_date: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  gross_salary: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  total_allowances: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0
  },
  total_bonuses: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0
  },
  total_deductions: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0
  },
  net_salary: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  overtime_hours: {
    type: DataTypes.DECIMAL(5, 2),
    defaultValue: 0
  },
  overtime_amount: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0
  },
  working_days: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  present_days: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  absent_days: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  leave_days: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  payment_method: {
    type: DataTypes.ENUM('bank_transfer', 'cash', 'check', 'digital_wallet'),
    defaultValue: 'bank_transfer'
  },
  bank_reference: {
    type: DataTypes.STRING
  },
  status: {
    type: DataTypes.ENUM('pending', 'processing', 'completed', 'failed', 'cancelled'),
    defaultValue: 'pending'
  },
  receipt_generated: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  receipt_sent: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  receipt_url: {
    type: DataTypes.STRING
  },
  notes: {
    type: DataTypes.TEXT
  },
  processed_by: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'user_id'
    }
  },
  approved_by: {
    type: DataTypes.UUID,
    references: {
      model: 'users',
      key: 'user_id'
    }
  },
  approval_date: {
    type: DataTypes.DATE
  }
}, {
  tableName: 'salary_payments',
  indexes: [
    {
      fields: ['payment_number']
    },
    {
      fields: ['employee_id']
    },
    {
      fields: ['assignment_id']
    },
    {
      fields: ['payment_date']
    },
    {
      fields: ['pay_period_start', 'pay_period_end']
    },
    {
      fields: ['status']
    }
  ],
  hooks: {
    beforeCreate: async (payment) => {
      if (!payment.payment_number) {
        // Generate payment number: PAY-YYYY-MM-XXXX
        const date = new Date();
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const count = await SalaryPayment.count({
          where: {
            payment_number: {
              [sequelize.Sequelize.Op.like]: `PAY-${year}-${month}-%`
            }
          }
        });
        payment.payment_number = `PAY-${year}-${month}-${String(count + 1).padStart(4, '0')}`;
      }
    }
  }
});

module.exports = SalaryPayment;
