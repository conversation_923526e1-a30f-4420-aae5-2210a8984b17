import React, { useState, useEffect, useCallback } from 'react';
import { 
  TrendingUpIcon, 
  DollarSignIcon, 
  ShoppingCartIcon,
  StarIcon,
  AlertCircleIcon,
  CheckCircleIcon
} from 'lucide-react';

const MonthlySummary = ({ user, refresh<PERSON>ey, onError, onLoadingChange }) => {
  const [summaryData, setSummaryData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());

  // Fetch monthly summary data
  const fetchMonthlySummary = useCallback(async () => {
    try {
      setLoading(true);
      onLoadingChange?.(true);
      onError?.(null);

      const token = localStorage.getItem('auth_token');
      const response = await fetch(
        `http://localhost:3001/api/commissions/monthly-summary?month=${selectedMonth}&year=${selectedYear}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch monthly summary: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        setSummaryData(result.data);
        console.log('✅ Monthly summary loaded:', result.data);
      } else {
        throw new Error(result.message || 'Failed to load monthly summary');
      }
    } catch (error) {
      console.error('❌ Error fetching monthly summary:', error);
      onError?.(error.message);
    } finally {
      setLoading(false);
      onLoadingChange?.(false);
    }
  }, [selectedMonth, selectedYear, onError, onLoadingChange]);

  // Fetch data on component mount and when dependencies change
  useEffect(() => {
    fetchMonthlySummary();
  }, [fetchMonthlySummary, refreshKey]);

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount || 0);
  };

  // Format percentage
  const formatPercentage = (percentage) => {
    return `${(percentage * 100).toFixed(1)}%`;
  };

  // Generate month options
  const monthOptions = Array.from({ length: 12 }, (_, i) => ({
    value: i + 1,
    label: new Date(2024, i, 1).toLocaleString('default', { month: 'long' })
  }));

  // Generate year options (current year and previous 2 years)
  const currentYear = new Date().getFullYear();
  const yearOptions = Array.from({ length: 3 }, (_, i) => ({
    value: currentYear - i,
    label: (currentYear - i).toString()
  }));

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Loading monthly summary...</span>
      </div>
    );
  }

  if (!summaryData) {
    return (
      <div className="text-center py-12">
        <AlertCircleIcon size={48} className="mx-auto text-gray-400 mb-4" />
        <p className="text-gray-600">No data available for the selected period</p>
      </div>
    );
  }

  const { summary = [] } = summaryData;
  const totalSales = summary.reduce((sum, s) => sum + (s.totalSales || 0), 0);
  const totalCommissions = summary.reduce((sum, s) => sum + (s.totalCommission || 0), 0);
  const totalOrders = summary.reduce((sum, s) => sum + (s.orderCount || 0), 0);

  return (
    <div className="space-y-6">
      {/* Period Selector */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">
          Commission Summary - {monthOptions.find(m => m.value === selectedMonth)?.label} {selectedYear}
        </h3>
        
        <div className="flex items-center space-x-3">
          <select
            value={selectedMonth}
            onChange={(e) => setSelectedMonth(parseInt(e.target.value))}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {monthOptions.map(month => (
              <option key={month.value} value={month.value}>
                {month.label}
              </option>
            ))}
          </select>
          
          <select
            value={selectedYear}
            onChange={(e) => setSelectedYear(parseInt(e.target.value))}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {yearOptions.map(year => (
              <option key={year.value} value={year.value}>
                {year.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-blue-50 rounded-lg p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <DollarSignIcon className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-blue-600">Total Sales</p>
              <p className="text-2xl font-semibold text-blue-900">{formatCurrency(totalSales)}</p>
            </div>
          </div>
        </div>

        <div className="bg-green-50 rounded-lg p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <TrendingUpIcon className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-green-600">Total Commissions</p>
              <p className="text-2xl font-semibold text-green-900">{formatCurrency(totalCommissions)}</p>
            </div>
          </div>
        </div>

        <div className="bg-purple-50 rounded-lg p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ShoppingCartIcon className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-purple-600">Total Orders</p>
              <p className="text-2xl font-semibold text-purple-900">{totalOrders}</p>
            </div>
          </div>
        </div>

        <div className="bg-orange-50 rounded-lg p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <StarIcon className="h-8 w-8 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-orange-600">Avg Commission Rate</p>
              <p className="text-2xl font-semibold text-orange-900">
                {totalSales > 0 ? formatPercentage(totalCommissions / totalSales) : '0.0%'}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Salesman Performance Table */}
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h4 className="text-lg font-medium text-gray-900">Salesman Performance</h4>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Salesman
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Orders
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total Sales
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Commission %
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Commission Earned
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Current Stage
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Progress
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {summary.map((salesman) => (
                <tr key={salesman.salesman_id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-sm font-medium text-blue-600">
                          {salesman.name.charAt(0).toUpperCase()}
                        </span>
                      </div>
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900">{salesman.name}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {salesman.orderCount}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatCurrency(salesman.totalSales)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <span className="text-sm text-gray-900">
                        {formatPercentage(salesman.commissionPercentage)}
                      </span>
                      {salesman.isOverride && (
                        <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                          Custom
                        </span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">
                    {formatCurrency(salesman.totalCommission)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {salesman.currentStage ? (
                      <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                        {salesman.currentStage.stage_name}
                      </span>
                    ) : (
                      <span className="text-sm text-gray-400">No stage</span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-1">
                        <div className="flex items-center justify-between text-xs text-gray-600 mb-1">
                          <span>{salesman.progressToNextStage.toFixed(0)}%</span>
                          {salesman.nextStageTarget && (
                            <span>{formatCurrency(salesman.nextStageTarget)}</span>
                          )}
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${Math.min(salesman.progressToNextStage, 100)}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default MonthlySummary;
