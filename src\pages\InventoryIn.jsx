import { useState, useEffect } from 'react';
import { PackageIcon, PlusIcon, SearchIcon, TruckIcon, CheckIcon, AlertCircleIcon, CalendarIcon, HashIcon, MapPinIcon, FileTextIcon } from 'lucide-react';
import { useApiData } from '../contexts/ApiDataContext';
import { useAuth } from '../contexts/AuthContext';

function InventoryIn() {
  const {
    supplierOptions,
    categoryOptions,
    recentDeliveries,
    addProduct,
    updateProductStock,
    products,
    getLowStockProducts,
    getInventoryValue
  } = useApiData();


  const { user } = useAuth();

  const [formData, setFormData] = useState({
    // Product Information
    productName: '',
    category: '',
    sku: '',
    barcode: '',

    // Supplier Information
    supplier: '',

    // Quantity & Pricing
    quantityReceived: '',
    unitPrice: '',
    costPrice: '',
    totalCost: '',

    // Product Details
    expirationDate: '',
    batchNumber: '',
    storageLocation: '',

    // Transaction Details
    date: new Date().toISOString().split('T')[0],
    invoiceNumber: '',
    referenceNumber: '',
    notes: '',

    // Form State
    isExistingProduct: false,
    selectedProductId: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [showError, setShowError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [existingProducts, setExistingProducts] = useState([]);

  // Initialize existing products list
  useEffect(() => {
    setExistingProducts(products);
  }, [products]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => {
      const newData = { ...prev, [name]: value };

      // Auto-calculate total cost when quantity or unit price changes
      if (name === 'quantityReceived' || name === 'unitPrice') {
        const quantity = name === 'quantityReceived' ? parseFloat(value) : parseFloat(prev.quantityReceived);
        const unitPrice = name === 'unitPrice' ? parseFloat(value) : parseFloat(prev.unitPrice);
        if (!isNaN(quantity) && !isNaN(unitPrice)) {
          newData.totalCost = (quantity * unitPrice).toFixed(2);
        }
      }

      // Auto-fill cost price if not set
      if (name === 'unitPrice' && !prev.costPrice) {
        newData.costPrice = value;
      }

      // Check if product exists when product name changes
      if (name === 'productName') {
        const existingProduct = products.find(p =>
          p.name.toLowerCase() === value.toLowerCase()
        );
        if (existingProduct) {
          newData.isExistingProduct = true;
          newData.selectedProductId = existingProduct.id;
          newData.category = existingProduct.category;
          newData.sku = existingProduct.sku || '';
          newData.unitPrice = existingProduct.price.toString();
          newData.costPrice = existingProduct.costPrice?.toString() || existingProduct.price.toString();
        } else {
          newData.isExistingProduct = false;
          newData.selectedProductId = '';
        }
      }

      return newData;
    });
  };

  const resetForm = () => {
    setFormData({
      productName: '',
      category: '',
      sku: '',
      barcode: '',
      supplier: '',
      quantityReceived: '',
      unitPrice: '',
      costPrice: '',
      totalCost: '',
      expirationDate: '',
      batchNumber: '',
      storageLocation: '',
      date: new Date().toISOString().split('T')[0],
      invoiceNumber: '',
      referenceNumber: '',
      notes: '',
      isExistingProduct: false,
      selectedProductId: ''
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setShowError(false);
    setErrorMessage('');

    try {
      // Validate required fields
      if (!formData.productName || !formData.category || !formData.supplier ||
          !formData.quantityReceived || !formData.unitPrice) {
        throw new Error('Please fill in all required fields');
      }

      const quantity = parseInt(formData.quantityReceived);
      const unitPrice = parseFloat(formData.unitPrice);
      const costPrice = parseFloat(formData.costPrice) || unitPrice;

      if (formData.isExistingProduct && formData.selectedProductId) {
        // Update existing product stock
        const existingProduct = products.find(p => p.id === formData.selectedProductId);
        const newStock = existingProduct.stock + quantity;

        updateProductStock(formData.selectedProductId, newStock, 'in', {
          supplier: formData.supplier,
          invoiceNumber: formData.invoiceNumber,
          referenceNumber: formData.referenceNumber,
          batchNumber: formData.batchNumber,
          expirationDate: formData.expirationDate,
          storageLocation: formData.storageLocation,
          notes: formData.notes,
          recordedBy: user?.user_id || user?.email || 'System',
          unitPrice: unitPrice,
          costPrice: costPrice
        });
      } else {
        // Create new product
        const productData = {
          name: formData.productName,
          category: formData.category,
          sku: formData.sku,
          barcode: formData.barcode,
          price: unitPrice,
          costPrice: costPrice,
          stock: quantity,
          supplier: formData.supplier,
          minStock: 50, // Default minimum stock
          storageLocation: formData.storageLocation,
          expirationDate: formData.expirationDate,
          batchNumber: formData.batchNumber,
          invoiceNumber: formData.invoiceNumber,
          referenceNumber: formData.referenceNumber,
          notes: formData.notes,
          recordedBy: user?.user_id || user?.email || 'System'
        };

        await addProduct(productData);
      }

      // Show success message
      setShowSuccess(true);

      // Reset form after delay
      setTimeout(() => {
        setShowSuccess(false);
        resetForm();
      }, 3000);

    } catch (error) {
      console.error('Error processing inventory:', error);
      setErrorMessage(error.message || 'Failed to process inventory. Please try again.');
      setShowError(true);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Use data from context (no fallbacks for clean slate)
  const currentSupplierOptions = supplierOptions;
  const currentCategoryOptions = categoryOptions;
  const currentRecentDeliveries = recentDeliveries;

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Inventory In</h1>
          <p className="text-gray-600">Record incoming inventory from suppliers</p>
        </div>
        <div className="flex space-x-3">
          <div className="text-right">
            <p className="text-sm text-gray-500">Current Inventory Value</p>
            <p className="text-lg font-semibold text-green-600">${getInventoryValue().toFixed(2)}</p>
          </div>
          <div className="text-right">
            <p className="text-sm text-gray-500">Low Stock Items</p>
            <p className="text-lg font-semibold text-red-600">{getLowStockProducts().length}</p>
          </div>
          <button className="flex items-center bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
            <SearchIcon size={16} className="mr-2" />
            View All Records
          </button>
        </div>
      </div>
      {showSuccess && (
        <div className="bg-green-50 border-l-4 border-green-500 p-4 mb-6 flex items-start">
          <CheckIcon size={20} className="text-green-500 mr-2 mt-0.5" />
          <div>
            <p className="text-green-700 font-medium">
              {formData.isExistingProduct ? 'Stock Updated Successfully!' : 'New Product Added Successfully!'}
            </p>
            <p className="text-green-600">
              {formData.isExistingProduct
                ? `Stock increased by ${formData.quantityReceived} units for ${formData.productName}`
                : `${formData.productName} has been added to inventory with ${formData.quantityReceived} units`
              }
            </p>
          </div>
        </div>
      )}

      {showError && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6 flex items-start">
          <AlertCircleIcon size={20} className="text-red-500 mr-2 mt-0.5" />
          <div>
            <p className="text-red-700 font-medium">Error Processing Inventory</p>
            <p className="text-red-600">{errorMessage}</p>
          </div>
        </div>
      )}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <PackageIcon size={20} className="text-blue-600 mr-2" />
            <h2 className="text-lg font-medium text-gray-800">Comprehensive Inventory Intake</h2>
          </div>
          {formData.isExistingProduct && (
            <div className="bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-sm font-medium">
              Updating Existing Product
            </div>
          )}
        </div>

        <form onSubmit={handleSubmit}>
          {/* Product Information Section */}
          <div className="mb-8">
            <h3 className="text-md font-semibold text-gray-700 mb-4 flex items-center">
              <PackageIcon size={16} className="mr-2" />
              Product Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div>
                <label htmlFor="productName" className="block text-sm font-medium text-gray-700 mb-1">
                  Product Name *
                </label>
                <input
                  type="text"
                  id="productName"
                  name="productName"
                  value={formData.productName}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter product name"
                  required
                />
                {formData.isExistingProduct && (
                  <p className="text-xs text-blue-600 mt-1">✓ Existing product found</p>
                )}
              </div>

              <div>
                <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
                  Category *
                </label>
                <select
                  id="category"
                  name="category"
                  value={formData.category}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                >
                  {currentCategoryOptions.map(option => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="sku" className="block text-sm font-medium text-gray-700 mb-1">
                  SKU/Product Code
                </label>
                <div className="relative">
                  <HashIcon size={16} className="absolute left-3 top-3 text-gray-400" />
                  <input
                    type="text"
                    id="sku"
                    name="sku"
                    value={formData.sku}
                    onChange={handleChange}
                    className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter SKU"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="barcode" className="block text-sm font-medium text-gray-700 mb-1">
                  Barcode
                </label>
                <input
                  type="text"
                  id="barcode"
                  name="barcode"
                  value={formData.barcode}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter barcode"
                />
              </div>
            </div>
          </div>

          {/* Supplier Information Section */}
          <div className="mb-8">
            <h3 className="text-md font-semibold text-gray-700 mb-4 flex items-center">
              <TruckIcon size={16} className="mr-2" />
              Supplier Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="supplier" className="block text-sm font-medium text-gray-700 mb-1">
                  Supplier *
                </label>
                <select
                  id="supplier"
                  name="supplier"
                  value={formData.supplier}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                >
                  {currentSupplierOptions.map(option => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Quantity & Pricing Section */}
          <div className="mb-8">
            <h3 className="text-md font-semibold text-gray-700 mb-4 flex items-center">
              <HashIcon size={16} className="mr-2" />
              Quantity & Pricing
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div>
                <label htmlFor="quantityReceived" className="block text-sm font-medium text-gray-700 mb-1">
                  Quantity Received *
                </label>
                <input
                  type="number"
                  id="quantityReceived"
                  name="quantityReceived"
                  value={formData.quantityReceived}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter quantity"
                  min="1"
                  required
                />
              </div>

              <div>
                <label htmlFor="unitPrice" className="block text-sm font-medium text-gray-700 mb-1">
                  Unit Price ($) *
                </label>
                <input
                  type="number"
                  id="unitPrice"
                  name="unitPrice"
                  value={formData.unitPrice}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="0.00"
                  step="0.01"
                  min="0"
                  required
                />
              </div>

              <div>
                <label htmlFor="costPrice" className="block text-sm font-medium text-gray-700 mb-1">
                  Cost Price ($)
                </label>
                <input
                  type="number"
                  id="costPrice"
                  name="costPrice"
                  value={formData.costPrice}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="0.00"
                  step="0.01"
                  min="0"
                />
              </div>

              <div>
                <label htmlFor="totalCost" className="block text-sm font-medium text-gray-700 mb-1">
                  Total Cost ($)
                </label>
                <input
                  type="text"
                  id="totalCost"
                  name="totalCost"
                  value={formData.totalCost}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 focus:outline-none font-semibold text-green-600"
                  placeholder="0.00"
                  readOnly
                />
              </div>
            </div>
          </div>

          {/* Product Details Section */}
          <div className="mb-8">
            <h3 className="text-md font-semibold text-gray-700 mb-4 flex items-center">
              <FileTextIcon size={16} className="mr-2" />
              Product Details
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div>
                <label htmlFor="expirationDate" className="block text-sm font-medium text-gray-700 mb-1">
                  Expiration Date
                </label>
                <div className="relative">
                  <CalendarIcon size={16} className="absolute left-3 top-3 text-gray-400" />
                  <input
                    type="date"
                    id="expirationDate"
                    name="expirationDate"
                    value={formData.expirationDate}
                    onChange={handleChange}
                    className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="batchNumber" className="block text-sm font-medium text-gray-700 mb-1">
                  Batch/Lot Number
                </label>
                <input
                  type="text"
                  id="batchNumber"
                  name="batchNumber"
                  value={formData.batchNumber}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter batch number"
                />
              </div>

              <div>
                <label htmlFor="storageLocation" className="block text-sm font-medium text-gray-700 mb-1">
                  Storage Location
                </label>
                <div className="relative">
                  <MapPinIcon size={16} className="absolute left-3 top-3 text-gray-400" />
                  <input
                    type="text"
                    id="storageLocation"
                    name="storageLocation"
                    value={formData.storageLocation}
                    onChange={handleChange}
                    className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., Warehouse A, Shelf 3"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Transaction Details Section */}
          <div className="mb-8">
            <h3 className="text-md font-semibold text-gray-700 mb-4 flex items-center">
              <FileTextIcon size={16} className="mr-2" />
              Transaction Details
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div>
                <label htmlFor="date" className="block text-sm font-medium text-gray-700 mb-1">
                  Date *
                </label>
                <input
                  type="date"
                  id="date"
                  name="date"
                  value={formData.date}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              <div>
                <label htmlFor="invoiceNumber" className="block text-sm font-medium text-gray-700 mb-1">
                  Invoice Number
                </label>
                <input
                  type="text"
                  id="invoiceNumber"
                  name="invoiceNumber"
                  value={formData.invoiceNumber}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter invoice number"
                />
              </div>

              <div>
                <label htmlFor="referenceNumber" className="block text-sm font-medium text-gray-700 mb-1">
                  Reference Number
                </label>
                <input
                  type="text"
                  id="referenceNumber"
                  name="referenceNumber"
                  value={formData.referenceNumber}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter reference number"
                />
              </div>
            </div>

            <div className="mt-6">
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
                Notes
              </label>
              <textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleChange}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter any additional notes about this inventory transaction"
              ></textarea>
            </div>
          </div>

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
              onClick={resetForm}
            >
              Clear Form
            </button>
            <button
              type="submit"
              className="flex items-center bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:bg-blue-400 transition-colors"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {formData.isExistingProduct ? 'Updating Stock...' : 'Adding Product...'}
                </>
              ) : (
                <>
                  <PlusIcon size={16} className="mr-2" />
                  {formData.isExistingProduct ? 'Update Stock' : 'Add to Inventory'}
                </>
              )}
            </button>
          </div>
        </form>
      </div>
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center mb-4">
          <TruckIcon size={20} className="text-blue-600 mr-2" />
          <h2 className="text-lg font-medium text-gray-800">Recent Deliveries</h2>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Cost</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {currentRecentDeliveries.map((delivery, index) => (
                <tr key={index}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{delivery.date}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{delivery.supplier}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{delivery.product}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{delivery.quantity.toLocaleString()}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${delivery.totalCost.toFixed(2)}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">{delivery.status}</span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

export default InventoryIn;
