const { Notification, User } = require('../models');
const chalk = require('chalk');

// Get notifications for current user
const getNotifications = async (req, res, next) => {
  try {
    const { page = 1, limit = 20, unread_only = false } = req.query;
    const userId = req.user.role === 'admin' ? null : req.user.user_id;

    console.log(chalk.blue(`📢 Fetching notifications for user: ${req.user.role}`));

    const whereClause = {
      target_user_id: userId
    };

    if (unread_only === 'true') {
      whereClause.is_read = false;
    }

    const { count, rows: notifications } = await Notification.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: User,
          as: 'createdBy',
          attributes: ['user_id', 'email'],
          required: false
        }
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: (parseInt(page) - 1) * parseInt(limit)
    });

    console.log(`✅ Found ${notifications.length} notifications`);

    res.json({
      success: true,
      data: {
        notifications: notifications,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / parseInt(limit)),
          totalItems: count,
          itemsPerPage: parseInt(limit)
        }
      }
    });
  } catch (error) {
    console.error('❌ Error fetching notifications:', error);
    next(error);
  }
};

// Get unread notification count
const getUnreadCount = async (req, res, next) => {
  try {
    const userId = req.user.role === 'admin' ? null : req.user.user_id;

    const count = await Notification.count({
      where: {
        target_user_id: userId,
        is_read: false
      }
    });

    res.json({
      success: true,
      data: { unread_count: count }
    });
  } catch (error) {
    console.error('❌ Error fetching unread count:', error);
    next(error);
  }
};

// Mark notification as read
const markAsRead = async (req, res, next) => {
  try {
    const { notification_id } = req.params;
    const userId = req.user.role === 'admin' ? null : req.user.user_id;

    console.log(chalk.blue(`📖 Marking notification ${notification_id} as read`));

    const notification = await Notification.findOne({
      where: {
        notification_id: notification_id,
        target_user_id: userId
      }
    });

    if (!notification) {
      return res.status(404).json({
        success: false,
        message: 'Notification not found'
      });
    }

    await notification.markAsRead();

    console.log('✅ Notification marked as read');

    res.json({
      success: true,
      message: 'Notification marked as read',
      data: notification
    });
  } catch (error) {
    console.error('❌ Error marking notification as read:', error);
    next(error);
  }
};

// Mark all notifications as read
const markAllAsRead = async (req, res, next) => {
  try {
    const userId = req.user.role === 'admin' ? null : req.user.user_id;

    console.log(chalk.blue('📖 Marking all notifications as read'));

    const [updatedCount] = await Notification.update(
      { is_read: true },
      {
        where: {
          target_user_id: userId,
          is_read: false
        }
      }
    );

    console.log(`✅ Marked ${updatedCount} notifications as read`);

    res.json({
      success: true,
      message: `Marked ${updatedCount} notifications as read`,
      data: { updated_count: updatedCount }
    });
  } catch (error) {
    console.error('❌ Error marking all notifications as read:', error);
    next(error);
  }
};

// Delete notification
const deleteNotification = async (req, res, next) => {
  try {
    const { notification_id } = req.params;
    const userId = req.user.role === 'admin' ? null : req.user.user_id;

    console.log(chalk.blue(`🗑️ Deleting notification ${notification_id}`));

    const notification = await Notification.findOne({
      where: {
        notification_id: notification_id,
        target_user_id: userId
      }
    });

    if (!notification) {
      return res.status(404).json({
        success: false,
        message: 'Notification not found'
      });
    }

    await notification.destroy();

    console.log('✅ Notification deleted');

    res.json({
      success: true,
      message: 'Notification deleted successfully'
    });
  } catch (error) {
    console.error('❌ Error deleting notification:', error);
    next(error);
  }
};

// Create stage progression notification (internal function)
const createStageProgressionNotification = async (salesmanData, stageData, salesAmount) => {
  try {
    console.log(chalk.yellow(`🚨 Creating stage progression notification for ${salesmanData.name}`));

    const notification = await Notification.createStageProgressionNotification(
      salesmanData,
      stageData,
      salesAmount
    );

    console.log('✅ Stage progression notification created');
    return notification;
  } catch (error) {
    console.error('❌ Error creating stage progression notification:', error);
    throw error;
  }
};

module.exports = {
  getNotifications,
  getUnreadCount,
  markAsRead,
  markAllAsRead,
  deleteNotification,
  createStageProgressionNotification
};
