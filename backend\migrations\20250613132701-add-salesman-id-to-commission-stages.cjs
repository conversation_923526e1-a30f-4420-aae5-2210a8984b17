'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Only add the column if it does not exist
    const tableInfo = await queryInterface.describeTable('commission_stages');
    if (!tableInfo['salesman_id']) {
      await queryInterface.addColumn('commission_stages', 'salesman_id', {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'salesmen',
          key: 'salesman_id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      });
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('commission_stages', 'salesman_id');
  }
}; 