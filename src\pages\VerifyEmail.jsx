import { useState, useEffect } from 'react';
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom';
import { CheckCircleIcon, AlertCircleIcon, MailIcon, RefreshCwIcon } from 'lucide-react';

function VerifyEmail() {
  const [status, setStatus] = useState('pending'); // pending, success, error, expired
  const [message, setMessage] = useState('');
  const [isResending, setIsResending] = useState(false);
  const [resendCooldown, setResendCooldown] = useState(0);
  const location = useLocation();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const email = location.state?.email || '';
  const token = searchParams.get('token');

  useEffect(() => {
    if (token) {
      // Auto-verify if token is in URL
      verifyEmailToken(token);
    } else if (location.state?.message) {
      setMessage(location.state.message);
    }
  }, [token, location.state]);

  useEffect(() => {
    // Countdown timer for resend cooldown
    if (resendCooldown > 0) {
      const timer = setTimeout(() => {
        setResendCooldown(resendCooldown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [resendCooldown]);

  const verifyEmailToken = async (verificationToken) => {
    try {
      setStatus('pending');
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/auth/verify-email`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token: verificationToken }),
      });

      const data = await response.json();

      if (data.success) {
        setStatus('success');
        setMessage(data.message);
        // Redirect to login after 3 seconds
        setTimeout(() => {
          navigate('/login', { 
            state: { 
              message: 'Email verified successfully! You can now log in.',
              type: 'success'
            }
          });
        }, 3000);
      } else {
        setStatus('error');
        setMessage(data.message || 'Email verification failed');
      }
    } catch (error) {
      setStatus('error');
      setMessage('Network error. Please try again.');
    }
  };

  const resendVerificationEmail = async () => {
    if (!email) {
      setMessage('Email address not found. Please try registering again.');
      return;
    }

    setIsResending(true);
    try {
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/auth/resend-verification`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (data.success) {
        setMessage('Verification email sent successfully! Please check your inbox.');
        setResendCooldown(60); // 60 second cooldown
      } else {
        setMessage(data.message || 'Failed to resend verification email');
      }
    } catch (error) {
      setMessage('Network error. Please try again.');
    } finally {
      setIsResending(false);
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'success':
        return <CheckCircleIcon className="h-16 w-16 text-green-500 mx-auto" />;
      case 'error':
        return <AlertCircleIcon className="h-16 w-16 text-red-500 mx-auto" />;
      case 'pending':
      default:
        return <MailIcon className="h-16 w-16 text-blue-500 mx-auto" />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'success':
        return 'text-green-800 bg-green-50 border-green-200';
      case 'error':
        return 'text-red-800 bg-red-50 border-red-200';
      case 'pending':
      default:
        return 'text-blue-800 bg-blue-50 border-blue-200';
    }
  };

  const getTitle = () => {
    switch (status) {
      case 'success':
        return 'Email Verified Successfully!';
      case 'error':
        return 'Email Verification Failed';
      case 'pending':
      default:
        return 'Verify Your Email Address';
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <h1 className="text-center text-3xl font-bold text-blue-800">ZIDAN Enterprises</h1>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="text-center">
            {getStatusIcon()}
            <h2 className="mt-4 text-2xl font-bold text-gray-900">{getTitle()}</h2>
            
            {status === 'pending' && !token && (
              <div className="mt-6">
                <p className="text-gray-600">
                  We've sent a verification email to:
                </p>
                <p className="mt-2 font-medium text-gray-900">{email}</p>
                <p className="mt-4 text-sm text-gray-600">
                  Please check your email and click the verification link to activate your account.
                </p>
              </div>
            )}

            {status === 'pending' && token && (
              <div className="mt-6">
                <div className="flex justify-center">
                  <svg className="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                </div>
                <p className="mt-4 text-gray-600">Verifying your email address...</p>
              </div>
            )}

            {message && (
              <div className={`mt-6 p-4 rounded-md border ${getStatusColor()}`}>
                <p className="text-sm">{message}</p>
              </div>
            )}

            {status === 'success' && (
              <div className="mt-6">
                <p className="text-gray-600">
                  Redirecting you to the login page in 3 seconds...
                </p>
                <div className="mt-4">
                  <a
                    href="/login"
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                  >
                    Continue to Login
                  </a>
                </div>
              </div>
            )}

            {(status === 'pending' || status === 'error') && !token && (
              <div className="mt-6 space-y-4">
                <div>
                  <p className="text-sm text-gray-600 mb-4">
                    Didn't receive the email? Check your spam folder or request a new one.
                  </p>
                  <button
                    onClick={resendVerificationEmail}
                    disabled={isResending || resendCooldown > 0}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-blue-300"
                  >
                    {isResending ? (
                      <>
                        <RefreshCwIcon className="animate-spin -ml-1 mr-2 h-4 w-4" />
                        Sending...
                      </>
                    ) : resendCooldown > 0 ? (
                      `Resend in ${resendCooldown}s`
                    ) : (
                      <>
                        <MailIcon className="-ml-1 mr-2 h-4 w-4" />
                        Resend Verification Email
                      </>
                    )}
                  </button>
                </div>

                <div className="border-t border-gray-200 pt-4">
                  <p className="text-sm text-gray-600">
                    Need help?{' '}
                    <a href="/login" className="font-medium text-blue-600 hover:text-blue-500">
                      Back to Login
                    </a>
                  </p>
                </div>
              </div>
            )}

            {status === 'error' && token && (
              <div className="mt-6">
                <p className="text-sm text-gray-600 mb-4">
                  The verification link may have expired or is invalid.
                </p>
                <div className="space-y-3">
                  {email && (
                    <button
                      onClick={resendVerificationEmail}
                      disabled={isResending || resendCooldown > 0}
                      className="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-blue-300"
                    >
                      {isResending ? (
                        <>
                          <RefreshCwIcon className="animate-spin -ml-1 mr-2 h-4 w-4" />
                          Sending...
                        </>
                      ) : resendCooldown > 0 ? (
                        `Resend in ${resendCooldown}s`
                      ) : (
                        'Request New Verification Email'
                      )}
                    </button>
                  )}
                  <a
                    href="/signup"
                    className="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Create New Account
                  </a>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default VerifyEmail;
