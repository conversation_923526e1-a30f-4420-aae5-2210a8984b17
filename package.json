{"name": "magic-patterns-vite-template", "version": "0.0.1", "private": true, "type": "commonjs", "scripts": {"dev": "npx vite", "start": "npx vite", "build": "npx vite build", "lint": "eslint . --ext .js,.jsx", "preview": "npx vite preview"}, "dependencies": {"axios": "^1.9.0", "bcryptjs": "^3.0.2", "lodash.isequal": "^4.5.0", "lucide-react": "^0.515.0", "node-fetch": "^3.3.2", "nodemailer": "^7.0.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.26.2", "sequelize": "^6.37.7", "sqlite3": "^5.1.7"}, "devDependencies": {"@types/node": "^20.11.18", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^5.54.0", "@typescript-eslint/parser": "^5.54.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "latest", "eslint": "^8.50.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.1", "postcss": "latest", "tailwindcss": "3.4.17", "typescript": "^5.5.4", "vite": "^5.2.0"}}