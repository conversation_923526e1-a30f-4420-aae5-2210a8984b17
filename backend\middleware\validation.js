const { body, param, query, validationResult } = require('express-validator');

// Handle validation errors
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }
  next();
};

// Password strength validation
const validatePasswordStrength = (password) => {
  const minLength = 8;
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

  if (password.length < minLength) {
    return 'Password must be at least 8 characters long';
  }
  if (!hasUpperCase) {
    return 'Password must contain at least one uppercase letter';
  }
  if (!hasLowerCase) {
    return 'Password must contain at least one lowercase letter';
  }
  if (!hasNumbers) {
    return 'Password must contain at least one number';
  }
  if (!hasSpecialChar) {
    return 'Password must contain at least one special character';
  }
  return null;
};

// User validation rules
const validateUserRegistration = [
  body('full_name')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Full name must be between 2 and 50 characters')
    .matches(/^[a-zA-Z\s]+$/)
    .withMessage('Full name can only contain letters and spaces'),
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required')
    .isLength({ max: 100 })
    .withMessage('Email must not exceed 100 characters'),
  body('password')
    .custom((value) => {
      const error = validatePasswordStrength(value);
      if (error) {
        throw new Error(error);
      }
      return true;
    }),
  body('confirm_password')
    .custom((value, { req }) => {
      if (value !== req.body.password) {
        throw new Error('Passwords do not match');
      }
      return true;
    }),
  body('role')
    .isIn(['admin', 'salesman'])
    .withMessage('Role must be either admin or salesman'),
  handleValidationErrors
];

const validateUserLogin = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required'),
  body('password')
    .notEmpty()
    .withMessage('Password is required'),
  handleValidationErrors
];

// Product validation rules
const validateProduct = [
  body('name')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Product name is required'),
  body('category')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Category is required'),
  body('unit_price')
    .isFloat({ min: 0 })
    .withMessage('Unit price must be a positive number'),
  body('current_stock')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Stock must be a non-negative integer'),
  body('min_stock_level')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Minimum stock level must be a non-negative integer'),
  handleValidationErrors
];

// Customer validation rules
const validateCustomer = [
  body('name')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Customer name is required'),
  body('type')
    .isIn(['Convenience Store', 'Supermarket', 'Wholesaler', 'Restaurant', 'Other'])
    .withMessage('Invalid customer type'),
  body('contact')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Contact information is required'),
  body('email')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required'),
  body('credit_limit')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Credit limit must be a positive number'),
  handleValidationErrors
];

// Order validation rules
const validateOrder = [
  body('customer_id')
    .isUUID()
    .withMessage('Valid customer ID is required'),
  body('items')
    .isArray({ min: 1 })
    .withMessage('Order must contain at least one item'),
  body('items.*.product_id')
    .isUUID()
    .withMessage('Valid product ID is required for each item'),
  body('items.*.quantity')
    .isInt({ min: 1 })
    .withMessage('Quantity must be a positive integer'),
  body('items.*.unit_price')
    .isFloat({ min: 0 })
    .withMessage('Unit price must be a positive number'),
  body('total_amount')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Total amount must be a positive number'),
  handleValidationErrors
];

// Order status update validation rules
const validateOrderStatus = [
  body('status')
    .isIn(['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled'])
    .withMessage('Invalid order status. Must be one of: pending, confirmed, processing, shipped, delivered, cancelled'),
  handleValidationErrors
];

// Payment validation rules
const validatePayment = [
  body('customer_id')
    .isUUID()
    .withMessage('Valid customer ID is required'),
  body('amount')
    .isFloat({ min: 0.01 })
    .withMessage('Amount must be greater than 0'),
  body('payment_method')
    .isIn(['cash', 'credit_card', 'bank_transfer', 'check'])
    .withMessage('Invalid payment method'),
  handleValidationErrors
];

// Expense validation rules
const validateExpense = [
  body('category')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Category is required'),
  body('description')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Description is required'),
  body('amount')
    .isFloat({ min: 0.01 })
    .withMessage('Amount must be greater than 0'),
  body('payment_method')
    .isIn(['cash', 'credit_card', 'bank_transfer', 'check'])
    .withMessage('Invalid payment method'),
  handleValidationErrors
];

// Password reset validation rules
const validateForgotPassword = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required'),
  handleValidationErrors
];

const validateResetPassword = [
  body('token')
    .isLength({ min: 32, max: 64 })
    .withMessage('Invalid reset token'),
  body('password')
    .custom((value) => {
      const error = validatePasswordStrength(value);
      if (error) {
        throw new Error(error);
      }
      return true;
    }),
  body('confirm_password')
    .custom((value, { req }) => {
      if (value !== req.body.password) {
        throw new Error('Passwords do not match');
      }
      return true;
    }),
  handleValidationErrors
];

const validateEmailVerification = [
  body('token')
    .isLength({ min: 32, max: 64 })
    .withMessage('Invalid verification token'),
  handleValidationErrors
];

// UUID parameter validation
const validateUUID = (paramName) => [
  param(paramName)
    .isUUID()
    .withMessage(`${paramName} must be a valid UUID`),
  handleValidationErrors
];

// Generic validation request middleware (alias for handleValidationErrors)
const validateRequest = handleValidationErrors;

module.exports = {
  handleValidationErrors,
  validateRequest,
  validateUserRegistration,
  validateUserLogin,
  validateForgotPassword,
  validateResetPassword,
  validateEmailVerification,
  validateProduct,
  validateCustomer,
  validateOrder,
  validateOrderStatus,
  validatePayment,
  validateExpense,
  validateUUID,
  validatePasswordStrength
};
