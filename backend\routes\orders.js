const express = require('express');
const router = express.Router();
const {
  getOrders,
  getOrder,
  createOrder,
  updateOrderStatus,
  updateOrderPaymentStatus
} = require('../controllers/orderController');
const { validateOrder, validateOrderStatus, validateUUID } = require('../middleware/validation');
const { authenticateToken, adminOrSalesman } = require('../middleware/auth');

// All routes require authentication
router.use(authenticateToken);

// GET routes
router.get('/', adminOrSalesman, getOrders);
router.get('/:id', adminOr<PERSON><PERSON><PERSON>, validateUUID('id'), getOrder);

// POST routes
router.post('/', adminOrSalesman, validateOrder, createOrder);

// PUT routes
router.put('/:id/status', adminOr<PERSON><PERSON><PERSON>, validateUUID('id'), validateOrderStatus, updateOrderStatus);
router.put('/:id/payment-status', adminOrSalesman, validateUUID('id'), updateOrderPaymentStatus);

module.exports = router;
