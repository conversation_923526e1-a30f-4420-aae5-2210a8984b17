import { useCallback } from 'react';
import { useData } from '../contexts/DataContext';

/**
 * Enhanced hook for automatic data refresh after CRUD operations
 * Provides intelligent refresh triggers for database modifications
 */
const useDataRefresh = () => {
  const {
    refreshAllData,
    refreshProducts,
    refreshOrders,
    refreshCustomers,
    refreshInventory
  } = useData();

  // Comprehensive refresh after any CRUD operation
  const refreshAfterCRUD = useCallback(async (operation, dataType, additionalTypes = []) => {
    try {
      console.log(`🔄 Auto-refresh triggered after ${operation} on ${dataType}`);
      
      // Determine which data to refresh based on the operation and data type
      const refreshPromises = [];
      
      switch (dataType) {
        case 'customer':
          refreshPromises.push(refreshCustomers());
          // Customer changes might affect orders and payments
          if (operation === 'create' || operation === 'update') {
            refreshPromises.push(refreshOrders());
          }
          break;
          
        case 'order':
          refreshPromises.push(refreshOrders());
          // Order changes affect customers (pending amounts) and inventory
          refreshPromises.push(refreshCustomers());
          if (operation === 'create' || operation === 'update') {
            refreshPromises.push(refreshProducts());
          }
          break;
          
        case 'product':
          refreshPromises.push(refreshProducts());
          // Product changes might affect orders and inventory
          if (operation === 'create' || operation === 'update') {
            refreshPromises.push(refreshOrders());
          }
          break;
          
        case 'payment':
          refreshPromises.push(refreshOrders());
          refreshPromises.push(refreshCustomers());
          break;
          
        case 'inventory':
          refreshPromises.push(refreshInventory());
          // Inventory changes affect orders and customers
          refreshPromises.push(refreshOrders());
          refreshPromises.push(refreshCustomers());
          break;
          
        case 'supplier':
        case 'category':
          refreshPromises.push(refreshProducts());
          refreshPromises.push(refreshInventory());
          break;
          
        default:
          // For unknown types or comprehensive changes, refresh all data
          refreshPromises.push(refreshAllData());
      }
      
      // Add any additional refresh types specified
      additionalTypes.forEach(type => {
        switch (type) {
          case 'products':
            refreshPromises.push(refreshProducts());
            break;
          case 'orders':
            refreshPromises.push(refreshOrders());
            break;
          case 'customers':
            refreshPromises.push(refreshCustomers());
            break;
          case 'inventory':
            refreshPromises.push(refreshInventory());
            break;
          case 'all':
            refreshPromises.push(refreshAllData());
            break;
        }
      });
      
      // Execute all refresh operations in parallel
      await Promise.all(refreshPromises);
      
      console.log(`✅ Auto-refresh completed after ${operation} on ${dataType}`);
      return true;
    } catch (error) {
      console.error(`❌ Auto-refresh failed after ${operation} on ${dataType}:`, error);
      // Don't throw error to prevent breaking the main operation
      return false;
    }
  }, [refreshAllData, refreshProducts, refreshOrders, refreshCustomers, refreshInventory]);

  // Specific refresh functions for common operations
  const refreshAfterCustomerCreate = useCallback(async () => {
    return refreshAfterCRUD('create', 'customer');
  }, [refreshAfterCRUD]);

  const refreshAfterCustomerUpdate = useCallback(async () => {
    return refreshAfterCRUD('update', 'customer');
  }, [refreshAfterCRUD]);

  const refreshAfterOrderCreate = useCallback(async () => {
    return refreshAfterCRUD('create', 'order');
  }, [refreshAfterCRUD]);

  const refreshAfterOrderUpdate = useCallback(async () => {
    return refreshAfterCRUD('update', 'order');
  }, [refreshAfterCRUD]);

  const refreshAfterPaymentCreate = useCallback(async () => {
    return refreshAfterCRUD('create', 'payment');
  }, [refreshAfterCRUD]);

  const refreshAfterPaymentUpdate = useCallback(async () => {
    return refreshAfterCRUD('update', 'payment');
  }, [refreshAfterCRUD]);

  const refreshAfterProductCreate = useCallback(async () => {
    return refreshAfterCRUD('create', 'product');
  }, [refreshAfterCRUD]);

  const refreshAfterProductUpdate = useCallback(async () => {
    return refreshAfterCRUD('update', 'product');
  }, [refreshAfterCRUD]);

  const refreshAfterInventoryChange = useCallback(async () => {
    return refreshAfterCRUD('update', 'inventory');
  }, [refreshAfterCRUD]);

  const refreshAfterSupplierCreate = useCallback(async () => {
    return refreshAfterCRUD('create', 'supplier');
  }, [refreshAfterCRUD]);

  const refreshAfterCategoryCreate = useCallback(async () => {
    return refreshAfterCRUD('create', 'category');
  }, [refreshAfterCRUD]);

  // Wrapper function for CRUD operations with automatic refresh
  const withAutoRefresh = useCallback((operation, dataType, additionalTypes = []) => {
    return async (originalFunction) => {
      try {
        // Execute the original operation
        const result = await originalFunction();
        
        // Trigger automatic refresh after successful operation
        await refreshAfterCRUD(operation, dataType, additionalTypes);
        
        return result;
      } catch (error) {
        // If the original operation fails, don't refresh
        console.error(`❌ Operation failed, skipping auto-refresh:`, error);
        throw error;
      }
    };
  }, [refreshAfterCRUD]);

  return {
    // Generic refresh function
    refreshAfterCRUD,
    
    // Specific refresh functions
    refreshAfterCustomerCreate,
    refreshAfterCustomerUpdate,
    refreshAfterOrderCreate,
    refreshAfterOrderUpdate,
    refreshAfterPaymentCreate,
    refreshAfterPaymentUpdate,
    refreshAfterProductCreate,
    refreshAfterProductUpdate,
    refreshAfterInventoryChange,
    refreshAfterSupplierCreate,
    refreshAfterCategoryCreate,
    
    // Wrapper function for automatic refresh
    withAutoRefresh
  };
};

export default useDataRefresh;
