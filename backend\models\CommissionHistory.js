const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const CommissionHistory = sequelize.define('CommissionHistory', {
  history_id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  salesman_id: {
    type: DataTypes.UUID,
    allowNull: true, // Allow null for system-wide actions like stage updates
    references: {
      model: 'salesmen',
      key: 'salesman_id'
    }
  },
  assignment_id: {
    type: DataTypes.UUID,
    allowNull: true, // Allow null for system-wide actions like stage updates
    references: {
      model: 'commission_assignments',
      key: 'assignment_id'
    }
  },
  stage_id: {
    type: DataTypes.UUID,
    references: {
      model: 'commission_stages',
      key: 'stage_id'
    }
  },
  action_type: {
    type: DataTypes.ENUM(
      'commission_calculated',
      'commission_approved',
      'commission_paid',
      'commission_cancelled',
      'stage_updated',
      'bonus_awarded',
      'adjustment_made'
    ),
    allowNull: false
  },
  previous_values: {
    type: DataTypes.JSONB,
    defaultValue: {}
  },
  new_values: {
    type: DataTypes.JSONB,
    defaultValue: {}
  },
  amount_involved: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  created_by: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'user_id'
    }
  },
  metadata: {
    type: DataTypes.JSONB,
    defaultValue: {}
  }
}, {
  tableName: 'commission_history',
  indexes: [
    {
      fields: ['salesman_id']
    },
    {
      fields: ['assignment_id']
    },
    {
      fields: ['action_type']
    },
    {
      fields: ['created_at']
    }
  ]
});

module.exports = CommissionHistory;
