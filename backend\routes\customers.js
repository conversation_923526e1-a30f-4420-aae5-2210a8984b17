const express = require('express');
const router = express.Router();
const {
  getCustomers,
  getCustomer,
  createCustomer,
  updateCustomer,
  deleteCustomer,
  getCustomerTypes
} = require('../controllers/customerController');
const { validateCustomer, validateUUID } = require('../middleware/validation');
const { authenticateToken, adminOrSalesman } = require('../middleware/auth');

// All routes require authentication
router.use(authenticateToken);

// GET routes
router.get('/', adminOr<PERSON>alesman, getCustomers);
router.get('/types', adminOrSalesman, getCustomerTypes);
router.get('/:id', adminOr<PERSON><PERSON><PERSON>, validateUUID('id'), getCustomer);

// POST/PUT/DELETE routes
router.post('/', adminOr<PERSON><PERSON><PERSON>, validateCustomer, createCustomer);
router.put('/:id', admin<PERSON><PERSON><PERSON><PERSON><PERSON>, validateUUID('id'), validateCustomer, updateCustomer);
router.delete('/:id', admin<PERSON>r<PERSON><PERSON><PERSON>, validateUUID('id'), deleteCustomer);

module.exports = router;
