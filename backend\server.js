const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const chalk = require('chalk');
const moment = require('moment');
require('dotenv').config();

const { testConnection, sequelize } = require('./config/database');
const errorHandler = require('./middleware/errorHandler');

// Import routes
const authRoutes = require('./routes/auth');
const productRoutes = require('./routes/products');
const customerRoutes = require('./routes/customers');
const orderRoutes = require('./routes/orders');
const inventoryRoutes = require('./routes/inventory');
const supplierRoutes = require('./routes/suppliers');
const salesmenRoutes = require('./routes/salesmen');
const paymentRoutes = require('./routes/payments');
const expenseRoutes = require('./routes/expenses');
const employeeRoutes = require('./routes/employees');
const commissionRoutes = require('./routes/commissions');
const adminRoutes = require('./routes/admin');
const monitorRoutes = require('./routes/monitor');

const app = express();
const PORT = process.env.PORT || 3001;

// ============================================================================
// ENHANCED LOGGING & MONITORING SYSTEM
// ============================================================================

// Database query logging (SIMPLIFIED FOR DEVELOPMENT)
if (process.env.ENABLE_QUERY_LOGGING === 'true') {
  sequelize.addHook('beforeQuery', (options) => {
    console.log(`🔍 Query: ${options.sql?.substring(0, 100)}...`);
  });

  sequelize.addHook('afterQuery', (options, result) => {
    console.log(`✅ Query completed`);
  });
}

// API Request/Response logging middleware (SIMPLIFIED)
const apiLogger = (req, res, next) => {
  if (process.env.ENABLE_API_LOGGING === 'true') {
    const method = req.method;
    const url = req.originalUrl;
    console.log(`📡 ${method} ${url}`);

    // Capture response
    const originalSend = res.send;
    res.send = function(data) {
      console.log(`📤 ${method} ${url} - Status: ${res.statusCode}`);
      originalSend.call(this, data);
    };
  }
  next();
};

// Data change monitoring (SIMPLIFIED)
const dataChangeLogger = (operation, table, data, userId = null) => {
  if (process.env.ENABLE_DATA_LOGGING === 'true') {
    console.log(`🔄 ${operation.toUpperCase()} on ${table} ${userId ? `by ${userId}` : ''}`);
  }
};

// Make dataChangeLogger globally available
global.logDataChange = dataChangeLogger;

// Security middleware
app.use(helmet());

// Rate limiting - DEVELOPMENT FRIENDLY
const limiter = rateLimit({
  windowMs: process.env.NODE_ENV === 'development'
    ? 1 * 60 * 1000  // 1 minute in development
    : parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes in production
  max: process.env.NODE_ENV === 'development'
    ? 1000  // 1000 requests per minute in development
    : parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // 100 requests per 15 minutes in production
  message: {
    success: false,
    message: 'Too many requests from this IP, please try again later.'
  },
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
});

// Only apply rate limiting in production or when explicitly enabled
if (process.env.NODE_ENV === 'production' || process.env.ENABLE_RATE_LIMIT === 'true') {
  app.use('/api/', limiter);
  console.log('🛡️ Rate limiting enabled');
} else {
  console.log('⚠️ Rate limiting disabled for development');
}

// CORS configuration
app.use(cors({
  origin: process.env.FRONTEND_URL || ['http://localhost:5173', 'http://localhost:5174', 'http://localhost:5175', 'http://localhost:5176', 'http://localhost:5177'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Compression middleware
app.use(compression());

// Enhanced API logging middleware (only when explicitly enabled)
if (process.env.ENABLE_API_LOGGING === 'true') {
  app.use('/api', apiLogger);
}

// Use simple morgan logging
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('tiny')); // Use 'tiny' instead of 'dev' for less verbose output
} else {
  app.use(morgan('combined'));
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Server is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV
  });
});

// Real-time database monitoring endpoint
app.get('/api/monitor/database', async (req, res) => {
  try {
    const { User, Customer, Product, Order, OrderItem, Payment, Expense, InventoryTransaction, Salesman, Supplier } = require('./models');

    console.log(chalk.blue(`\n📊 [${moment().format('YYYY-MM-DD HH:mm:ss.SSS')}] DATABASE MONITORING REQUEST`));

    const stats = {
      timestamp: new Date().toISOString(),
      tables: {
        users: await User.count(),
        customers: await Customer.count(),
        products: await Product.count(),
        orders: await Order.count(),
        order_items: await OrderItem.count(),
        payments: await Payment.count(),
        expenses: await Expense.count(),
        inventory_transactions: await InventoryTransaction.count(),
        salesmen: await Salesman.count(),
        suppliers: await Supplier.count()
      },
      recent_data: {
        latest_users: await User.findAll({ limit: 5, order: [['created_at', 'DESC']] }),
        latest_customers: await Customer.findAll({ limit: 5, order: [['created_at', 'DESC']] }),
        latest_products: await Product.findAll({ limit: 5, order: [['created_at', 'DESC']] }),
        latest_orders: await Order.findAll({ limit: 5, order: [['created_at', 'DESC']] }),
        latest_payments: await Payment.findAll({ limit: 5, order: [['created_at', 'DESC']] }),
        latest_transactions: await InventoryTransaction.findAll({ limit: 5, order: [['created_at', 'DESC']] }),
        latest_salesmen: await Salesman.findAll({ limit: 5, order: [['created_at', 'DESC']] }),
        latest_suppliers: await Supplier.findAll({ limit: 5, order: [['created_at', 'DESC']] })
      }
    };

    console.log(chalk.green(`\n📊 [${moment().format('YYYY-MM-DD HH:mm:ss.SSS')}] DATABASE MONITORING REQUEST:`));
    console.log(chalk.white(JSON.stringify(stats.tables, null, 2)));

    res.json({ success: true, data: stats });
  } catch (error) {
    console.error(chalk.red('❌ Database monitoring error:'), error);
    res.status(500).json({ success: false, message: error.message });
  }
});

// Real-time data viewer endpoint
app.get('/api/monitor/data/:table', async (req, res) => {
  try {
    const { table } = req.params;
    const { limit = 50, offset = 0 } = req.query;

    const models = {
      users: require('./models').User,
      customers: require('./models').Customer,
      products: require('./models').Product,
      orders: require('./models').Order,
      order_items: require('./models').OrderItem,
      payments: require('./models').Payment,
      expenses: require('./models').Expense,
      inventory_transactions: require('./models').InventoryTransaction,
      salesmen: require('./models').Salesman,
      suppliers: require('./models').Supplier
    };

    const Model = models[table];
    if (!Model) {
      return res.status(400).json({ success: false, message: 'Invalid table name' });
    }

    const data = await Model.findAll({
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['created_at', 'DESC']]
    });

    console.log(chalk.blue(`\n📋 [${moment().format('YYYY-MM-DD HH:mm:ss.SSS')}] DATA VIEWER REQUEST:`));
    console.log(chalk.white(`TABLE: ${table.toUpperCase()}`));
    console.log(chalk.white(`RECORDS: ${data.length}`));

    res.json({ success: true, data, count: data.length });
  } catch (error) {
    console.error(chalk.red('❌ Data viewer error:'), error);
    res.status(500).json({ success: false, message: error.message });
  }
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/products', productRoutes);
app.use('/api/customers', customerRoutes);
app.use('/api/orders', orderRoutes);
app.use('/api/inventory', inventoryRoutes);
app.use('/api/suppliers', supplierRoutes);
app.use('/api/salesmen', salesmenRoutes);
app.use('/api/payments', paymentRoutes);
app.use('/api/expenses', expenseRoutes);
app.use('/api/employees', employeeRoutes);
app.use('/api/commissions', commissionRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/monitor', monitorRoutes);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found'
  });
});

// Error handling middleware (must be last)
app.use(errorHandler);

// Production-ready server startup with full database integration
const startServer = async () => {
  console.log('🚀 Starting production-ready server...');

  try {
    // Test database connection with retry logic
    console.log('🔄 Testing database connection...');
    await testConnection();
    console.log('✅ Database connection established');

    // Sync database models with production-ready error handling
    console.log('🔄 Synchronizing database models...');
    try {
      // Use alter sync to add new category_id column
      await sequelize.sync({
        force: false,
        alter: true, // Enabled to add new category_id column
        logging: false // Disable sync logging to prevent terminal spam
      });
      console.log('✅ Database models synchronized successfully');

      // Log all available tables
      const tables = await sequelize.getQueryInterface().showAllTables();
      console.log('📊 Available database tables:', tables);

      // Verify critical tables exist
      const criticalTables = ['users', 'products', 'customers', 'suppliers', 'orders', 'inventory_transactions'];
      const missingTables = criticalTables.filter(table => !tables.includes(table));

      if (missingTables.length > 0) {
        console.warn('⚠️ Missing critical tables:', missingTables);
        console.log('🔄 Creating missing tables...');

        // Force sync only for missing tables
        await sequelize.sync({ force: false, alter: false });
        console.log('✅ Missing tables created');
      }

    } catch (syncError) {
      console.error('❌ Database sync failed:', syncError.message);

      // Try to recover with a fresh sync
      console.log('🔄 Attempting recovery with fresh sync...');
      try {
        await sequelize.sync({ force: false, alter: false });
        console.log('✅ Database recovery successful');
      } catch (recoveryError) {
        console.error('❌ Database recovery failed:', recoveryError.message);
        throw new Error(`Database synchronization failed: ${syncError.message}`);
      }
    }

    // Add health check route with database status
    app.get('/health', async (req, res) => {
      try {
        // Test database connectivity
        await sequelize.authenticate();
        res.json({
          status: 'OK',
          timestamp: new Date().toISOString(),
          port: PORT,
          database: 'Connected',
          message: 'Backend server and database are healthy!'
        });
      } catch (dbError) {
        res.status(503).json({
          status: 'ERROR',
          timestamp: new Date().toISOString(),
          port: PORT,
          database: 'Disconnected',
          message: 'Database connection failed',
          error: dbError.message
        });
      }
    });

    // Start listening
    const server = app.listen(PORT, () => {
      console.log('');
      console.log('🎉 ================================');
      console.log('✅ PRODUCTION SERVER RUNNING');
      console.log('🎉 ================================');
      console.log(`📍 Port: ${PORT}`);
      console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log(`🔗 API Base URL: http://localhost:${PORT}/api`);
      console.log(`🔗 Health Check: http://localhost:${PORT}/health`);
      console.log(`📁 Database: SQLite (${process.cwd()}/backend/database.sqlite)`);
      console.log('');
      console.log('🚀 Server is ready for production use!');
      console.log('💡 Press Ctrl+C to stop the server');
      console.log('');
    });

    // Enhanced server error handling
    server.on('error', (error) => {
      console.log('');
      console.log('❌ ================================');
      console.log('❌ SERVER STARTUP FAILED');
      console.log('❌ ================================');

      if (error.code === 'EADDRINUSE') {
        console.error(`❌ Port ${PORT} is already in use`);
        console.log('💡 Solutions:');
        console.log('   1. Kill existing processes: taskkill /F /IM node.exe');
        console.log('   2. Use different port: PORT=3002 npm start');
        console.log('   3. Check port usage: netstat -ano | findstr :3001');
      } else {
        console.error('❌ Server error:', error.message);
        console.error('❌ Stack trace:', error.stack);
      }

      console.log('');
      process.exit(1);
    });

  } catch (error) {
    console.error('❌ Failed to start server:', error.message);
    console.error('❌ Stack trace:', error.stack);

    // Provide specific guidance based on error type
    if (error.message.includes('Database')) {
      console.log('💡 Database troubleshooting:');
      console.log('   1. Check if database file exists and is accessible');
      console.log('   2. Verify database permissions');
      console.log('   3. Try deleting database.sqlite to recreate tables');
    }

    process.exit(1);
  }
};

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  console.error('Unhandled Promise Rejection:', err);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  console.error('Uncaught Exception:', err);
  process.exit(1);
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('SIGTERM received, shutting down gracefully');
  await sequelize.close();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('SIGINT received, shutting down gracefully');
  await sequelize.close();
  process.exit(0);
});

startServer();
