@echo off
echo ========================================
echo   INVENTORY MANAGEMENT SYSTEM STARTUP
echo ========================================
echo.

echo 🚀 Starting Backend Server...
echo.
start "Backend Server" cmd /k "cd backend && npm start"

echo ⏳ Waiting for backend to initialize...
timeout /t 5 /nobreak >nul

echo 🌐 Starting Frontend Application...
echo.
start "Frontend App" cmd /k "npm run dev"

echo.
echo ✅ SYSTEM STARTUP COMPLETE!
echo.
echo 📊 Backend Server: http://localhost:3001
echo 🌐 Frontend App: http://localhost:5173
echo 📋 Health Check: http://localhost:3001/health
echo.
echo 💡 Both servers are running in separate windows.
echo 💡 Close the terminal windows to stop the servers.
echo.
pause
