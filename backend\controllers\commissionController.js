const { 
  CommissionStage, 
  CommissionAssignment, 
  CommissionHistory, 
  Salesman, 
  Order, 
  User, 
  CommissionStageOverride 
} = require('../models');
const { Op } = require('sequelize');
const chalk = require('chalk');
const moment = require('moment');
const { Sequelize } = require('sequelize');

// Get all commission stages
const getCommissionStages = async (req, res, next) => {
  try {
    console.log(chalk.blue('📊 Fetching commission stages'));

    // ENHANCEMENT: Debug query to find all stages first
    const allStages = await CommissionStage.findAll({
      order: [['stage_number', 'ASC']]
    });
    console.log(`📊 Total stages in database: ${allStages.length}`);

    // ENHANCEMENT: Return active stages (for now, return all stages since we're debugging)
    const stages = await CommissionStage.findAll({
      where: { is_active: true },
      order: [['stage_number', 'ASC']]
    });

    // If no active stages found, check if there are any stages at all
    if (stages.length === 0) {
      console.log('📊 No active stages found, checking all stages...');
      const allStagesCheck = await CommissionStage.findAll({
        order: [['stage_number', 'ASC']]
      });
      console.log(`📊 All stages (including inactive): ${allStagesCheck.length}`);
      if (allStagesCheck.length > 0) {
        console.log('📊 Found inactive stages, activating them...');
        // Activate all existing stages
        await CommissionStage.update(
          { is_active: true },
          { where: {} }
        );
        // Re-fetch active stages
        const reactivatedStages = await CommissionStage.findAll({
          where: { is_active: true },
          order: [['stage_number', 'ASC']]
        });
        console.log(`📊 Reactivated ${reactivatedStages.length} stages`);
        // Also fetch overrides
        const overrides = await CommissionStageOverride.findAll();
        return res.json({
          success: true,
          data: { stages: reactivatedStages, overrides }
        });
      }
    }

    console.log(`✅ Found ${stages.length} commission stages`);

    // Fetch overrides
    const overrides = await CommissionStageOverride.findAll();

    res.json({
      success: true,
      data: { stages, overrides }
    });
  } catch (error) {
    console.error('❌ Error fetching commission stages:', error);
    next(error);
  }
};

// Create or update commission stages
const updateCommissionStages = async (req, res, next) => {
  try {
    const { stages } = req.body;
    console.log(chalk.green(`📊 Updating commission stages: ${stages.length} stages`));

    // Validate stages
    if (!Array.isArray(stages) || stages.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Stages array is required and cannot be empty'
      });
    }

    // Sort by min_sales_amount
    const sortedStages = [...stages].sort((a, b) => parseFloat(a.min_sales_amount) - parseFloat(b.min_sales_amount));
    for (let i = 0; i < sortedStages.length; i++) {
      const stage = sortedStages[i];
      if (!stage.stage_number || !stage.stage_name || stage.commission_percentage === undefined) {
        return res.status(400).json({
          success: false,
          message: 'Each stage must have stage_number, stage_name, and commission_percentage'
        });
      }
      if (stage.commission_percentage < 0 || stage.commission_percentage > 1) {
        return res.status(400).json({
          success: false,
          message: 'Commission percentage must be between 0 and 1 (0-100%)'
        });
      }
      if (i > 0) {
        const prev = sortedStages[i - 1];
        if (parseFloat(stage.min_sales_amount) <= parseFloat(prev.max_sales_amount || 0)) {
          let nextMin = prev.max_sales_amount !== null ? (parseFloat(prev.max_sales_amount) + 1) : 'the next logical value';
          return res.status(400).json({
            success: false,
            message: `Stage ${stage.stage_number} should start from ${nextMin} or above (previous stage: ${prev.min_sales_amount} - ${prev.max_sales_amount || '∞'})`
          });
        }
      }
    }

    // Deactivate existing stages
    await CommissionStage.update(
      { is_active: false, end_date: new Date() },
      { where: { is_active: true } }
    );

    // Create new stages
    const createdStages = [];
    for (const stageData of stages) {
      const stage = await CommissionStage.create({
        ...stageData,
        created_by: req.user.user_id,
        effective_date: new Date()
      });

      // Create history entry (stage updates don't require salesman_id)
      await CommissionHistory.create({
        salesman_id: null, // Stage updates are not salesman-specific
        stage_id: stage.stage_id,
        action_type: 'stage_updated',
        new_values: stageData,
        description: `Commission stage ${stage.stage_number} created/updated`,
        created_by: req.user.user_id
      });

      createdStages.push(stage);
    }

    console.log(`✅ Created ${createdStages.length} commission stages`);

    // Log data change for monitoring
    if (global.logDataChange) {
      global.logDataChange('UPDATE', 'commission_stages', { count: createdStages.length }, req.user?.user_id);
    }

    res.json({
      success: true,
      data: createdStages,
      message: 'Commission stages updated successfully'
    });
  } catch (error) {
    console.error('❌ Error updating commission stages:', error);
    next(error);
  }
};

// Get commission assignments for a salesman
const getCommissionAssignments = async (req, res, next) => {
  try {
    const { salesmanId } = req.params;
    const {
      page = 1,
      limit = 50,
      status = '',
      startDate = '',
      endDate = ''
    } = req.query;

    console.log(chalk.blue(`💰 Fetching commission assignments for salesman: ${salesmanId}`));

    const offset = (page - 1) * limit;
    const whereClause = { salesman_id: salesmanId };

    if (status) whereClause.status = status;
    if (startDate && endDate) {
      whereClause.calculation_date = {
        [Op.between]: [new Date(startDate), new Date(endDate)]
      };
    }

    const { count, rows: assignments } = await CommissionAssignment.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Salesman,
          as: 'salesman',
          attributes: ['salesman_id', 'full_name']
        },
        {
          model: Order,
          as: 'order',
          attributes: ['order_id', 'order_number', 'total_amount', 'status']
        },
        {
          model: CommissionStage,
          as: 'stage',
          attributes: ['stage_id', 'stage_number', 'stage_name', 'commission_percentage']
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['calculation_date', 'DESC']]
    });

    console.log(`✅ Found ${assignments.length} commission assignments`);

    res.json({
      success: true,
      data: {
        assignments,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / limit),
          totalItems: count,
          itemsPerPage: parseInt(limit)
        }
      }
    });
  } catch (error) {
    console.error('❌ Error fetching commission assignments:', error);
    next(error);
  }
};

// Calculate and assign commission for an order
const calculateCommission = async (req, res, next) => {
  try {
    const { orderId } = req.params;
    console.log(chalk.green(`💰 Calculating commission for order: ${orderId}`));

    // Get order with salesman
    const order = await Order.findByPk(orderId, {
      include: [
        {
          model: Salesman,
          as: 'salesman',
          attributes: ['salesman_id', 'full_name', 'commission_rate']
        }
      ]
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    if (!order.salesman) {
      return res.status(400).json({
        success: false,
        message: 'Order has no assigned salesman'
      });
    }

    if (order.status !== 'delivered') {
      return res.status(400).json({
        success: false,
        message: 'Commission can only be calculated for delivered orders'
      });
    }

    // Check if commission already exists
    const existingAssignment = await CommissionAssignment.findOne({
      where: { order_id: orderId, salesman_id: order.salesman.salesman_id }
    });

    if (existingAssignment) {
      return res.status(400).json({
        success: false,
        message: 'Commission already calculated for this order'
      });
    }

    // Get appropriate commission stage
    const salesAmount = parseFloat(order.total_amount);
    // Find the global stage for this sales amount
    const commissionStage = await CommissionStage.findOne({
      where: {
        is_active: true,
        salesman_id: null,
        min_sales_amount: { [Op.lte]: salesAmount },
        [Op.or]: [
          { max_sales_amount: { [Op.gte]: salesAmount } },
          { max_sales_amount: null }
        ]
      },
      order: [['min_sales_amount', 'DESC']]
    });
    if (!commissionStage) {
      return res.status(400).json({
        success: false,
        message: 'No applicable commission stage found for this sales amount'
      });
    }
    // Check for per-salesman override
    let commissionPercentage = parseFloat(commissionStage.commission_percentage);
    const override = await CommissionStageOverride.findOne({
      where: {
        stage_id: commissionStage.stage_id,
        salesman_id: order.salesman.salesman_id
      }
    });
    if (override) {
      commissionPercentage = parseFloat(override.commission_percentage);
    }

    // Calculate commission
    const commissionAmount = salesAmount * commissionPercentage;
    const bonusAmount = parseFloat(commissionStage.bonus_amount) || 0;
    const totalCommission = commissionAmount + bonusAmount;

    // Create commission assignment
    const assignment = await CommissionAssignment.create({
      salesman_id: order.salesman.salesman_id,
      order_id: orderId,
      stage_id: commissionStage.stage_id,
      sales_amount: salesAmount,
      commission_percentage: commissionPercentage,
      commission_amount: commissionAmount,
      bonus_amount: bonusAmount,
      total_commission: totalCommission,
      period_start: moment().startOf('month').toDate(),
      period_end: moment().endOf('month').toDate(),
      status: 'calculated',
      created_by: req.user.user_id
    });

    // Create history entry
    await CommissionHistory.create({
      salesman_id: order.salesman.salesman_id,
      assignment_id: assignment.assignment_id,
      stage_id: commissionStage.stage_id,
      action_type: 'commission_calculated',
      new_values: {
        sales_amount: salesAmount,
        commission_amount: commissionAmount,
        bonus_amount: bonusAmount,
        total_commission: totalCommission
      },
      amount_involved: totalCommission,
      description: `Commission calculated for order ${order.order_number}`,
      created_by: req.user.user_id
    });

    console.log(`✅ Commission calculated: $${totalCommission.toFixed(2)}`);

    // Log data change for monitoring
    if (global.logDataChange) {
      global.logDataChange('CREATE', 'commission_assignments', assignment, req.user?.user_id);
    }

    res.json({
      success: true,
      data: assignment,
      message: 'Commission calculated successfully'
    });
  } catch (error) {
    console.error('❌ Error calculating commission:', error);
    next(error);
  }
};

// Create a new commission stage (optionally for a specific salesman)
const createCommissionStage = async (req, res, next) => {
  try {
    const {
      stage_number,
      stage_name,
      min_sales_amount,
      max_sales_amount,
      commission_percentage,
      bonus_amount = 0,
      calculation_period = 'monthly',
      eligible_categories = [],
      notes = '',
      salesman_id = null
    } = req.body;

    if (!stage_number || !stage_name || commission_percentage === undefined) {
      return res.status(400).json({
        success: false,
        message: 'stage_number, stage_name, and commission_percentage are required'
      });
    }

    if (commission_percentage < 0 || commission_percentage > 1) {
      return res.status(400).json({
        success: false,
        message: 'Commission percentage must be between 0 and 1 (0-100%)'
      });
    }

    // Validation: No overlap or duplicate range for the same salesman/global
    const existingStages = await CommissionStage.findAll({
      where: { salesman_id },
      order: [['min_sales_amount', 'ASC']]
    });
    for (const stage of existingStages) {
      if (
        (max_sales_amount === null || parseFloat(max_sales_amount) >= parseFloat(stage.min_sales_amount)) &&
        (stage.max_sales_amount === null || parseFloat(min_sales_amount) <= parseFloat(stage.max_sales_amount))
      ) {
        let nextMin = stage.max_sales_amount !== null ? (parseFloat(stage.max_sales_amount) + 1) : 'the next logical value';
        return res.status(400).json({
          success: false,
          message: `This stage should start from ${nextMin} or above (last stage: ${stage.min_sales_amount} - ${stage.max_sales_amount || '∞'})`
        });
      }
    }
    if (existingStages.length > 0) {
      const lastStage = existingStages[existingStages.length - 1];
      if (parseFloat(min_sales_amount) <= parseFloat(lastStage.max_sales_amount || 0)) {
        let nextMin = lastStage.max_sales_amount !== null ? (parseFloat(lastStage.max_sales_amount) + 1) : 'the next logical value';
        return res.status(400).json({
          success: false,
          message: `This stage should start from ${nextMin} or above (last stage: ${lastStage.min_sales_amount} - ${lastStage.max_sales_amount || '∞'})`
        });
      }
    }

    // Validation: No duplicate stage_number within the same group
    const duplicate = await CommissionStage.findOne({
      where: {
        stage_number,
        salesman_id: salesman_id || null
      }
    });
    if (duplicate) {
      return res.status(400).json({
        success: false,
        message: `Duplicate stage_number: '${stage_number}' already exists for this group.`
      });
    }

    const newStage = await CommissionStage.create({
      stage_number,
      stage_name,
      min_sales_amount,
      max_sales_amount,
      commission_percentage,
      bonus_amount,
      calculation_period,
      eligible_categories,
      notes,
      salesman_id,
      is_active: true,
      created_by: req.user.user_id
    });

    res.json({
      success: true,
      data: newStage,
      message: 'Commission stage created successfully'
    });
  } catch (error) {
    console.error('❌ Error creating commission stage:', error);
    next(error);
  }
};

// Create or update a commission percentage override for a salesman and stage
const setCommissionOverride = async (req, res, next) => {
  try {
    const { stage_id, salesman_id, commission_percentage } = req.body;

    console.log(chalk.blue('📊 Setting commission override:'), {
      stage_id,
      salesman_id,
      commission_percentage,
      user_id: req.user?.user_id
    });

    // Input validation
    if (!stage_id || !salesman_id || commission_percentage === undefined) {
      console.log('❌ Missing required fields:', { stage_id, salesman_id, commission_percentage });
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: stage_id, salesman_id, and commission_percentage are required'
      });
    }

    // Validate commission percentage range
    const percentage = parseFloat(commission_percentage);
    if (isNaN(percentage) || percentage < 0 || percentage > 1) {
      console.log('❌ Invalid commission percentage:', commission_percentage);
      return res.status(400).json({
        success: false,
        message: 'Commission percentage must be a number between 0 and 1 (0-100%)'
      });
    }

    // Verify that the stage exists
    const stage = await CommissionStage.findByPk(stage_id);
    if (!stage) {
      console.log('❌ Commission stage not found:', stage_id);
      return res.status(404).json({
        success: false,
        message: 'Commission stage not found'
      });
    }

    // Verify that the salesman exists
    const salesman = await Salesman.findByPk(salesman_id);
    if (!salesman) {
      console.log('❌ Salesman not found:', salesman_id);
      return res.status(404).json({
        success: false,
        message: 'Salesman not found'
      });
    }

    console.log('✅ Validation passed, creating/updating override...');

    // Check if override already exists
    let override = await CommissionStageOverride.findOne({
      where: { stage_id, salesman_id }
    });

    let created = false;
    if (override) {
      console.log('📝 Updating existing override...');
      await override.update({
        commission_percentage: percentage,
        updated_by: req.user.user_id
      });
    } else {
      console.log('📝 Creating new override...');
      override = await CommissionStageOverride.create({
        stage_id,
        salesman_id,
        commission_percentage: percentage,
        created_by: req.user.user_id,
        is_active: true
      });
      created = true;
    }

    console.log('✅ Commission override saved successfully:', {
      override_id: override.override_id,
      created: created
    });

    res.json({
      success: true,
      message: created ? 'Commission override created successfully' : 'Commission override updated successfully',
      data: override
    });
  } catch (error) {
    console.error('❌ Error setting commission override:', error);
    next(error);
  }
};

const getCommissionOverrides = async (req, res, next) => {
  try {
    const overrides = await CommissionStageOverride.findAll({
      include: [
        { 
          model: Salesman, 
          attributes: ['salesman_id', 'name'],
          include: [{ model: User, attributes: ['email'] }]
        },
        { 
          model: CommissionStage, 
          attributes: ['stage_id', 'stage_number', 'target_amount']
        }
      ]
    });
    res.json(overrides);
  } catch (error) {
    next(error);
  }
};

const getCommissionSummary = async (req, res, next) => {
  try {
    console.log(chalk.blue('📊 Fetching commission summary for all salesmen'));
    
    const salesmen = await Salesman.findAll({
      include: [{
        model: Order,
        as: 'orders',
        where: {
          status: 'completed', // Ensure we are looking for completed orders
          // Consider a more robust date range, e.g., based on query params or a defined period
          createdAt: {
            [Op.between]: [
              moment().startOf('month').toDate(),
              moment().endOf('month').toDate()
            ]
          }
        },
        required: false, // Use left join to include salesmen even if they have no orders in the period
        include: [{
          model: CommissionAssignment,
          as: 'commissionAssignments', // Corrected alias
          include: [{ model: CommissionStage, as: 'stage' }] // Assuming 'stage' is the correct alias for CommissionStage in CommissionAssignment
        }]
      }]
    });

    const summary = salesmen.map(salesman => {
      let totalSales = 0;
      let totalCommission = 0;

      if (salesman.orders) {
        salesman.orders.forEach(order => {
          totalSales += parseFloat(order.total_amount || 0);
          if (order.commissionAssignments) {
            order.commissionAssignments.forEach(assignment => {
              totalCommission += parseFloat(assignment.total_commission || 0);
            });
          }
        });
      }
      
      return {
        id: salesman.salesman_id,
        name: salesman.name || salesman.user?.email, // Fallback to email if name is not present
        totalSales: totalSales,
        totalCommission: totalCommission,
        orderCount: salesman.orders ? salesman.orders.length : 0
      };
    });

    res.json({ success: true, data: summary });
  } catch (error) {
    console.error(chalk.red('Error fetching commission summary:'), error);
    next(error);
  }
};

module.exports = {
  getCommissionStages,
  updateCommissionStages,
  setCommissionOverride,
  getCommissionOverrides,
  getCommissionAssignments,
  calculateCommission,
  createCommissionStage,
  getCommissionSummary
};
