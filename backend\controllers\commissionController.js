const {
  CommissionStage,
  CommissionAssignment,
  CommissionHistory,
  Salesman,
  Order,
  User,
  CommissionStageOverride,
  Notification
} = require('../models');
const { sequelize } = require('../config/database');
const { Op } = require('sequelize');
const chalk = require('chalk');
const moment = require('moment');
const { Sequelize } = require('sequelize');

// Auto-order stages by sales range and validate infinity settings
const autoOrderAndValidateStages = async (transaction = null) => {
  // First, get all stages ordered by min_sales_amount
  let stages = await CommissionStage.findAll({
    where: { is_active: true },
    order: [['min_sales_amount', 'ASC']],
    transaction
  });

  console.log(`🔄 Auto-ordering ${stages.length} stages...`);

  // Update stage numbers and validate infinity settings
  for (let i = 0; i < stages.length; i++) {
    const stage = stages[i];
    const isLastStage = i === stages.length - 1;

    let updateData = {
      stage_number: i + 1
    };

    // Handle infinity validation for ALL stages
    if (!stage.max_sales_amount || stage.max_sales_amount === null) {
      // This stage has infinity
      if (!isLastStage) {
        // Not the last stage - must fix infinity
        const nextStage = stages[i + 1];
        if (nextStage) {
          const nextMin = parseFloat(nextStage.min_sales_amount) || 0;
          const currentMin = parseFloat(stage.min_sales_amount) || 0;
          const newMax = Math.max(nextMin - 1, currentMin);

          updateData.max_sales_amount = newMax;

          console.log(`🔧 Auto-adjusted stage "${stage.stage_name}": max changed from ∞ to ${newMax} (next stage "${nextStage.stage_name}" starts at ${nextMin})`);
        }
      } else {
        // Last stage - infinity is allowed
        console.log(`✅ Stage "${stage.stage_name}" is the last stage and can have infinity`);
      }
    } else {
      // Stage has a specific max value - check if it needs adjustment for ordering
      if (!isLastStage) {
        const nextStage = stages[i + 1];
        if (nextStage) {
          const currentMax = parseFloat(stage.max_sales_amount);
          const nextMin = parseFloat(nextStage.min_sales_amount) || 0;

          // If current max is >= next min, adjust it
          if (currentMax >= nextMin) {
            const newMax = Math.max(nextMin - 1, parseFloat(stage.min_sales_amount) || 0);
            updateData.max_sales_amount = newMax;

            console.log(`🔧 Auto-adjusted stage "${stage.stage_name}": max changed from ${currentMax} to ${newMax} to prevent overlap with "${nextStage.stage_name}"`);
          }
        }
      }
    }

    // Update the stage with new data
    await stage.update(updateData, { transaction });
  }

  // Fetch and return the updated stages in correct order
  const updatedStages = await CommissionStage.findAll({
    where: { is_active: true },
    order: [['min_sales_amount', 'ASC']],
    transaction
  });

  console.log(`✅ Auto-ordering complete. Final order:`);
  updatedStages.forEach((stage, index) => {
    const maxDisplay = stage.max_sales_amount ? stage.max_sales_amount : '∞';
    console.log(`   ${index + 1}. ${stage.stage_name}: $${stage.min_sales_amount} - $${maxDisplay}`);
  });

  return updatedStages;
};

// Get all commission stages with automatic ordering
const getCommissionStages = async (req, res, next) => {
  try {
    console.log(chalk.blue('📊 Fetching commission stages with auto-ordering'));

    // Auto-order and validate stages
    const stages = await autoOrderAndValidateStages();

    console.log(`✅ Found ${stages.length} commission stages (auto-ordered by sales range)`);

    // Fetch overrides
    const overrides = await CommissionStageOverride.findAll();

    res.json({
      success: true,
      data: { stages, overrides }
    });
  } catch (error) {
    console.error('❌ Error fetching commission stages:', error);
    next(error);
  }
};

// Create or update commission stages
const updateCommissionStages = async (req, res, next) => {
  try {
    const { stages } = req.body;
    console.log(chalk.green(`📊 Updating commission stages: ${stages.length} stages`));

    // Validate stages
    if (!Array.isArray(stages) || stages.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Stages array is required and cannot be empty'
      });
    }

    // Sort by min_sales_amount
    const sortedStages = [...stages].sort((a, b) => parseFloat(a.min_sales_amount) - parseFloat(b.min_sales_amount));
    for (let i = 0; i < sortedStages.length; i++) {
      const stage = sortedStages[i];
      if (!stage.stage_number || !stage.stage_name || stage.commission_percentage === undefined) {
        return res.status(400).json({
          success: false,
          message: 'Each stage must have stage_number, stage_name, and commission_percentage'
        });
      }
      if (stage.commission_percentage < 0 || stage.commission_percentage > 1) {
        return res.status(400).json({
          success: false,
          message: 'Commission percentage must be between 0 and 1 (0-100%)'
        });
      }
      if (i > 0) {
        const prev = sortedStages[i - 1];
        if (parseFloat(stage.min_sales_amount) <= parseFloat(prev.max_sales_amount || 0)) {
          let nextMin = prev.max_sales_amount !== null ? (parseFloat(prev.max_sales_amount) + 1) : 'the next logical value';
          return res.status(400).json({
            success: false,
            message: `Stage ${stage.stage_number} should start from ${nextMin} or above (previous stage: ${prev.min_sales_amount} - ${prev.max_sales_amount || '∞'})`
          });
        }
      }
    }

    // Deactivate existing stages
    await CommissionStage.update(
      { is_active: false, end_date: new Date() },
      { where: { is_active: true } }
    );

    // Create new stages
    const createdStages = [];
    for (const stageData of stages) {
      const stage = await CommissionStage.create({
        ...stageData,
        created_by: req.user.user_id,
        effective_date: new Date()
      });

      // Create history entry (stage updates don't require salesman_id)
      await CommissionHistory.create({
        salesman_id: null, // Stage updates are not salesman-specific
        stage_id: stage.stage_id,
        action_type: 'stage_updated',
        new_values: stageData,
        description: `Commission stage ${stage.stage_number} created/updated`,
        created_by: req.user.user_id
      });

      createdStages.push(stage);
    }

    console.log(`✅ Created ${createdStages.length} commission stages`);

    // Log data change for monitoring
    if (global.logDataChange) {
      global.logDataChange('UPDATE', 'commission_stages', { count: createdStages.length }, req.user?.user_id);
    }

    res.json({
      success: true,
      data: createdStages,
      message: 'Commission stages updated successfully'
    });
  } catch (error) {
    console.error('❌ Error updating commission stages:', error);
    next(error);
  }
};

// Get commission assignments for a salesman
const getCommissionAssignments = async (req, res, next) => {
  try {
    const { salesmanId } = req.params;
    const {
      page = 1,
      limit = 50,
      status = '',
      startDate = '',
      endDate = ''
    } = req.query;

    console.log(chalk.blue(`💰 Fetching commission assignments for salesman: ${salesmanId}`));

    const offset = (page - 1) * limit;
    const whereClause = { salesman_id: salesmanId };

    if (status) whereClause.status = status;
    if (startDate && endDate) {
      whereClause.calculation_date = {
        [Op.between]: [new Date(startDate), new Date(endDate)]
      };
    }

    const { count, rows: assignments } = await CommissionAssignment.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Salesman,
          as: 'salesman',
          attributes: ['salesman_id', 'full_name']
        },
        {
          model: Order,
          as: 'order',
          attributes: ['order_id', 'order_number', 'total_amount', 'status']
        },
        {
          model: CommissionStage,
          as: 'stage',
          attributes: ['stage_id', 'stage_number', 'stage_name', 'commission_percentage']
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['calculation_date', 'DESC']]
    });

    console.log(`✅ Found ${assignments.length} commission assignments`);

    res.json({
      success: true,
      data: {
        assignments,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / limit),
          totalItems: count,
          itemsPerPage: parseInt(limit)
        }
      }
    });
  } catch (error) {
    console.error('❌ Error fetching commission assignments:', error);
    next(error);
  }
};

// Calculate and assign commission for an order
const calculateCommission = async (req, res, next) => {
  try {
    const { orderId } = req.params;
    console.log(chalk.green(`💰 Calculating commission for order: ${orderId}`));

    // Get order with salesman
    const order = await Order.findByPk(orderId, {
      include: [
        {
          model: Salesman,
          as: 'salesman',
          attributes: ['salesman_id', 'full_name', 'commission_rate']
        }
      ]
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    if (!order.salesman) {
      return res.status(400).json({
        success: false,
        message: 'Order has no assigned salesman'
      });
    }

    if (order.status !== 'delivered') {
      return res.status(400).json({
        success: false,
        message: 'Commission can only be calculated for delivered orders'
      });
    }

    // Check if commission already exists
    const existingAssignment = await CommissionAssignment.findOne({
      where: { order_id: orderId, salesman_id: order.salesman.salesman_id }
    });

    if (existingAssignment) {
      return res.status(400).json({
        success: false,
        message: 'Commission already calculated for this order'
      });
    }

    // Get appropriate commission stage
    const salesAmount = parseFloat(order.total_amount);
    // Find the global stage for this sales amount
    const commissionStage = await CommissionStage.findOne({
      where: {
        is_active: true,
        salesman_id: null,
        min_sales_amount: { [Op.lte]: salesAmount },
        [Op.or]: [
          { max_sales_amount: { [Op.gte]: salesAmount } },
          { max_sales_amount: null }
        ]
      },
      order: [['min_sales_amount', 'DESC']]
    });
    if (!commissionStage) {
      return res.status(400).json({
        success: false,
        message: 'No applicable commission stage found for this sales amount'
      });
    }
    // Check for per-salesman override
    let commissionPercentage = parseFloat(commissionStage.commission_percentage);
    const override = await CommissionStageOverride.findOne({
      where: {
        stage_id: commissionStage.stage_id,
        salesman_id: order.salesman.salesman_id
      }
    });
    if (override) {
      commissionPercentage = parseFloat(override.commission_percentage);
    }

    // Calculate commission
    const commissionAmount = salesAmount * commissionPercentage;
    const bonusAmount = parseFloat(commissionStage.bonus_amount) || 0;
    const totalCommission = commissionAmount + bonusAmount;

    // Create commission assignment
    const assignment = await CommissionAssignment.create({
      salesman_id: order.salesman.salesman_id,
      order_id: orderId,
      stage_id: commissionStage.stage_id,
      sales_amount: salesAmount,
      commission_percentage: commissionPercentage,
      commission_amount: commissionAmount,
      bonus_amount: bonusAmount,
      total_commission: totalCommission,
      period_start: moment().startOf('month').toDate(),
      period_end: moment().endOf('month').toDate(),
      status: 'calculated',
      created_by: req.user.user_id
    });

    // Create history entry
    await CommissionHistory.create({
      salesman_id: order.salesman.salesman_id,
      assignment_id: assignment.assignment_id,
      stage_id: commissionStage.stage_id,
      action_type: 'commission_calculated',
      new_values: {
        sales_amount: salesAmount,
        commission_amount: commissionAmount,
        bonus_amount: bonusAmount,
        total_commission: totalCommission
      },
      amount_involved: totalCommission,
      description: `Commission calculated for order ${order.order_number}`,
      created_by: req.user.user_id
    });

    console.log(`✅ Commission calculated: $${totalCommission.toFixed(2)}`);

    // Log data change for monitoring
    if (global.logDataChange) {
      global.logDataChange('CREATE', 'commission_assignments', assignment, req.user?.user_id);
    }

    res.json({
      success: true,
      data: assignment,
      message: 'Commission calculated successfully'
    });
  } catch (error) {
    console.error('❌ Error calculating commission:', error);
    next(error);
  }
};

// Create a new commission stage with smart range calculation
const createCommissionStage = async (req, res, next) => {
  try {
    const { stage_name, min_sales_amount, max_sales_amount, commission_percentage, insert_position } = req.body;

    console.log(chalk.blue('📝 Creating new commission stage'));

    // Validate required fields
    if (!stage_name || commission_percentage === undefined) {
      return res.status(400).json({
        success: false,
        message: 'Stage name and commission percentage are required'
      });
    }

    // Start transaction
    const transaction = await sequelize.transaction();

    try {
      // Get all existing stages ordered by stage_number
      const existingStages = await CommissionStage.findAll({
        where: { is_active: true },
        order: [['stage_number', 'ASC']],
        transaction
      });

      let newMinAmount, newMaxAmount, newStageNumber;

      if (min_sales_amount !== undefined) {
        // Manual range specification
        newMinAmount = parseFloat(min_sales_amount);
        newMaxAmount = max_sales_amount ? parseFloat(max_sales_amount) : null;
      } else {
        // Smart range calculation
        if (existingStages.length === 0) {
          // First stage
          newMinAmount = 1;
          newMaxAmount = max_sales_amount ? parseFloat(max_sales_amount) : 10000;
        } else if (insert_position !== undefined && insert_position < existingStages.length) {
          // Insert between existing stages
          const insertIndex = parseInt(insert_position);
          const prevStage = insertIndex > 0 ? existingStages[insertIndex - 1] : null;
          const nextStage = existingStages[insertIndex];

          if (prevStage) {
            newMinAmount = parseFloat(prevStage.max_sales_amount) + 1;
          } else {
            newMinAmount = 1;
          }

          newMaxAmount = parseFloat(nextStage.min_sales_amount) - 1;

          if (newMaxAmount <= newMinAmount) {
            await transaction.rollback();
            return res.status(400).json({
              success: false,
              message: 'Cannot insert stage here - insufficient range between existing stages'
            });
          }
        } else {
          // Add as last stage
          const lastStage = existingStages[existingStages.length - 1];
          newMinAmount = lastStage ? parseFloat(lastStage.max_sales_amount) + 1 : 1;
          newMaxAmount = max_sales_amount ? parseFloat(max_sales_amount) : null;
        }
      }

      // Validate ranges
      if (newMinAmount < 0) {
        await transaction.rollback();
        return res.status(400).json({
          success: false,
          message: 'Minimum sales amount cannot be negative'
        });
      }

      if (newMaxAmount && newMaxAmount <= newMinAmount) {
        await transaction.rollback();
        return res.status(400).json({
          success: false,
          message: 'Maximum sales amount must be greater than minimum sales amount'
        });
      }

      // Determine stage number and handle insertions
      if (insert_position !== undefined && insert_position < existingStages.length) {
        newStageNumber = parseInt(insert_position) + 1;

        // Shift existing stages down
        for (let i = parseInt(insert_position); i < existingStages.length; i++) {
          await existingStages[i].update({
            stage_number: existingStages[i].stage_number + 1,
            updated_by: req.user.user_id
          }, { transaction });
        }
      } else {
        // Add as last stage
        const maxStageNumber = await CommissionStage.max('stage_number', {
          where: { is_active: true },
          transaction
        }) || 0;
        newStageNumber = maxStageNumber + 1;
      }

      // Create the new stage
      const newStage = await CommissionStage.create({
        stage_name,
        stage_number: newStageNumber,
        min_sales_amount: newMinAmount,
        max_sales_amount: newMaxAmount,
        commission_percentage: parseFloat(commission_percentage) / 100,
        is_active: true,
        created_by: req.user.user_id
      }, { transaction });

      // Auto-order and validate all stages within the transaction
      const allStages = await autoOrderAndValidateStages(transaction);

      await transaction.commit();

      console.log('✅ Commission stage created successfully with auto-ordering');

      res.status(201).json({
        success: true,
        message: 'Commission stage created successfully',
        data: {
          stage: newStage,
          all_stages: allStages
        }
      });
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.error('❌ Error creating commission stage:', error);
    next(error);
  }
};

// Create or update a commission percentage override for a salesman and stage
const setCommissionOverride = async (req, res, next) => {
  try {
    const { stage_id, salesman_id, commission_percentage } = req.body;

    console.log(chalk.blue('📊 Setting commission override:'), {
      stage_id,
      salesman_id,
      commission_percentage,
      user_id: req.user?.user_id
    });

    // Input validation
    if (!stage_id || !salesman_id || commission_percentage === undefined) {
      console.log('❌ Missing required fields:', { stage_id, salesman_id, commission_percentage });
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: stage_id, salesman_id, and commission_percentage are required'
      });
    }

    // Validate commission percentage range
    const percentage = parseFloat(commission_percentage);
    if (isNaN(percentage) || percentage < 0 || percentage > 1) {
      console.log('❌ Invalid commission percentage:', commission_percentage);
      return res.status(400).json({
        success: false,
        message: 'Commission percentage must be a number between 0 and 1 (0-100%)'
      });
    }

    // Verify that the stage exists
    const stage = await CommissionStage.findByPk(stage_id);
    if (!stage) {
      console.log('❌ Commission stage not found:', stage_id);
      return res.status(404).json({
        success: false,
        message: 'Commission stage not found'
      });
    }

    // Verify that the salesman exists
    const salesman = await Salesman.findByPk(salesman_id);
    if (!salesman) {
      console.log('❌ Salesman not found:', salesman_id);
      return res.status(404).json({
        success: false,
        message: 'Salesman not found'
      });
    }

    console.log('✅ Validation passed, creating/updating override...');

    // Check if override already exists
    let override = await CommissionStageOverride.findOne({
      where: { stage_id, salesman_id }
    });

    let created = false;
    if (override) {
      console.log('📝 Updating existing override...');
      await override.update({
        commission_percentage: percentage,
        updated_by: req.user.user_id
      });
    } else {
      console.log('📝 Creating new override...');
      override = await CommissionStageOverride.create({
        stage_id,
        salesman_id,
        commission_percentage: percentage,
        created_by: req.user.user_id,
        is_active: true
      });
      created = true;
    }

    console.log('✅ Commission override saved successfully:', {
      override_id: override.override_id,
      created: created
    });

    res.json({
      success: true,
      message: created ? 'Commission override created successfully' : 'Commission override updated successfully',
      data: override
    });
  } catch (error) {
    console.error('❌ Error setting commission override:', error);
    next(error);
  }
};

const getCommissionOverrides = async (req, res, next) => {
  try {
    console.log(chalk.blue('📊 Fetching commission overrides'));

    const overrides = await CommissionStageOverride.findAll({
      where: { is_active: true },
      include: [
        {
          model: Salesman,
          as: 'salesman',
          attributes: ['salesman_id', 'full_name']
        },
        {
          model: CommissionStage,
          as: 'stage',
          attributes: ['stage_id', 'stage_number', 'stage_name']
        }
      ],
      order: [['created_at', 'DESC']]
    });

    console.log(`✅ Found ${overrides.length} commission overrides`);

    res.json({
      success: true,
      data: overrides
    });
  } catch (error) {
    console.error('❌ Error fetching commission overrides:', error);
    next(error);
  }
};

const getCommissionSummary = async (req, res, next) => {
  try {
    console.log(chalk.blue('📊 Fetching commission summary for all salesmen'));
    
    const salesmen = await Salesman.findAll({
      include: [{
        model: Order,
        as: 'orders',
        where: {
          status: 'completed', // Ensure we are looking for completed orders
          // Consider a more robust date range, e.g., based on query params or a defined period
          createdAt: {
            [Op.between]: [
              moment().startOf('month').toDate(),
              moment().endOf('month').toDate()
            ]
          }
        },
        required: false, // Use left join to include salesmen even if they have no orders in the period
        include: [{
          model: CommissionAssignment,
          as: 'commissionAssignments', // Corrected alias
          include: [{ model: CommissionStage, as: 'stage' }] // Assuming 'stage' is the correct alias for CommissionStage in CommissionAssignment
        }]
      }]
    });

    const summary = salesmen.map(salesman => {
      let totalSales = 0;
      let totalCommission = 0;

      if (salesman.orders) {
        salesman.orders.forEach(order => {
          totalSales += parseFloat(order.total_amount || 0);
          if (order.commissionAssignments) {
            order.commissionAssignments.forEach(assignment => {
              totalCommission += parseFloat(assignment.total_commission || 0);
            });
          }
        });
      }
      
      return {
        id: salesman.salesman_id,
        name: salesman.name || salesman.user?.email, // Fallback to email if name is not present
        totalSales: totalSales,
        totalCommission: totalCommission,
        orderCount: salesman.orders ? salesman.orders.length : 0
      };
    });

    res.json({ success: true, data: summary });
  } catch (error) {
    console.error(chalk.red('Error fetching commission summary:'), error);
    next(error);
  }
};

// Get monthly commission summary with proper commission calculations
const getMonthlySummary = async (req, res, next) => {
  try {
    const { month, year } = req.query;
    const targetMonth = month ? parseInt(month) : moment().month() + 1;
    const targetYear = year ? parseInt(year) : moment().year();

    console.log(chalk.blue(`📊 Fetching monthly commission summary for ${targetYear}-${targetMonth}`));

    const startDate = moment().year(targetYear).month(targetMonth - 1).startOf('month').toDate();
    const endDate = moment().year(targetYear).month(targetMonth - 1).endOf('month').toDate();

    // Get all salesmen with their orders for the specified month
    const salesmen = await Salesman.findAll({
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['email']
        },
        {
          model: Order,
          as: 'orders',
          where: {
            status: 'delivered',
            created_at: {
              [Op.between]: [startDate, endDate]
            }
          },
          required: false
        }
      ]
    });

    // Get all commission stages and overrides
    const commissionStages = await CommissionStage.findAll({
      where: { is_active: true },
      order: [['min_sales_amount', 'ASC']]
    });

    const commissionOverrides = await CommissionStageOverride.findAll({
      where: { is_active: true }
    });

    // Create override map for quick lookup
    const overrideMap = commissionOverrides.reduce((map, override) => {
      const key = `${override.stage_id}-${override.salesman_id}`;
      map[key] = override.commission_percentage;
      return map;
    }, {});

    const summary = [];
    const stageProgressionNotifications = [];

    for (const salesman of salesmen) {
      const orders = salesman.orders || [];
      const totalSales = orders.reduce((sum, order) => sum + parseFloat(order.total_amount || 0), 0);

      // Find appropriate commission stage based on total sales
      let applicableStage = null;
      for (const stage of commissionStages) {
        if (totalSales >= parseFloat(stage.min_sales_amount)) {
          if (!stage.max_sales_amount || totalSales <= parseFloat(stage.max_sales_amount)) {
            applicableStage = stage;
            break;
          }
        }
      }

      let commissionPercentage = 0;
      let isOverride = false;

      if (applicableStage) {
        // Check for override first
        const overrideKey = `${applicableStage.stage_id}-${salesman.salesman_id}`;
        if (overrideMap[overrideKey]) {
          commissionPercentage = parseFloat(overrideMap[overrideKey]);
          isOverride = true;
        } else {
          commissionPercentage = parseFloat(applicableStage.commission_percentage);
        }
      }

      const totalCommission = totalSales * commissionPercentage;

      // Calculate progress within current stage and toward next stage
      let progressToNextStage = 0;
      let currentStageProgress = 0;
      let nextStageTarget = null;

      if (applicableStage) {
        const stageMin = parseFloat(applicableStage.min_sales_amount);
        const stageMax = applicableStage.max_sales_amount ? parseFloat(applicableStage.max_sales_amount) : null;

        // Calculate progress within current stage
        if (stageMax) {
          const stageRange = stageMax - stageMin;
          const salesInStage = totalSales - stageMin;
          currentStageProgress = Math.min((salesInStage / stageRange) * 100, 100);
        } else {
          // If no max (highest stage), show 100% if they've reached the minimum
          currentStageProgress = totalSales >= stageMin ? 100 : 0;
        }

        // Calculate progress toward next stage
        const nextStage = commissionStages.find(stage =>
          parseFloat(stage.min_sales_amount) > parseFloat(applicableStage.min_sales_amount)
        );

        if (nextStage) {
          nextStageTarget = parseFloat(nextStage.min_sales_amount);
          progressToNextStage = Math.min((totalSales / nextStageTarget) * 100, 100);
        } else {
          progressToNextStage = 100; // Already at highest stage
        }
      }

      // Check if salesman has exceeded their current highest stage
      const highestStage = commissionStages[commissionStages.length - 1];
      if (applicableStage && highestStage &&
          applicableStage.stage_id === highestStage.stage_id &&
          totalSales > parseFloat(highestStage.max_sales_amount || Infinity)) {

        // Queue stage progression notification for creation
        stageProgressionNotifications.push({
          salesmanData: {
            salesman_id: salesman.salesman_id,
            name: salesman.full_name || salesman.user?.email || 'Unknown'
          },
          stageData: applicableStage,
          salesAmount: totalSales
        });
      }

      summary.push({
        salesman_id: salesman.salesman_id,
        name: salesman.full_name || salesman.user?.email || 'Unknown',
        orderCount: orders.length,
        totalSales: totalSales,
        commissionPercentage: commissionPercentage,
        totalCommission: totalCommission,
        isOverride: isOverride,
        currentStage: applicableStage ? {
          stage_id: applicableStage.stage_id,
          stage_name: applicableStage.stage_name,
          stage_number: applicableStage.stage_number,
          min_sales_amount: parseFloat(applicableStage.min_sales_amount),
          max_sales_amount: applicableStage.max_sales_amount ? parseFloat(applicableStage.max_sales_amount) : null
        } : null,
        currentStageProgress: currentStageProgress,
        progressToNextStage: progressToNextStage,
        nextStageTarget: nextStageTarget
      });
    }

    // Create stage progression notifications
    for (const notification of stageProgressionNotifications) {
      try {
        await Notification.createStageProgressionNotification(
          notification.salesmanData,
          notification.stageData,
          notification.salesAmount
        );
        console.log(`🚨 Stage progression notification created for ${notification.salesmanData.name}`);
      } catch (notificationError) {
        console.error('❌ Failed to create stage progression notification:', notificationError);
      }
    }

    console.log(`✅ Generated monthly summary for ${summary.length} salesmen`);

    res.json({
      success: true,
      data: {
        month: targetMonth,
        year: targetYear,
        summary: summary
      }
    });
  } catch (error) {
    console.error('❌ Error fetching monthly commission summary:', error);
    next(error);
  }
};

// Get commission history with filtering and pagination
const getCommissionHistory = async (req, res, next) => {
  try {
    const {
      startDate,
      endDate,
      salesmanId,
      page = 1,
      limit = 50,
      sortBy = 'created_at',
      sortOrder = 'DESC'
    } = req.query;

    console.log(chalk.blue('📊 Fetching commission history'));

    const offset = (page - 1) * limit;
    const whereClause = {};

    // Date filtering
    if (startDate && endDate) {
      whereClause.created_at = {
        [Op.between]: [new Date(startDate), new Date(endDate)]
      };
    } else {
      // Default to last 12 months
      whereClause.created_at = {
        [Op.gte]: moment().subtract(12, 'months').toDate()
      };
    }

    // Salesman filtering
    if (salesmanId) {
      whereClause.salesman_id = salesmanId;
    }

    const { count, rows: assignments } = await CommissionAssignment.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Salesman,
          as: 'salesman',
          attributes: ['salesman_id', 'full_name'],
          include: [{ model: User, as: 'user', attributes: ['email'] }]
        },
        {
          model: Order,
          as: 'order',
          attributes: ['order_id', 'order_number', 'total_amount', 'status', 'created_at']
        },
        {
          model: CommissionStage,
          as: 'stage',
          attributes: ['stage_id', 'stage_number', 'stage_name']
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [[sortBy, sortOrder.toUpperCase()]]
    });

    const history = assignments.map(assignment => ({
      assignment_id: assignment.assignment_id,
      date: assignment.created_at,
      salesman: {
        id: assignment.salesman.salesman_id,
        name: assignment.salesman.full_name || assignment.salesman.user?.email || 'Unknown'
      },
      order: {
        id: assignment.order.order_id,
        number: assignment.order.order_number,
        amount: parseFloat(assignment.order.total_amount),
        status: assignment.order.status,
        date: assignment.order.created_at
      },
      stage: assignment.stage ? {
        id: assignment.stage.stage_id,
        name: assignment.stage.stage_name,
        number: assignment.stage.stage_number
      } : null,
      salesAmount: parseFloat(assignment.sales_amount),
      commissionPercentage: parseFloat(assignment.commission_percentage),
      commissionAmount: parseFloat(assignment.commission_amount),
      bonusAmount: parseFloat(assignment.bonus_amount || 0),
      totalCommission: parseFloat(assignment.total_commission),
      status: assignment.status
    }));

    console.log(`✅ Found ${history.length} commission history records`);

    res.json({
      success: true,
      data: {
        history: history,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / limit),
          totalItems: count,
          itemsPerPage: parseInt(limit)
        }
      }
    });
  } catch (error) {
    console.error('❌ Error fetching commission history:', error);
    next(error);
  }
};

// Export commission data to CSV
const exportCommissionData = async (req, res, next) => {
  try {
    const { startDate, endDate, format = 'csv' } = req.query;

    console.log(chalk.blue(`📊 Exporting commission data (${format})`));

    const whereClause = {};
    if (startDate && endDate) {
      whereClause.created_at = {
        [Op.between]: [new Date(startDate), new Date(endDate)]
      };
    } else {
      // Default to last 12 months
      whereClause.created_at = {
        [Op.gte]: moment().subtract(12, 'months').toDate()
      };
    }

    const assignments = await CommissionAssignment.findAll({
      where: whereClause,
      include: [
        {
          model: Salesman,
          as: 'salesman',
          attributes: ['salesman_id', 'full_name'],
          include: [{ model: User, as: 'user', attributes: ['email'] }]
        },
        {
          model: Order,
          as: 'order',
          attributes: ['order_id', 'order_number', 'total_amount', 'status', 'created_at']
        },
        {
          model: CommissionStage,
          as: 'stage',
          attributes: ['stage_id', 'stage_number', 'stage_name']
        }
      ],
      order: [['created_at', 'DESC']]
    });

    if (format === 'csv') {
      // Generate CSV content
      const csvHeaders = [
        'Date',
        'Salesman Name',
        'Order Number',
        'Order Date',
        'Sales Amount',
        'Commission Stage',
        'Commission %',
        'Commission Amount',
        'Bonus Amount',
        'Total Commission',
        'Status'
      ];

      const csvRows = assignments.map(assignment => [
        moment(assignment.created_at).format('YYYY-MM-DD'),
        assignment.salesman.full_name || assignment.salesman.user?.email || 'Unknown',
        assignment.order.order_number,
        moment(assignment.order.created_at).format('YYYY-MM-DD'),
        parseFloat(assignment.sales_amount).toFixed(2),
        assignment.stage ? assignment.stage.stage_name : 'N/A',
        (parseFloat(assignment.commission_percentage) * 100).toFixed(2) + '%',
        parseFloat(assignment.commission_amount).toFixed(2),
        parseFloat(assignment.bonus_amount || 0).toFixed(2),
        parseFloat(assignment.total_commission).toFixed(2),
        assignment.status
      ]);

      const csvContent = [csvHeaders, ...csvRows]
        .map(row => row.map(field => `"${field}"`).join(','))
        .join('\n');

      const filename = `commission_report_${moment().format('YYYY-MM-DD')}.csv`;

      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.send(csvContent);
    } else {
      res.status(400).json({
        success: false,
        message: 'Unsupported export format. Only CSV is currently supported.'
      });
    }

    console.log(`✅ Exported ${assignments.length} commission records`);
  } catch (error) {
    console.error('❌ Error exporting commission data:', error);
    next(error);
  }
};

// Monthly commission reset functionality
const processMonthlyReset = async (req, res, next) => {
  try {
    const { month, year } = req.body;
    const targetMonth = month || moment().month() + 1;
    const targetYear = year || moment().year();

    console.log(chalk.blue(`🔄 Processing monthly commission reset for ${targetYear}-${targetMonth}`));

    // Start transaction
    const transaction = await sequelize.transaction();

    try {
      // 1. Archive current month's commission data to history
      const startDate = moment().year(targetYear).month(targetMonth - 1).startOf('month').toDate();
      const endDate = moment().year(targetYear).month(targetMonth - 1).endOf('month').toDate();

      // Get all commission assignments for the month
      const assignments = await CommissionAssignment.findAll({
        where: {
          created_at: {
            [Op.between]: [startDate, endDate]
          }
        },
        include: [
          { model: Salesman, as: 'salesman' },
          { model: Order, as: 'order' },
          { model: CommissionStage, as: 'stage' }
        ],
        transaction
      });

      console.log(`📊 Found ${assignments.length} commission assignments to archive`);

      // 2. Create commission history records
      const historyRecords = assignments.map(assignment => ({
        assignment_id: assignment.assignment_id,
        salesman_id: assignment.salesman_id,
        stage_id: assignment.stage_id,
        action_type: 'commission_calculated',
        previous_values: {},
        new_values: {
          sales_amount: assignment.sales_amount,
          commission_percentage: assignment.commission_percentage,
          commission_amount: assignment.commission_amount,
          bonus_amount: assignment.bonus_amount,
          total_commission: assignment.total_commission,
          status: assignment.status
        },
        amount_involved: assignment.total_commission,
        description: `Monthly commission archived for ${moment().year(targetYear).month(targetMonth - 1).format('MMMM YYYY')}`,
        created_by: req.user.user_id,
        metadata: {
          month: targetMonth,
          year: targetYear,
          archived_at: new Date(),
          order_id: assignment.order_id
        }
      }));

      if (historyRecords.length > 0) {
        await CommissionHistory.bulkCreate(historyRecords, { transaction });
        console.log(`✅ Archived ${historyRecords.length} commission records to history`);
      }

      // 3. Reset monthly sales totals (this would depend on your sales tracking implementation)
      // For now, we'll just mark the reset as completed

      // 4. Create a system notification about the reset
      await Notification.create({
        type: 'system_alert',
        title: 'Monthly Commission Reset Completed',
        message: `Commission data for ${moment().year(targetYear).month(targetMonth - 1).format('MMMM YYYY')} has been archived and monthly totals have been reset.`,
        data: {
          month: targetMonth,
          year: targetYear,
          assignments_archived: assignments.length,
          reset_date: new Date()
        },
        priority: 'medium',
        target_user_id: null, // For all admin users
        created_by: req.user.user_id
      }, { transaction });

      await transaction.commit();

      console.log('✅ Monthly commission reset completed successfully');

      res.json({
        success: true,
        message: 'Monthly commission reset completed successfully',
        data: {
          month: targetMonth,
          year: targetYear,
          assignments_archived: assignments.length,
          reset_date: new Date()
        }
      });
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.error('❌ Error processing monthly commission reset:', error);
    next(error);
  }
};

// Check if monthly reset is needed
const checkMonthlyResetStatus = async (req, res, next) => {
  try {
    const currentMonth = moment().month() + 1;
    const currentYear = moment().year();

    // Check if there are any commission assignments for the current month
    const startDate = moment().startOf('month').toDate();
    const endDate = moment().endOf('month').toDate();

    const currentMonthAssignments = await CommissionAssignment.count({
      where: {
        created_at: {
          [Op.between]: [startDate, endDate]
        }
      }
    });

    // Check if last month's data has been archived
    const lastMonth = moment().subtract(1, 'month');
    const lastMonthStart = lastMonth.startOf('month').toDate();
    const lastMonthEnd = lastMonth.endOf('month').toDate();

    const lastMonthAssignments = await CommissionAssignment.count({
      where: {
        created_at: {
          [Op.between]: [lastMonthStart, lastMonthEnd]
        }
      }
    });

    // Check for archived records by looking for monthly reset actions in history
    const lastMonthArchived = await CommissionHistory.count({
      where: {
        action_type: 'commission_calculated',
        created_at: {
          [Op.between]: [lastMonthStart, lastMonthEnd]
        }
      }
    });

    res.json({
      success: true,
      data: {
        current_month: currentMonth,
        current_year: currentYear,
        current_month_assignments: currentMonthAssignments,
        last_month_assignments: lastMonthAssignments,
        last_month_archived: lastMonthArchived,
        reset_needed: lastMonthAssignments > 0 && lastMonthArchived === 0
      }
    });
  } catch (error) {
    console.error('❌ Error checking monthly reset status:', error);
    next(error);
  }
};

// Update a single commission stage with automatic range adjustments
const updateCommissionStage = async (req, res, next) => {
  try {
    const { stageId } = req.params;
    const { stage_name, min_sales_amount, max_sales_amount, commission_percentage } = req.body;

    console.log(chalk.blue(`📝 Updating commission stage ${stageId}`));

    // Start transaction for atomic updates
    const transaction = await sequelize.transaction();

    try {
      // Get the current stage
      const currentStage = await CommissionStage.findByPk(stageId, { transaction });
      if (!currentStage) {
        await transaction.rollback();
        return res.status(404).json({
          success: false,
          message: 'Commission stage not found'
        });
      }

      // Get all stages ordered by stage_number
      const allStages = await CommissionStage.findAll({
        where: { is_active: true },
        order: [['stage_number', 'ASC']],
        transaction
      });

      const currentStageIndex = allStages.findIndex(s => s.stage_id === stageId);
      const newMinAmount = parseFloat(min_sales_amount);
      const newMaxAmount = max_sales_amount ? parseFloat(max_sales_amount) : null;

      // Validate the new range
      if (newMinAmount < 0) {
        await transaction.rollback();
        return res.status(400).json({
          success: false,
          message: 'Minimum sales amount cannot be negative'
        });
      }

      if (newMaxAmount && newMaxAmount <= newMinAmount) {
        await transaction.rollback();
        return res.status(400).json({
          success: false,
          message: 'Maximum sales amount must be greater than minimum sales amount'
        });
      }

      // Update the current stage
      await currentStage.update({
        stage_name: stage_name || currentStage.stage_name,
        min_sales_amount: newMinAmount,
        max_sales_amount: newMaxAmount,
        commission_percentage: commission_percentage !== undefined ? parseFloat(commission_percentage) / 100 : currentStage.commission_percentage,
        updated_by: req.user.user_id
      }, { transaction });

      // Auto-adjust adjacent stages
      const stagesToUpdate = [];

      // Adjust previous stage's max_sales_amount
      if (currentStageIndex > 0) {
        const previousStage = allStages[currentStageIndex - 1];
        const newPreviousMax = newMinAmount - 1;

        if (newPreviousMax >= parseFloat(previousStage.min_sales_amount)) {
          stagesToUpdate.push({
            stage: previousStage,
            updates: { max_sales_amount: newPreviousMax }
          });
        }
      }

      // Adjust next stage's min_sales_amount
      if (currentStageIndex < allStages.length - 1 && newMaxAmount) {
        const nextStage = allStages[currentStageIndex + 1];
        const newNextMin = newMaxAmount + 1;

        stagesToUpdate.push({
          stage: nextStage,
          updates: { min_sales_amount: newNextMin }
        });

        // Cascade adjustments to subsequent stages
        for (let i = currentStageIndex + 1; i < allStages.length - 1; i++) {
          const stage = allStages[i];
          const nextStageInLoop = allStages[i + 1];

          // If this stage has a max_sales_amount, adjust the next stage's min
          if (stage.max_sales_amount) {
            const adjustedMax = i === currentStageIndex + 1 ? newMaxAmount : stage.max_sales_amount;
            stagesToUpdate.push({
              stage: nextStageInLoop,
              updates: { min_sales_amount: adjustedMax + 1 }
            });
          }
        }
      }

      // Apply all updates
      for (const { stage, updates } of stagesToUpdate) {
        await stage.update({
          ...updates,
          updated_by: req.user.user_id
        }, { transaction });
      }

      // Auto-order and validate all stages within the transaction
      const updatedStages = await autoOrderAndValidateStages(transaction);

      await transaction.commit();

      console.log('✅ Commission stage updated successfully with auto-ordering');

      res.json({
        success: true,
        message: 'Commission stage updated successfully',
        data: {
          stages: updatedStages,
          adjustments_made: stagesToUpdate.length
        }
      });
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.error('❌ Error updating commission stage:', error);
    next(error);
  }
};

// Delete a commission stage
const deleteCommissionStage = async (req, res, next) => {
  try {
    const { stageId } = req.params;

    console.log(chalk.blue(`🗑️ Deleting commission stage ${stageId}`));

    // Start transaction
    const transaction = await sequelize.transaction();

    try {
      // Check if stage exists
      const stage = await CommissionStage.findByPk(stageId, { transaction });
      if (!stage) {
        await transaction.rollback();
        return res.status(404).json({
          success: false,
          message: 'Commission stage not found'
        });
      }

      // Check if stage is being used in assignments
      const assignmentCount = await CommissionAssignment.count({
        where: { stage_id: stageId },
        transaction
      });

      if (assignmentCount > 0) {
        await transaction.rollback();
        return res.status(400).json({
          success: false,
          message: `Cannot delete stage. It is currently used in ${assignmentCount} commission assignments.`
        });
      }

      // Soft delete the stage
      await stage.update({
        is_active: false,
        updated_by: req.user.user_id
      }, { transaction });

      // Get remaining stages and reorder them
      const remainingStages = await CommissionStage.findAll({
        where: { is_active: true },
        order: [['stage_number', 'ASC']],
        transaction
      });

      // Renumber stages to maintain sequence
      for (let i = 0; i < remainingStages.length; i++) {
        await remainingStages[i].update({
          stage_number: i + 1,
          updated_by: req.user.user_id
        }, { transaction });
      }

      await transaction.commit();

      console.log('✅ Commission stage deleted successfully');

      res.json({
        success: true,
        message: 'Commission stage deleted successfully',
        data: {
          deleted_stage_id: stageId,
          remaining_stages: remainingStages.length
        }
      });
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.error('❌ Error deleting commission stage:', error);
    next(error);
  }
};

// Reorder commission stages
const reorderCommissionStages = async (req, res, next) => {
  try {
    const { stageOrder } = req.body; // Array of stage IDs in new order

    console.log(chalk.blue('🔄 Reordering commission stages'));

    if (!Array.isArray(stageOrder) || stageOrder.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Invalid stage order provided'
      });
    }

    // Start transaction
    const transaction = await sequelize.transaction();

    try {
      // Update stage numbers based on new order
      for (let i = 0; i < stageOrder.length; i++) {
        await CommissionStage.update({
          stage_number: i + 1,
          updated_by: req.user.user_id
        }, {
          where: { stage_id: stageOrder[i] },
          transaction
        });
      }

      await transaction.commit();

      // Fetch reordered stages
      const reorderedStages = await CommissionStage.findAll({
        where: { is_active: true },
        order: [['stage_number', 'ASC']]
      });

      console.log('✅ Commission stages reordered successfully');

      res.json({
        success: true,
        message: 'Commission stages reordered successfully',
        data: {
          stages: reorderedStages
        }
      });
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.error('❌ Error reordering commission stages:', error);
    next(error);
  }
};

module.exports = {
  getCommissionStages,
  updateCommissionStages,
  setCommissionOverride,
  getCommissionOverrides,
  getCommissionAssignments,
  calculateCommission,
  createCommissionStage,
  getCommissionSummary,
  getMonthlySummary,
  getCommissionHistory,
  exportCommissionData,
  processMonthlyReset,
  checkMonthlyResetStatus,
  updateCommissionStage,
  deleteCommissionStage,
  reorderCommissionStages
};
