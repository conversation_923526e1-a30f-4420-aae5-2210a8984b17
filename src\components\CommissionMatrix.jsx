import React, { useState, useEffect, useMemo } from 'react';
import {
  EditIcon,
  SaveIcon,
  XIcon,
  CheckIcon,
  AlertCircleIcon,
  SearchIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  FilterIcon,
  UsersIcon,
  ExpandIcon,
  MinimizeIcon
} from 'lucide-react';
import isEqual from 'lodash.isequal';

const CommissionMatrix = ({
  stages = [],
  salesmen = [],
  overrides = [],
  onUpdateOverride,
  loading = false,
  user
}) => {
  const [editingCell, setEditingCell] = useState(null);
  const [editValue, setEditValue] = useState('');
  const [savingCell, setSavingCell] = useState(null);

  // Search and pagination state
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [salesmenPerPage, setSalesmenPerPage] = useState(5);
  const [isExpanded, setIsExpanded] = useState(false);

  // Debug: Log when overrides change
  useEffect(() => {
    console.log('🔄 CommissionMatrix received overrides:', overrides?.length || 0);
  }, [overrides]);

  // Defensive: always use the correct array for stages
  stages = (stages && Array.isArray(stages.stages)) ? stages.stages : (Array.isArray(stages) ? stages : []);

  // Create a map of overrides for quick lookup
  const overrideMap = useMemo(() => {
    return (overrides || []).reduce((map, override) => {
      const key = `${override.stage_id}-${override.salesman_id}`;
      map[key] = override.commission_percentage;
      return map;
    }, {});
  }, [overrides]);

  // Filter and paginate salesmen
  const filteredSalesmen = useMemo(() => {
    if (!searchTerm) return salesmen;
    return salesmen.filter(salesman =>
      salesman.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      salesman.user?.email?.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [salesmen, searchTerm]);

  const paginatedSalesmen = useMemo(() => {
    if (isExpanded || filteredSalesmen.length <= salesmenPerPage) {
      return filteredSalesmen;
    }
    const startIndex = (currentPage - 1) * salesmenPerPage;
    return filteredSalesmen.slice(startIndex, startIndex + salesmenPerPage);
  }, [filteredSalesmen, currentPage, salesmenPerPage, isExpanded]);

  const totalPages = Math.ceil(filteredSalesmen.length / salesmenPerPage);
  const shouldShowPagination = !isExpanded && filteredSalesmen.length > salesmenPerPage;

  // Get commission percentage for a stage-salesman combination
  const getCommissionPercentage = (stageId, salesmanId) => {
    const key = `${stageId}-${salesmanId}`;
    const override = overrideMap[key];

    if (override !== undefined) {
      // Override is stored as decimal (0.02), convert to percentage (2)
      return override * 100;
    }

    // Find the stage and return default percentage
    const stage = stages.find(s => s.stage_id === stageId);
    // Stage commission_percentage is stored as decimal (0.02), convert to percentage (2)
    return stage ? stage.commission_percentage * 100 : 0;
  };

  // Check if a percentage is overridden (custom)
  const isOverridden = (stageId, salesmanId) => {
    const key = `${stageId}-${salesmanId}`;
    return overrideMap[key] !== undefined;
  };

  // Start editing a cell
  const startEditing = (stageId, salesmanId) => {
    if (user?.role !== 'admin') return;
    
    const currentPercentage = getCommissionPercentage(stageId, salesmanId);
    setEditingCell(`${stageId}-${salesmanId}`);
    setEditValue(currentPercentage.toString());
  };

  // Cancel editing
  const cancelEditing = () => {
    setEditingCell(null);
    setEditValue('');
  };

  // Save the edited value
  const saveEdit = async (stageId, salesmanId) => {
    const cellKey = `${stageId}-${salesmanId}`;
    
    try {
      setSavingCell(cellKey);
      
      const percentage = parseFloat(editValue);
      if (isNaN(percentage) || percentage < 0 || percentage > 100) {
        alert('Please enter a valid percentage between 0 and 100');
        return;
      }

      // Convert percentage to decimal for backend
      const decimalPercentage = percentage / 100;
      
      await onUpdateOverride({
        stage_id: stageId,
        salesman_id: salesmanId,
        commission_percentage: decimalPercentage
      });

      setEditingCell(null);
      setEditValue('');
    } catch (error) {
      console.error('Error saving commission override:', error);
      alert('Failed to save commission override. Please try again.');
    } finally {
      setSavingCell(null);
    }
  };

  // Handle key press in edit input
  const handleKeyPress = (e, stageId, salesmanId) => {
    if (e.key === 'Enter') {
      saveEdit(stageId, salesmanId);
    } else if (e.key === 'Escape') {
      cancelEditing();
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">Loading commission matrix...</span>
        </div>
      </div>
    );
  }

  if (stages.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="text-center py-8">
          <AlertCircleIcon size={48} className="mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Commission Stages</h3>
          <p className="text-gray-500">No commission stages have been created yet.</p>
        </div>
      </div>
    );
  }

  if (salesmen.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="text-center py-8">
          <AlertCircleIcon size={48} className="mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Salesmen</h3>
          <p className="text-gray-500">No salesmen found. Please add salesmen to view the commission matrix.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg overflow-hidden">
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-800 flex items-center">
              <UsersIcon size={24} className="mr-2 text-blue-600" />
              Commission Matrix
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              Global commission stages with per-salesman overrides.
              {user?.role === 'admin' && ' Click any percentage to edit.'}
            </p>
          </div>

          <div className="flex items-center space-x-3">
            <span className="text-sm text-gray-500">
              {filteredSalesmen.length} of {salesmen.length} salesmen
            </span>

            {filteredSalesmen.length > 3 && (
              <button
                onClick={() => setIsExpanded(!isExpanded)}
                className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {isExpanded ? (
                  <>
                    <MinimizeIcon size={16} className="mr-2" />
                    Collapse
                  </>
                ) : (
                  <>
                    <ExpandIcon size={16} className="mr-2" />
                    Expand All
                  </>
                )}
              </button>
            )}
          </div>
        </div>

        {/* Search and Filter Controls */}
        {salesmen.length > 3 && (
          <div className="mt-4 flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <SearchIcon size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search salesmen..."
                  value={searchTerm}
                  onChange={(e) => {
                    setSearchTerm(e.target.value);
                    setCurrentPage(1);
                  }}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {!isExpanded && (
                <select
                  value={salesmenPerPage}
                  onChange={(e) => {
                    setSalesmenPerPage(parseInt(e.target.value));
                    setCurrentPage(1);
                  }}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value={3}>3 per page</option>
                  <option value={5}>5 per page</option>
                  <option value={10}>10 per page</option>
                  <option value={20}>20 per page</option>
                </select>
              )}
            </div>

            {searchTerm && (
              <button
                onClick={() => {
                  setSearchTerm('');
                  setCurrentPage(1);
                }}
                className="text-sm text-gray-500 hover:text-gray-700"
              >
                Clear search
              </button>
            )}
          </div>
        )}
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Stage
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Sales Range
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Default %
              </th>
              {paginatedSalesmen.map(salesman => (
                <th key={salesman.id} className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <div className="flex flex-col items-center">
                    <span className="truncate max-w-24">{salesman.name}</span>
                    <span className="text-xs text-gray-400 mt-1">
                      {salesman.user?.email?.split('@')[0] || 'No email'}
                    </span>
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {stages.map((stage) => (
              <tr key={stage.stage_id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-sm font-medium text-blue-600">
                        {stage.stage_number}
                      </span>
                    </div>
                    <div className="ml-3">
                      <div className="text-sm font-medium text-gray-900">
                        {stage.stage_name}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  ${parseFloat(stage.min_sales_amount).toLocaleString()} - {
                    stage.max_sales_amount 
                      ? `$${parseFloat(stage.max_sales_amount).toLocaleString()}`
                      : '∞'
                  }
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {(stage.commission_percentage * 100).toFixed(1)}%
                </td>
                {paginatedSalesmen.map(salesman => {
                  const cellKey = `${stage.stage_id}-${salesman.id}`;
                  const isEditing = editingCell === cellKey;
                  const isSaving = savingCell === cellKey;
                  const percentage = getCommissionPercentage(stage.stage_id, salesman.id);
                  const isCustom = isOverridden(stage.stage_id, salesman.id);
                  
                  return (
                    <td key={salesman.id} className="px-4 py-4 whitespace-nowrap text-center">
                      {isEditing ? (
                        <div className="flex items-center justify-center space-x-1">
                          <input
                            type="number"
                            min="0"
                            max="100"
                            step="0.1"
                            value={editValue}
                            onChange={(e) => setEditValue(e.target.value)}
                            onKeyDown={(e) => handleKeyPress(e, stage.stage_id, salesman.id)}
                            className="w-16 px-2 py-1 text-sm border border-blue-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                            autoFocus
                          />
                          <button
                            onClick={() => saveEdit(stage.stage_id, salesman.id)}
                            disabled={isSaving}
                            className="p-1 text-green-600 hover:text-green-800 disabled:opacity-50"
                          >
                            {isSaving ? (
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600"></div>
                            ) : (
                              <CheckIcon size={16} />
                            )}
                          </button>
                          <button
                            onClick={cancelEditing}
                            disabled={isSaving}
                            className="p-1 text-red-600 hover:text-red-800 disabled:opacity-50"
                          >
                            <XIcon size={16} />
                          </button>
                        </div>
                      ) : (
                        <div
                          className={`inline-flex items-center space-x-1 px-2 py-1 rounded ${
                            user?.role === 'admin' 
                              ? 'cursor-pointer hover:bg-blue-50' 
                              : ''
                          } ${
                            isCustom 
                              ? 'bg-yellow-50 border border-yellow-200' 
                              : 'bg-gray-50'
                          }`}
                          onClick={() => startEditing(stage.stage_id, salesman.id)}
                        >
                          <span className={`text-sm font-medium ${
                            isCustom ? 'text-yellow-800' : 'text-gray-700'
                          }`}>
                            {percentage.toFixed(1)}%
                          </span>
                          {isCustom && (
                            <span className="text-xs text-yellow-600" title="Custom override">
                              ●
                            </span>
                          )}
                          {user?.role === 'admin' && (
                            <EditIcon size={12} className="text-gray-400" />
                          )}
                        </div>
                      )}
                    </td>
                  );
                })}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination Controls */}
      {shouldShowPagination && (
        <div className="px-6 py-3 bg-gray-50 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Showing {((currentPage - 1) * salesmenPerPage) + 1} to{' '}
              {Math.min(currentPage * salesmenPerPage, filteredSalesmen.length)} of{' '}
              {filteredSalesmen.length} salesmen
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage <= 1}
                className="p-2 border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronLeftIcon size={16} />
              </button>

              <span className="px-3 py-1 text-sm text-gray-700">
                Page {currentPage} of {totalPages}
              </span>

              <button
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage >= totalPages}
                className="p-2 border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronRightIcon size={16} />
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Legend */}
      {user?.role === 'admin' && (
        <div className="px-6 py-3 bg-gray-50 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center text-xs text-gray-500">
              <div className="flex items-center mr-4">
                <div className="w-3 h-3 bg-gray-50 border border-gray-200 rounded mr-2"></div>
                Default percentage
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-yellow-50 border border-yellow-200 rounded mr-2"></div>
                Custom override
              </div>
            </div>

            {searchTerm && (
              <div className="text-xs text-gray-500">
                <FilterIcon size={12} className="inline mr-1" />
                Filtered by: "{searchTerm}"
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default CommissionMatrix;
