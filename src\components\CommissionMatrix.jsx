import React, { useState, useEffect } from 'react';
import { EditIcon, SaveIcon, XIcon, CheckIcon, AlertCircleIcon } from 'lucide-react';
import isEqual from 'lodash.isequal';

const CommissionMatrix = ({ 
  stages = [], 
  salesmen = [], 
  overrides = [], 
  onUpdateOverride, 
  loading = false,
  user 
}) => {
  const [editingCell, setEditingCell] = useState(null);
  const [editValue, setEditValue] = useState('');
  const [savingCell, setSavingCell] = useState(null);

  // DEBUG: Log received props
  console.log('CommissionMatrix props:', { stages, salesmen, overrides });

  // Defensive: always use the correct array for stages
  stages = (stages && Array.isArray(stages.stages)) ? stages.stages : (Array.isArray(stages) ? stages : []);

  // Create a map of overrides for quick lookup
  const overrideMap = (overrides || []).reduce((map, override) => {
    const key = `${override.stage_id}-${override.salesman_id}`;
    map[key] = override.commission_percentage;
    return map;
  }, {});

  // Get commission percentage for a stage-salesman combination
  const getCommissionPercentage = (stageId, salesmanId) => {
    const key = `${stageId}-${salesmanId}`;
    const override = overrideMap[key];
    
    if (override !== undefined) {
      return override * 100; // Convert to percentage
    }
    
    // Find the stage and return default percentage
    const stage = stages.find(s => s.stage_id === stageId);
    return stage ? stage.commission_percentage * 100 : 0;
  };

  // Check if a percentage is overridden (custom)
  const isOverridden = (stageId, salesmanId) => {
    const key = `${stageId}-${salesmanId}`;
    return overrideMap[key] !== undefined;
  };

  // Start editing a cell
  const startEditing = (stageId, salesmanId) => {
    if (user?.role !== 'admin') return;
    
    const currentPercentage = getCommissionPercentage(stageId, salesmanId);
    setEditingCell(`${stageId}-${salesmanId}`);
    setEditValue(currentPercentage.toString());
  };

  // Cancel editing
  const cancelEditing = () => {
    setEditingCell(null);
    setEditValue('');
  };

  // Save the edited value
  const saveEdit = async (stageId, salesmanId) => {
    const cellKey = `${stageId}-${salesmanId}`;
    
    try {
      setSavingCell(cellKey);
      
      const percentage = parseFloat(editValue);
      if (isNaN(percentage) || percentage < 0 || percentage > 100) {
        alert('Please enter a valid percentage between 0 and 100');
        return;
      }

      // Convert percentage to decimal for backend
      const decimalPercentage = percentage / 100;
      
      await onUpdateOverride({
        stage_id: stageId,
        salesman_id: salesmanId,
        commission_percentage: decimalPercentage
      });

      setEditingCell(null);
      setEditValue('');
    } catch (error) {
      console.error('Error saving commission override:', error);
      alert('Failed to save commission override. Please try again.');
    } finally {
      setSavingCell(null);
    }
  };

  // Handle key press in edit input
  const handleKeyPress = (e, stageId, salesmanId) => {
    if (e.key === 'Enter') {
      saveEdit(stageId, salesmanId);
    } else if (e.key === 'Escape') {
      cancelEditing();
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">Loading commission matrix...</span>
        </div>
      </div>
    );
  }

  if (stages.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="text-center py-8">
          <AlertCircleIcon size={48} className="mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Commission Stages</h3>
          <p className="text-gray-500">No commission stages have been created yet.</p>
        </div>
      </div>
    );
  }

  if (salesmen.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="text-center py-8">
          <AlertCircleIcon size={48} className="mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Salesmen</h3>
          <p className="text-gray-500">No salesmen found. Please add salesmen to view the commission matrix.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg overflow-hidden">
      <div className="px-6 py-4 border-b border-gray-200">
        <h2 className="text-xl font-semibold text-gray-800">Commission Matrix</h2>
        <p className="text-sm text-gray-600 mt-1">
          Global commission stages with per-salesman overrides. 
          {user?.role === 'admin' && ' Click any percentage to edit.'}
        </p>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Stage
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Sales Range
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Default %
              </th>
              {salesmen.map(salesman => (
                <th key={salesman.id} className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <div className="flex flex-col items-center">
                    <span className="truncate max-w-24">{salesman.name}</span>
                    <span className="text-xs text-gray-400 mt-1">
                      {salesman.user?.email?.split('@')[0] || 'No email'}
                    </span>
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {stages.map((stage) => (
              <tr key={stage.stage_id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-sm font-medium text-blue-600">
                        {stage.stage_number}
                      </span>
                    </div>
                    <div className="ml-3">
                      <div className="text-sm font-medium text-gray-900">
                        {stage.stage_name}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  ${parseFloat(stage.min_sales_amount).toLocaleString()} - {
                    stage.max_sales_amount 
                      ? `$${parseFloat(stage.max_sales_amount).toLocaleString()}`
                      : '∞'
                  }
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {(stage.commission_percentage * 100).toFixed(1)}%
                </td>
                {salesmen.map(salesman => {
                  const cellKey = `${stage.stage_id}-${salesman.id}`;
                  const isEditing = editingCell === cellKey;
                  const isSaving = savingCell === cellKey;
                  const percentage = getCommissionPercentage(stage.stage_id, salesman.id);
                  const isCustom = isOverridden(stage.stage_id, salesman.id);
                  
                  return (
                    <td key={salesman.id} className="px-4 py-4 whitespace-nowrap text-center">
                      {isEditing ? (
                        <div className="flex items-center justify-center space-x-1">
                          <input
                            type="number"
                            min="0"
                            max="100"
                            step="0.1"
                            value={editValue}
                            onChange={(e) => setEditValue(e.target.value)}
                            onKeyDown={(e) => handleKeyPress(e, stage.stage_id, salesman.id)}
                            className="w-16 px-2 py-1 text-sm border border-blue-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                            autoFocus
                          />
                          <button
                            onClick={() => saveEdit(stage.stage_id, salesman.id)}
                            disabled={isSaving}
                            className="p-1 text-green-600 hover:text-green-800 disabled:opacity-50"
                          >
                            {isSaving ? (
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600"></div>
                            ) : (
                              <CheckIcon size={16} />
                            )}
                          </button>
                          <button
                            onClick={cancelEditing}
                            disabled={isSaving}
                            className="p-1 text-red-600 hover:text-red-800 disabled:opacity-50"
                          >
                            <XIcon size={16} />
                          </button>
                        </div>
                      ) : (
                        <div
                          className={`inline-flex items-center space-x-1 px-2 py-1 rounded ${
                            user?.role === 'admin' 
                              ? 'cursor-pointer hover:bg-blue-50' 
                              : ''
                          } ${
                            isCustom 
                              ? 'bg-yellow-50 border border-yellow-200' 
                              : 'bg-gray-50'
                          }`}
                          onClick={() => startEditing(stage.stage_id, salesman.id)}
                        >
                          <span className={`text-sm font-medium ${
                            isCustom ? 'text-yellow-800' : 'text-gray-700'
                          }`}>
                            {percentage.toFixed(1)}%
                          </span>
                          {isCustom && (
                            <span className="text-xs text-yellow-600" title="Custom override">
                              ●
                            </span>
                          )}
                          {user?.role === 'admin' && (
                            <EditIcon size={12} className="text-gray-400" />
                          )}
                        </div>
                      )}
                    </td>
                  );
                })}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {user?.role === 'admin' && (
        <div className="px-6 py-3 bg-gray-50 border-t border-gray-200">
          <div className="flex items-center text-xs text-gray-500">
            <div className="flex items-center mr-4">
              <div className="w-3 h-3 bg-gray-50 border border-gray-200 rounded mr-2"></div>
              Default percentage
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-yellow-50 border border-yellow-200 rounded mr-2"></div>
              Custom override
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Memoize the component to prevent unnecessary rerenders
export default React.memo(CommissionMatrix, (prevProps, nextProps) => {
  return (
    isEqual(prevProps.stages, nextProps.stages) &&
    isEqual(prevProps.salesmen, nextProps.salesmen) &&
    isEqual(prevProps.overrides, nextProps.overrides) &&
    prevProps.loading === nextProps.loading &&
    isEqual(prevProps.user, nextProps.user)
  );
});
