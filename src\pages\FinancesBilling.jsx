import { useState, Fragment } from 'react';
import { FileTextIcon, PlusIcon, SearchIcon, FilterIcon, ChevronLeftIcon, ChevronRightIcon, DownloadIcon, SendIcon, EyeIcon, CheckIcon, AlertCircleIcon, CalendarIcon } from 'lucide-react';
import { useData } from '../contexts/DataContext';

const FinancesBilling = () => {
  const { invoicesData, customerOptions, statusOptions } = useData();
  const [filters, setFilters] = useState({
    dateFrom: '',
    dateTo: '',
    customer: '',
    status: ''
  });
  const [showCreateInvoice, setShowCreateInvoice] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);

  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleView = (id) => {
    setSelectedInvoice(id === selectedInvoice ? null : id);
  };

  // Use fallback data if context data is not available
  const localInvoicesData = [
    {
      id: 'INV-2023-001',
      customer: 'ABC Store',
      amount: 1250.0,
      date: '2023-06-15',
      dueDate: '2023-07-15',
      status: 'paid',
      items: [
        { description: 'Coca-Cola 500ml', quantity: 240, unitPrice: 0.7, total: 168.0 },
        { description: 'Sprite 1L', quantity: 120, unitPrice: 1.1, total: 132.0 },
        { description: 'Fanta 500ml', quantity: 180, unitPrice: 0.7, total: 126.0 },
        { description: 'Various Snacks', quantity: 824, unitPrice: 1.0, total: 824.0 }
      ]
    },
    {
      id: 'INV-2023-002',
      customer: 'XYZ Mart',
      amount: 850.0,
      date: '2023-06-12',
      dueDate: '2023-07-12',
      status: 'pending',
      items: [
        { description: 'Pepsi 330ml Can', quantity: 120, unitPrice: 0.7, total: 84.0 },
        { description: 'Various Snacks', quantity: 766, unitPrice: 1.0, total: 766.0 }
      ]
    },
    {
      id: 'INV-2023-003',
      customer: '123 Supermarket',
      amount: 1600.0,
      date: '2023-06-10',
      dueDate: '2023-07-10',
      status: 'overdue',
      items: [
        { description: 'Nestle Pure Life 1.5L', quantity: 100, unitPrice: 0.9, total: 90.0 },
        { description: 'Various Products', quantity: 1510, unitPrice: 1.0, total: 1510.0 }
      ]
    },
    {
      id: 'INV-2023-004',
      customer: 'Quick Shop',
      amount: 980.0,
      date: '2023-06-08',
      dueDate: '2023-07-08',
      status: 'pending',
      items: [
        { description: 'Sprite 1L', quantity: 150, unitPrice: 1.1, total: 165.0 },
        { description: 'Various Products', quantity: 815, unitPrice: 1.0, total: 815.0 }
      ]
    },
    {
      id: 'INV-2023-005',
      customer: 'Mega Mart',
      amount: 1200.0,
      date: '2023-06-05',
      dueDate: '2023-07-05',
      status: 'paid',
      items: [
        { description: 'Fanta 500ml', quantity: 180, unitPrice: 0.7, total: 126.0 },
        { description: 'Various Products', quantity: 1074, unitPrice: 1.0, total: 1074.0 }
      ]
    }
  ];

  const getStatusBadgeClass = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'overdue':
        return 'bg-red-100 text-red-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800'; // FIXED: Add cancelled status styling
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Calculate statistics
  const currentInvoicesData = invoicesData || localInvoicesData;
  const currentCustomerOptions = customerOptions || [
    { value: '', label: 'All Customers' },
    { value: 'abc', label: 'ABC Store' },
    { value: 'xyz', label: 'XYZ Mart' },
    { value: '123', label: '123 Supermarket' },
    { value: 'quick', label: 'Quick Shop' },
    { value: 'mega', label: 'Mega Mart' }
  ];
  const currentStatusOptions = statusOptions || [
    { value: '', label: 'All Statuses' },
    { value: 'pending', label: 'Pending' },
    { value: 'paid', label: 'Paid' },
    { value: 'overdue', label: 'Overdue' },
    { value: 'cancelled', label: 'Cancelled' }
  ];

  const totalInvoices = currentInvoicesData.length;
  const totalAmount = currentInvoicesData.reduce((sum, invoice) => sum + invoice.amount, 0);
  const pendingAmount = currentInvoicesData.filter(i => i.status === 'pending').reduce((sum, invoice) => sum + invoice.amount, 0);
  const overdueAmount = currentInvoicesData.filter(i => i.status === 'overdue').reduce((sum, invoice) => sum + invoice.amount, 0);

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Billing & Invoices</h1>
          <p className="text-gray-600">Manage customer invoices and payment tracking</p>
        </div>
        <div>
          <button className="flex items-center bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700" onClick={() => setShowCreateInvoice(!showCreateInvoice)}>
            <PlusIcon size={16} className="mr-2" />
            Create Invoice
          </button>
        </div>
      </div>
      {/* Billing Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-sm font-medium text-gray-500">TOTAL INVOICES</h3>
            <FileTextIcon size={20} className="text-blue-600" />
          </div>
          <p className="text-2xl font-bold text-gray-900">{totalInvoices}</p>
          <div className="mt-2 flex items-center text-sm">
            <span className="text-gray-600">This month</span>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-sm font-medium text-gray-500">TOTAL AMOUNT</h3>
            <FileTextIcon size={20} className="text-green-600" />
          </div>
          <p className="text-2xl font-bold text-gray-900">${totalAmount.toFixed(2)}</p>
          <div className="mt-2 flex items-center text-sm">
            <span className="text-gray-600">All invoices</span>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-sm font-medium text-gray-500">PENDING PAYMENT</h3>
            <FileTextIcon size={20} className="text-yellow-600" />
          </div>
          <p className="text-2xl font-bold text-gray-900">${pendingAmount.toFixed(2)}</p>
          <div className="mt-2 flex items-center text-sm">
            <span className="text-yellow-600">{currentInvoicesData.filter(i => i.status === 'pending').length} invoices</span>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-sm font-medium text-gray-500">OVERDUE</h3>
            <AlertCircleIcon size={20} className="text-red-600" />
          </div>
          <p className="text-2xl font-bold text-gray-900">${overdueAmount.toFixed(2)}</p>
          <div className="mt-2 flex items-center text-sm">
            <span className="text-red-600">{currentInvoicesData.filter(i => i.status === 'overdue').length} invoices</span>
          </div>
        </div>
      </div>
      {/* Create Invoice Form (conditionally rendered) */}
      {showCreateInvoice && (
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="flex items-center mb-4">
            <FileTextIcon size={20} className="text-blue-600 mr-2" />
            <h2 className="text-lg font-medium text-gray-800">Create New Invoice</h2>
          </div>
          <form>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
              <div>
                <label htmlFor="customer" className="block text-sm font-medium text-gray-700 mb-1">Customer</label>
                <select id="customer" name="customer" className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                  <option value="">Select customer</option>
                  {currentCustomerOptions.filter(option => option.value !== '').map(option => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  ))}
                </select>
              </div>
              <div>
                <label htmlFor="invoiceDate" className="block text-sm font-medium text-gray-700 mb-1">Invoice Date</label>
                <input type="date" id="invoiceDate" name="invoiceDate" className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" defaultValue={new Date().toISOString().split('T')[0]} required />
              </div>
              <div>
                <label htmlFor="dueDate" className="block text-sm font-medium text-gray-700 mb-1">Due Date</label>
                <input type="date" id="dueDate" name="dueDate" className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required />
              </div>
            </div>
            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-700 mb-2">Invoice Items</h3>
              <div className="border rounded-md overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Price</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    <tr>
                      <td className="px-4 py-2">
                        <input type="text" className="w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500" placeholder="Item description" />
                      </td>
                      <td className="px-4 py-2">
                        <input type="number" className="w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500" placeholder="Qty" min="1" />
                      </td>
                      <td className="px-4 py-2">
                        <input type="number" className="w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500" placeholder="0.00" step="0.01" min="0" />
                      </td>
                      <td className="px-4 py-2">
                        <input type="text" className="w-full px-2 py-1 border border-gray-300 rounded-md bg-gray-50 focus:outline-none" placeholder="0.00" readOnly />
                      </td>
                      <td className="px-4 py-2">
                        <button type="button" className="text-red-600 hover:text-red-900">Remove</button>
                      </td>
                    </tr>
                  </tbody>
                  <tfoot className="bg-gray-50">
                    <tr>
                      <td colSpan={5} className="px-4 py-2">
                        <button type="button" className="text-blue-600 hover:text-blue-900 text-sm font-medium">+ Add Item</button>
                      </td>
                    </tr>
                    <tr>
                      <td colSpan={3} className="px-4 py-2 text-right font-medium">Subtotal:</td>
                      <td className="px-4 py-2 font-medium">$0.00</td>
                      <td></td>
                    </tr>
                    <tr>
                      <td colSpan={3} className="px-4 py-2 text-right font-medium">Tax (5%):</td>
                      <td className="px-4 py-2 font-medium">$0.00</td>
                      <td></td>
                    </tr>
                    <tr>
                      <td colSpan={3} className="px-4 py-2 text-right font-medium">Total:</td>
                      <td className="px-4 py-2 font-bold text-blue-600">$0.00</td>
                      <td></td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </div>
            <div className="mb-6">
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">Notes</label>
              <textarea id="notes" name="notes" rows={3} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Additional notes for the invoice"></textarea>
            </div>
            <div className="flex justify-end">
              <button type="button" className="px-4 py-2 mr-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50" onClick={() => setShowCreateInvoice(false)}>
                Cancel
              </button>
              <button type="submit" className="flex items-center bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700">
                <PlusIcon size={16} className="mr-2" />
                Create Invoice
              </button>
            </div>
          </form>
        </div>
      )}
      {/* Filters */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="flex items-center mb-4">
          <FilterIcon size={20} className="text-blue-600 mr-2" />
          <h2 className="text-lg font-medium text-gray-800">Filters</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <label htmlFor="dateFrom" className="block text-sm font-medium text-gray-700 mb-1">Date From</label>
            <input type="date" id="dateFrom" name="dateFrom" value={filters.dateFrom} onChange={handleFilterChange} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" />
          </div>
          <div>
            <label htmlFor="dateTo" className="block text-sm font-medium text-gray-700 mb-1">Date To</label>
            <input type="date" id="dateTo" name="dateTo" value={filters.dateTo} onChange={handleFilterChange} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" />
          </div>
          <div>
            <label htmlFor="customer" className="block text-sm font-medium text-gray-700 mb-1">Customer</label>
            <select id="customer" name="customer" value={filters.customer} onChange={handleFilterChange} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              {currentCustomerOptions.map(option => (
                <option key={option.value} value={option.value}>{option.label}</option>
              ))}
            </select>
          </div>
          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <select id="status" name="status" value={filters.status} onChange={handleFilterChange} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              {currentStatusOptions.map(option => (
                <option key={option.value} value={option.value}>{option.label}</option>
              ))}
            </select>
          </div>
        </div>
        <div className="flex justify-end mt-4">
          <button className="px-4 py-2 mr-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50" onClick={() => setFilters({ dateFrom: '', dateTo: '', customer: '', status: '' })}>
            Clear Filters
          </button>
          <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Apply Filters</button>
        </div>
      </div>
      {/* Invoices Table */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <h2 className="text-lg font-medium text-gray-800">Invoices</h2>
          <button className="flex items-center text-blue-600 hover:text-blue-800">
            <DownloadIcon size={16} className="mr-1" />
            <span className="text-sm">Export</span>
          </button>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice #</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Issue Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {currentInvoicesData.map((invoice, index) => (
                <Fragment key={invoice.id || `invoice-${index}`}>
                  <tr>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">{invoice.id}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{invoice.customer}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{invoice.date}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{invoice.dueDate}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${invoice.amount.toFixed(2)}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(invoice.status)}`}>
                        {invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button className="text-blue-600 hover:text-blue-900" onClick={() => handleView(parseInt(invoice.id.split('-')[2]))}>
                          <EyeIcon size={18} />
                        </button>
                        <button className="text-green-600 hover:text-green-900">
                          <DownloadIcon size={18} />
                        </button>
                        {invoice.status === 'pending' && (
                          <button className="text-purple-600 hover:text-purple-900">
                            <SendIcon size={18} />
                          </button>
                        )}
                        {invoice.status === 'cancelled' && (
                          <span className="text-red-600 text-xs">Cancelled</span>
                        )}
                      </div>
                    </td>
                  </tr>
                  {selectedInvoice === parseInt(invoice.id.split('-')[2]) && (
                    <tr>
                      <td colSpan={7} className="px-6 py-4 bg-gray-50">
                        <div className="border rounded-md overflow-hidden">
                          <div className="bg-gray-100 px-4 py-2 font-medium flex justify-between items-center">
                            <span>Invoice Details</span>
                            <div className="flex space-x-2">
                              <button className="text-blue-600 hover:text-blue-900 text-sm">
                                <DownloadIcon size={14} className="inline mr-1" />PDF
                              </button>
                              <button className="text-green-600 hover:text-green-900 text-sm">
                                <SendIcon size={14} className="inline mr-1" />Email
                              </button>
                            </div>
                          </div>
                          <div className="p-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                              <div>
                                <p className="text-sm text-gray-500 mb-1">Bill To</p>
                                <p className="text-sm font-medium">{invoice.customer}</p>
                                <p className="text-sm text-gray-600">123 Customer Street</p>
                                <p className="text-sm text-gray-600">City, State, ZIP</p>
                              </div>
                              <div className="text-right">
                                <p className="text-sm text-gray-500 mb-1">Invoice Details</p>
                                <p className="text-sm"><span className="font-medium">Invoice Number:</span> {invoice.id}</p>
                                <p className="text-sm"><span className="font-medium">Issue Date:</span> {invoice.date}</p>
                                <p className="text-sm"><span className="font-medium">Due Date:</span> {invoice.dueDate}</p>
                              </div>
                            </div>
                            <div className="border rounded-md overflow-hidden mb-4">
                              <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                  <tr>
                                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Price</th>
                                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                                  </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                  {invoice.items.map((item, index) => (
                                    <tr key={index}>
                                      <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">{item.description}</td>
                                      <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.quantity}</td>
                                      <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">${item.unitPrice.toFixed(2)}</td>
                                      <td className="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">${item.total.toFixed(2)}</td>
                                    </tr>
                                  ))}
                                </tbody>
                                <tfoot className="bg-gray-50">
                                  <tr>
                                    <td colSpan={3} className="px-4 py-2 text-right font-medium">Total:</td>
                                    <td className="px-4 py-2 font-bold text-blue-600">${invoice.amount.toFixed(2)}</td>
                                  </tr>
                                </tfoot>
                              </table>
                            </div>
                            <div className="flex justify-between items-center">
                              <div>
                                <p className="text-sm text-gray-500">Payment Status</p>
                                <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(invoice.status)}`}>
                                  {invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
                                </span>
                              </div>
                              {invoice.status !== 'paid' && invoice.status !== 'cancelled' && (
                                <button className="px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center">
                                  <CheckIcon size={14} className="mr-1" />Mark as Paid
                                </button>
                              )}
                              {invoice.status === 'cancelled' && (
                                <span className="px-3 py-1 text-red-600 text-sm">
                                  Invoice cancelled
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                  )}
                </Fragment>
              ))}
            </tbody>
          </table>
        </div>
        {/* Pagination */}
        <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing <span className="font-medium">1</span> to <span className="font-medium">5</span> of <span className="font-medium">12</span> invoices
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50" disabled={currentPage === 1} onClick={() => setCurrentPage(currentPage - 1)}>
                  <span className="sr-only">Previous</span>
                  <ChevronLeftIcon size={16} />
                </button>
                <button className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-blue-600 hover:bg-gray-50">1</button>
                <button className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">2</button>
                <button className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50" onClick={() => setCurrentPage(currentPage + 1)}>
                  <span className="sr-only">Next</span>
                  <ChevronRightIcon size={16} />
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FinancesBilling;
