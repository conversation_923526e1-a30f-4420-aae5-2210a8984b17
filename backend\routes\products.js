const express = require('express');
const router = express.Router();
const {
  getProducts,
  getProduct,
  createProduct,
  updateProduct,
  deleteProduct,
  getCategories,
  getSuppliers,
  createSupplier,
  createCategory
} = require('../controllers/productController');
const { validateProduct, validateUUID } = require('../middleware/validation');
const { authenticateToken, adminOnly, adminOrSalesman } = require('../middleware/auth');

// All routes require authentication
router.use(authenticateToken);

// GET routes (accessible by admin and salesman)
router.get('/', adminOrSalesman, getProducts);
router.get('/categories', adminOrSalesman, getCategories);
router.get('/suppliers', adminOrSalesman, getSuppliers);
router.get('/:id', adminOrSalesman, validateUUID('id'), getProduct);

// POST/PUT/DELETE routes (admin only)
router.post('/', adminOnly, validateProduct, createProduct);
router.post('/suppliers', adminOnly, createSupplier);
router.post('/categories', adminOnly, createCategory);
router.put('/:id', adminOnly, validateUUID('id'), validateProduct, updateProduct);
router.delete('/:id', adminOnly, validateUUID('id'), deleteProduct);

module.exports = router;
