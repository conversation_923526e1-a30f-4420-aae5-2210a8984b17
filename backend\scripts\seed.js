const { User, Salesman, Supplier } = require('../models');

const seedDatabase = async () => {
  try {
    console.log('🌱 Starting database seeding...');
    console.log('📝 Note: No demo accounts will be created. Users must register through the application.');

    // Create default suppliers
    const suppliers = [
      {
        name: 'Coca Cola Company',
        contact_person: 'Sales Manager',
        phone: '+****************',
        email: '<EMAIL>',
        address: '1 Coca Cola Plaza, Atlanta, GA 30313'
      },
      {
        name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        contact_person: 'Account Manager',
        phone: '+****************',
        email: '<EMAIL>',
        address: '700 Anderson Hill Rd, Purchase, NY 10577'
      },
      {
        name: '<PERSON><PERSON><PERSON>',
        contact_person: 'Distribution Manager',
        phone: '+****************',
        email: '<EMAIL>',
        address: '1812 N Moore St, Rosslyn, VA 22209'
      }
    ];

    for (const supplierData of suppliers) {
      const [supplier, created] = await Supplier.findOrCreate({
        where: { name: supplierData.name },
        defaults: supplierData
      });

      if (created) {
        console.log(`✅ Supplier "${supplierData.name}" created`);
      } else {
        console.log(`ℹ️ Supplier "${supplierData.name}" already exists`);
      }
    }

    console.log('🎉 Database seeding completed successfully!');
    console.log('\n📋 Next Steps:');
    console.log('1. Start the backend server: npm start');
    console.log('2. Start the frontend: npm run dev');
    console.log('3. Register a new account at: http://localhost:5176/signup');
    console.log('4. Verify your email to activate your account');

    process.exit(0);
  } catch (error) {
    console.error('❌ Seeding failed:', error);
    process.exit(1);
  }
};

seedDatabase();
