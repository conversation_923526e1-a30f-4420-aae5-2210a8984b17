# Inventory Management System - Backend API

A robust Node.js/Express backend API for the Inventory Management System with PostgreSQL database.

## 🚀 Quick Start

### Prerequisites
- Node.js (v16 or higher)
- PostgreSQL (v12 or higher)
- npm or yarn

### Installation

1. **Clone and navigate to backend directory**
```bash
cd backend
```

2. **Install dependencies**
```bash
npm install
```

3. **Set up environment variables**
```bash
cp .env.example .env
```
Edit `.env` file with your database credentials and configuration.

4. **Set up PostgreSQL database**
```sql
CREATE DATABASE inventory_management;
CREATE USER your_username WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE inventory_management TO your_username;
```

5. **Run database migrations**
```bash
npm run migrate
```

6. **Seed database with default data**
```bash
npm run seed
```

7. **Start the server**
```bash
# Development mode
npm run dev

# Production mode
npm start
```

## 🔧 Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DB_HOST` | Database host | localhost |
| `DB_PORT` | Database port | 5432 |
| `DB_NAME` | Database name | inventory_management |
| `DB_USER` | Database username | - |
| `DB_PASSWORD` | Database password | - |
| `PORT` | Server port | 3001 |
| `JWT_SECRET` | JWT secret key | - |
| `JWT_EXPIRES_IN` | JWT expiration time | 7d |
| `FRONTEND_URL` | Frontend URL for CORS | http://localhost:5177 |

## 📊 Database Schema

### Core Entities
- **Users** - Authentication and user management
- **Salesmen** - Salesman profiles and performance tracking
- **Customers** - Customer information and relationships
- **Products** - Product catalog and inventory
- **Suppliers** - Supplier information
- **Orders** - Sales orders and order items
- **Inventory Transactions** - All inventory movements
- **Payments** - Payment records
- **Expenses** - Business expense tracking

### Key Relationships
- Users → Salesmen (1:1)
- Salesmen → Customers (1:Many)
- Customers → Orders (1:Many)
- Orders → Order Items (1:Many)
- Products → Inventory Transactions (1:Many)
- Suppliers → Products (1:Many)

## 🛣️ API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user (Admin only)
- `POST /api/auth/login` - User login
- `GET /api/auth/profile` - Get user profile
- `PUT /api/auth/profile` - Update user profile

### Products
- `GET /api/products` - Get all products
- `GET /api/products/:id` - Get single product
- `POST /api/products` - Create product (Admin only)
- `PUT /api/products/:id` - Update product (Admin only)
- `DELETE /api/products/:id` - Delete product (Admin only)
- `GET /api/products/categories` - Get product categories

### Customers
- `GET /api/customers` - Get all customers
- `GET /api/customers/:id` - Get single customer
- `POST /api/customers` - Create customer
- `PUT /api/customers/:id` - Update customer
- `DELETE /api/customers/:id` - Delete customer
- `GET /api/customers/types` - Get customer types

## 🔐 Authentication & Authorization

### JWT Authentication
All protected routes require a valid JWT token in the Authorization header:
```
Authorization: Bearer <token>
```

### Role-Based Access Control
- **Admin**: Full access to all resources
- **Salesman**: Limited access to assigned customers and related data

### Default Credentials
After seeding the database:
- **Admin**: <EMAIL> / admin123
- **Salesman**: <EMAIL> / salesman123

## 📝 API Response Format

### Success Response
```json
{
  "success": true,
  "message": "Operation successful",
  "data": {
    // Response data
  }
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error description",
  "errors": [
    // Validation errors (if any)
  ]
}
```

### Pagination Response
```json
{
  "success": true,
  "data": {
    "items": [...],
    "pagination": {
      "current_page": 1,
      "total_pages": 10,
      "total_items": 100,
      "items_per_page": 10
    }
  }
}
```

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests with coverage
npm run test:coverage
```

## 📦 Production Deployment

1. **Set environment to production**
```bash
export NODE_ENV=production
```

2. **Use production database**
Update `.env` with production database credentials.

3. **Run migrations**
```bash
npm run migrate
```

4. **Start with PM2 (recommended)**
```bash
npm install -g pm2
pm2 start server.js --name "inventory-api"
```

## 🔍 Monitoring & Logging

- **Health Check**: `GET /health`
- **Logs**: Morgan logging middleware
- **Error Handling**: Centralized error handling
- **Rate Limiting**: 100 requests per 15 minutes per IP

## 🛡️ Security Features

- **Helmet**: Security headers
- **CORS**: Cross-origin resource sharing
- **Rate Limiting**: Request throttling
- **JWT**: Secure authentication
- **Password Hashing**: bcryptjs
- **Input Validation**: express-validator
- **SQL Injection Protection**: Sequelize ORM

## 📚 Additional Resources

- [Sequelize Documentation](https://sequelize.org/)
- [Express.js Documentation](https://expressjs.com/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
