import { useState, useEffect } from 'react';
import { XIcon, DollarSignIcon, CheckCircleIcon, AlertCircleIcon, ClockIcon, CreditCardIcon } from 'lucide-react';

const PaymentProcessingModal = ({ isOpen, onClose, employees, onProcessPayments, loading = false }) => {
  const [selectedEmployees, setSelectedEmployees] = useState([]);
  const [paymentData, setPaymentData] = useState({
    pay_period_start: '',
    pay_period_end: '',
    payment_method: 'bank_transfer',
    notes: ''
  });

  useEffect(() => {
    if (isOpen && employees.length > 0) {
      // Auto-select all active employees
      setSelectedEmployees(employees.filter(emp => emp.status === 'active').map(emp => emp.id));
      
      // Set default pay period (current month)
      const now = new Date();
      const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
      const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);
      
      setPaymentData(prev => ({
        ...prev,
        pay_period_start: firstDay.toISOString().split('T')[0],
        pay_period_end: lastDay.toISOString().split('T')[0]
      }));
    }
  }, [isOpen, employees]);

  const handleEmployeeToggle = (employeeId) => {
    setSelectedEmployees(prev => 
      prev.includes(employeeId)
        ? prev.filter(id => id !== employeeId)
        : [...prev, employeeId]
    );
  };

  const handleSelectAll = () => {
    const activeEmployees = employees.filter(emp => emp.status === 'active');
    setSelectedEmployees(activeEmployees.map(emp => emp.id));
  };

  const handleDeselectAll = () => {
    setSelectedEmployees([]);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setPaymentData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const calculateTotalPayment = () => {
    return selectedEmployees.reduce((total, empId) => {
      const employee = employees.find(emp => emp.id === empId);
      return total + (employee?.totalSalary || 0);
    }, 0);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (selectedEmployees.length === 0) {
      alert('Please select at least one employee for payment processing.');
      return;
    }
    
    const paymentRequests = selectedEmployees.map(employeeId => {
      const employee = employees.find(emp => emp.id === employeeId);
      return {
        employee_id: employeeId,
        assignment_id: employee?.currentAssignmentId, // This would come from the employee data
        ...paymentData,
        working_days: 30, // Default, could be calculated
        present_days: 30, // Default, could be from attendance data
        absent_days: 0,
        leave_days: 0,
        overtime_hours: 0
      };
    });
    
    onProcessPayments(paymentRequests);
  };

  const handleClose = () => {
    setSelectedEmployees([]);
    setPaymentData({
      pay_period_start: '',
      pay_period_end: '',
      payment_method: 'bank_transfer',
      notes: ''
    });
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center">
            <CreditCardIcon size={20} className="mr-2 text-green-600" />
            Process Monthly Salary Payments
          </h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-500 transition-colors"
          >
            <XIcon size={24} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6">
          {/* Payment Period */}
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <ClockIcon size={18} className="mr-2 text-blue-600" />
              Payment Period
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Pay Period Start
                </label>
                <input
                  type="date"
                  name="pay_period_start"
                  value={paymentData.pay_period_start}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Pay Period End
                </label>
                <input
                  type="date"
                  name="pay_period_end"
                  value={paymentData.pay_period_end}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Payment Method
                </label>
                <select
                  name="payment_method"
                  value={paymentData.payment_method}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                >
                  <option value="bank_transfer">Bank Transfer</option>
                  <option value="cash">Cash</option>
                  <option value="check">Check</option>
                  <option value="digital_wallet">Digital Wallet</option>
                </select>
              </div>
            </div>
          </div>

          {/* Employee Selection */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900 flex items-center">
                <DollarSignIcon size={18} className="mr-2 text-green-600" />
                Select Employees ({selectedEmployees.length} selected)
              </h3>
              <div className="space-x-2">
                <button
                  type="button"
                  onClick={handleSelectAll}
                  className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                >
                  Select All
                </button>
                <button
                  type="button"
                  onClick={handleDeselectAll}
                  className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
                >
                  Deselect All
                </button>
              </div>
            </div>

            <div className="max-h-64 overflow-y-auto border border-gray-200 rounded-lg">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50 sticky top-0">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Select
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Employee
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Position
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Salary
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {employees.map((employee) => (
                    <tr key={employee.id} className="hover:bg-gray-50">
                      <td className="px-4 py-3 whitespace-nowrap">
                        <input
                          type="checkbox"
                          checked={selectedEmployees.includes(employee.id)}
                          onChange={() => handleEmployeeToggle(employee.id)}
                          className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                          disabled={employee.status !== 'active'}
                        />
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{employee.name}</div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                        {employee.position}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          employee.status === 'active' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {employee.status}
                        </span>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                        ${(employee.totalSalary || 0).toFixed(2)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Payment Summary */}
          <div className="mb-6 p-4 bg-green-50 rounded-lg">
            <h3 className="text-lg font-medium text-gray-900 mb-2">Payment Summary</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Selected Employees:</span>
                <p className="font-medium">{selectedEmployees.length}</p>
              </div>
              <div>
                <span className="text-gray-600">Total Amount:</span>
                <p className="font-bold text-green-600 text-lg">${calculateTotalPayment().toFixed(2)}</p>
              </div>
              <div>
                <span className="text-gray-600">Payment Method:</span>
                <p className="font-medium capitalize">{paymentData.payment_method.replace('_', ' ')}</p>
              </div>
            </div>
          </div>

          {/* Notes */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Payment Notes
            </label>
            <textarea
              name="notes"
              value={paymentData.notes}
              onChange={handleInputChange}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
              placeholder="Additional notes for this payment batch..."
            />
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading || selectedEmployees.length === 0}
              className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:bg-green-400 flex items-center"
            >
              {loading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Processing...
                </>
              ) : (
                `Process ${selectedEmployees.length} Payments ($${calculateTotalPayment().toFixed(2)})`
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default PaymentProcessingModal;
