const { Customer, Salesman, Order, Payment } = require('../models');
const { Op } = require('sequelize');

// Get all customers
const getCustomers = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 50,
      search,
      type,
      salesman_id,
      status = 'active'
    } = req.query;

    const offset = (page - 1) * limit;
    const where = {};

    // Filter by status
    if (status !== 'all') {
      where.status = status;
    }

    // Filter by type
    if (type) {
      where.type = type;
    }

    // Filter by salesman (for salesman role)
    if (req.user.role === 'salesman') {
      const salesman = await Salesman.findOne({ where: { user_id: req.user.user_id } });
      if (salesman) {
        where.salesman_id = salesman.salesman_id;
      }
    } else if (salesman_id) {
      where.salesman_id = salesman_id;
    }

    // Search by name
    if (search) {
      where.name = {
        [Op.iLike]: `%${search}%`
      };
    }

    const { count, rows: customers } = await Customer.findAndCountAll({
      where,
      include: [
        {
          model: Salesman,
          as: 'salesman',
          attributes: ['salesman_id', 'full_name']
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['name', 'ASC']]
    });

    res.json({
      success: true,
      data: {
        customers,
        pagination: {
          current_page: parseInt(page),
          total_pages: Math.ceil(count / limit),
          total_items: count,
          items_per_page: parseInt(limit)
        }
      }
    });
  } catch (error) {
    next(error);
  }
};

// Get single customer
const getCustomer = async (req, res, next) => {
  try {
    const { id } = req.params;

    const customer = await Customer.findByPk(id, {
      include: [
        {
          model: Salesman,
          as: 'salesman',
          attributes: ['salesman_id', 'full_name', 'contact_info']
        },
        {
          model: Order,
          as: 'orders',
          limit: 10,
          order: [['order_date', 'DESC']],
          attributes: ['order_id', 'order_number', 'order_date', 'status', 'total_amount']
        },
        {
          model: Payment,
          as: 'payments',
          limit: 5,
          order: [['payment_date', 'DESC']],
          attributes: ['payment_id', 'payment_number', 'amount', 'payment_date', 'payment_method']
        }
      ]
    });

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found'
      });
    }

    // Check if user has permission to view this customer
    if (req.user.role === 'salesman') {
      const salesman = await Salesman.findOne({ where: { user_id: req.user.user_id } });
      if (salesman && customer.salesman_id !== salesman.salesman_id) {
        return res.status(403).json({
          success: false,
          message: 'Access denied'
        });
      }
    }

    res.json({
      success: true,
      data: { customer }
    });
  } catch (error) {
    next(error);
  }
};

// Create new customer
const createCustomer = async (req, res, next) => {
  try {
    const {
      name,
      type,
      contact,
      phone,
      email,
      address,
      credit_limit = 0,
      payment_terms = 'Net 30',
      payment_method = 'Cash',
      salesman_id
    } = req.body;

    // If user is salesman, assign to their profile
    let assignedSalesmanId = salesman_id;
    if (req.user.role === 'salesman') {
      const salesman = await Salesman.findOne({ where: { user_id: req.user.user_id } });
      if (salesman) {
        assignedSalesmanId = salesman.salesman_id;
      }
    }

    const customer = await Customer.create({
      name,
      type,
      contact,
      phone,
      email,
      address,
      credit_limit,
      payment_terms,
      payment_method,
      salesman_id: assignedSalesmanId
    });

    // Get customer with salesman info
    const createdCustomer = await Customer.findByPk(customer.customer_id, {
      include: [
        {
          model: Salesman,
          as: 'salesman',
          attributes: ['salesman_id', 'full_name']
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: 'Customer created successfully',
      data: { customer: createdCustomer }
    });
  } catch (error) {
    next(error);
  }
};

// Update customer
const updateCustomer = async (req, res, next) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const customer = await Customer.findByPk(id);
    if (!customer) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found'
      });
    }

    // Check if user has permission to update this customer
    if (req.user.role === 'salesman') {
      const salesman = await Salesman.findOne({ where: { user_id: req.user.user_id } });
      if (salesman && customer.salesman_id !== salesman.salesman_id) {
        return res.status(403).json({
          success: false,
          message: 'Access denied'
        });
      }
      // Prevent salesman from changing salesman assignment
      delete updateData.salesman_id;
    }

    await customer.update(updateData);

    // Get updated customer with salesman info
    const updatedCustomer = await Customer.findByPk(id, {
      include: [
        {
          model: Salesman,
          as: 'salesman',
          attributes: ['salesman_id', 'full_name']
        }
      ]
    });

    res.json({
      success: true,
      message: 'Customer updated successfully',
      data: { customer: updatedCustomer }
    });
  } catch (error) {
    next(error);
  }
};

// Delete customer (soft delete)
const deleteCustomer = async (req, res, next) => {
  try {
    const { id } = req.params;

    const customer = await Customer.findByPk(id);
    if (!customer) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found'
      });
    }

    // Check if user has permission to delete this customer
    if (req.user.role === 'salesman') {
      const salesman = await Salesman.findOne({ where: { user_id: req.user.user_id } });
      if (salesman && customer.salesman_id !== salesman.salesman_id) {
        return res.status(403).json({
          success: false,
          message: 'Access denied'
        });
      }
    }

    await customer.update({ status: 'inactive' });

    res.json({
      success: true,
      message: 'Customer deleted successfully'
    });
  } catch (error) {
    next(error);
  }
};

// Get customer types
const getCustomerTypes = async (req, res, next) => {
  try {
    const types = ['Convenience Store', 'Supermarket', 'Wholesaler', 'Restaurant', 'Other'];

    res.json({
      success: true,
      data: { types }
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getCustomers,
  getCustomer,
  createCustomer,
  updateCustomer,
  deleteCustomer,
  getCustomerTypes
};
