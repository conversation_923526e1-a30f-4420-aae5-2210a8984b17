import React, { useState, useEffect } from 'react';
import {
  XIcon,
  PlusIcon,
  EditIcon,
  TrashIcon,
  SaveIcon,
  AlertTriangleIcon,
  CheckCircleIcon
} from 'lucide-react';

const ManageStagesModal = ({ isOpen, onClose, onStagesUpdated, user }) => {
  const [stages, setStages] = useState([]);
  const [loading, setLoading] = useState(false);
  const [editingStage, setEditingStage] = useState(null);
  const [newStage, setNewStage] = useState({
    stage_name: '',
    min_sales_amount: '',
    max_sales_amount: '',
    commission_percentage: ''
  });
  const [showAddForm, setShowAddForm] = useState(false);
  const [draggedStage, setDraggedStage] = useState(null);

  // Fetch stages with automatic ordering
  const fetchStages = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('auth_token');
      const response = await fetch('http://localhost:3001/api/commissions/stages', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          // Auto-order stages by sales range
          const orderedStages = autoOrderStages(result.data.stages || []);
          setStages(orderedStages);
        }
      }
    } catch (error) {
      console.error('Error fetching stages:', error);
    } finally {
      setLoading(false);
    }
  };

  // Create new stage with automatic ordering
  const createStage = async () => {
    try {
      // Validation
      if (!newStage.stage_name || !newStage.commission_percentage) {
        alert('Stage name and commission percentage are required');
        return;
      }

      const commission = parseFloat(newStage.commission_percentage);
      if (isNaN(commission) || commission < 0 || commission > 100) {
        alert('Commission percentage must be between 0 and 100');
        return;
      }

      // First stage validation
      const minSales = newStage.min_sales_amount ? parseFloat(newStage.min_sales_amount) : undefined;
      const isFirstStage = stages.length === 0;
      const wouldBeFirstStage = stages.length > 0 && minSales !== undefined && minSales < Math.min(...stages.map(s => parseFloat(s.min_sales_amount)));

      if ((isFirstStage || wouldBeFirstStage) && minSales !== 0) {
        alert('First commission stage must start from $0');
        return;
      }

      const token = localStorage.getItem('auth_token');
      const response = await fetch('http://localhost:3001/api/commissions/stages', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...newStage,
          commission_percentage: commission
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          // Refresh stages from server to get the auto-ordered and validated data
          await fetchStages();
          setNewStage({
            stage_name: '',
            min_sales_amount: '',
            max_sales_amount: '',
            commission_percentage: ''
          });
          setShowAddForm(false);
          onStagesUpdated?.();
        }
      } else {
        const error = await response.json();
        alert(error.message || 'Failed to create stage');
      }
    } catch (error) {
      console.error('Error creating stage:', error);
      alert('Error creating stage');
    }
  };

  // Update stage with automatic ordering and infinity validation
  const updateStage = async (stageId, updates) => {
    try {
      // Validation
      const commission = parseFloat(updates.commission_percentage);
      if (isNaN(commission) || commission < 0 || commission > 100) {
        alert('Commission percentage must be between 0 and 100');
        return;
      }

      // First stage validation - cannot change min_sales_amount from 0
      const orderedStages = [...stages].sort((a, b) => parseFloat(a.min_sales_amount) - parseFloat(b.min_sales_amount));
      const isFirstStage = orderedStages.length > 0 && orderedStages[0].stage_id === stageId;
      const newMinSales = parseFloat(updates.min_sales_amount);

      if (isFirstStage && newMinSales !== 0) {
        alert('First commission stage must start from $0');
        return;
      }

      // Validate infinity setting before sending to backend
      const updatedStages = stages.map(stage =>
        stage.stage_id === stageId
          ? { ...stage, ...updates, commission_percentage: commission / 100 }
          : stage
      );

      const validatedStages = validateInfinitySettings(updatedStages);
      const targetStage = validatedStages.find(s => s.stage_id === stageId);

      if (!targetStage) {
        alert('Error: Stage not found');
        return;
      }

      const token = localStorage.getItem('auth_token');
      const response = await fetch(`http://localhost:3001/api/commissions/stages/${stageId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          stage_name: targetStage.stage_name,
          min_sales_amount: targetStage.min_sales_amount,
          max_sales_amount: targetStage.max_sales_amount,
          commission_percentage: targetStage.commission_percentage
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          // Refresh stages from server to get the auto-ordered and validated data
          await fetchStages();
          setEditingStage(null);
          onStagesUpdated?.();
        }
      } else {
        const error = await response.json();
        alert(error.message || 'Failed to update stage');
      }
    } catch (error) {
      console.error('Error updating stage:', error);
      alert('Error updating stage');
    }
  };

  // Delete stage
  const deleteStage = async (stageId) => {
    if (!window.confirm('Are you sure you want to delete this stage? This action cannot be undone.')) {
      return;
    }

    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`http://localhost:3001/api/commissions/stages/${stageId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        await fetchStages();
        onStagesUpdated?.();
      } else {
        const error = await response.json();
        alert(error.message || 'Failed to delete stage');
      }
    } catch (error) {
      console.error('Error deleting stage:', error);
      alert('Error deleting stage');
    }
  };

  // Auto-order stages based on sales ranges
  const autoOrderStages = (stagesList) => {
    return [...stagesList].sort((a, b) => {
      const aMin = parseFloat(a.min_sales_amount) || 0;
      const bMin = parseFloat(b.min_sales_amount) || 0;
      return aMin - bMin;
    });
  };

  // Validate and fix infinity settings
  const validateInfinitySettings = (stagesList) => {
    const orderedStages = autoOrderStages(stagesList);

    return orderedStages.map((stage, index) => {
      const isLastStage = index === orderedStages.length - 1;

      // If this is not the last stage and has infinity max, set it to next stage's min - 1
      if (!isLastStage && (!stage.max_sales_amount || stage.max_sales_amount === null)) {
        const nextStage = orderedStages[index + 1];
        if (nextStage) {
          const nextMin = parseFloat(nextStage.min_sales_amount) || 0;
          return {
            ...stage,
            max_sales_amount: Math.max(nextMin - 1, parseFloat(stage.min_sales_amount) || 0)
          };
        }
      }

      // If this is the last stage, it can have infinity
      if (isLastStage) {
        return stage;
      }

      return stage;
    });
  };

  // Smart range calculation for new stage
  const calculateSmartRange = () => {
    if (stages.length === 0) {
      return { min: 1, max: 10000 };
    }
    
    const lastStage = stages[stages.length - 1];
    const newMin = lastStage.max_sales_amount ? parseFloat(lastStage.max_sales_amount) + 1 : parseFloat(lastStage.min_sales_amount) + 10000;
    
    return { min: newMin, max: null };
  };

  useEffect(() => {
    if (isOpen) {
      fetchStages();
    }
  }, [isOpen]);

  useEffect(() => {
    if (showAddForm && stages.length > 0) {
      const smartRange = calculateSmartRange();
      setNewStage(prev => ({
        ...prev,
        min_sales_amount: smartRange.min.toString(),
        max_sales_amount: smartRange.max ? smartRange.max.toString() : ''
      }));
    }
  }, [showAddForm, stages]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900">Manage Commission Stages</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <XIcon size={20} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-3 text-gray-600">Loading stages...</span>
            </div>
          ) : (
            <div className="space-y-6">
              {/* Add New Stage Button */}
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-900">Commission Stages ({stages.length})</h3>
                <button
                  onClick={() => setShowAddForm(true)}
                  className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <PlusIcon size={16} className="mr-2" />
                  Add New Stage
                </button>
              </div>

              {/* Add New Stage Form */}
              {showAddForm && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="text-md font-medium text-blue-900 mb-4">Add New Commission Stage</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Stage Name</label>
                      <input
                        type="text"
                        value={newStage.stage_name}
                        onChange={(e) => setNewStage(prev => ({ ...prev, stage_name: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="e.g., Bronze, Silver, Gold"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Commission %</label>
                      <input
                        type="number"
                        min="0"
                        max="100"
                        step="0.1"
                        value={newStage.commission_percentage}
                        onChange={(e) => setNewStage(prev => ({ ...prev, commission_percentage: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="5.0"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Min Sales Amount ($)
                        {stages.length === 0 && (
                          <span className="text-red-600 text-xs ml-1">(Must be $0 for first stage)</span>
                        )}
                      </label>
                      <input
                        type="number"
                        min="0"
                        value={newStage.min_sales_amount}
                        onChange={(e) => setNewStage(prev => ({ ...prev, min_sales_amount: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder={stages.length === 0 ? "0" : "Auto-calculated"}
                      />
                      {stages.length === 0 && (
                        <p className="text-xs text-red-600 mt-1">First stage must start from $0</p>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Max Sales Amount ($)</label>
                      <input
                        type="number"
                        min="0"
                        value={newStage.max_sales_amount}
                        onChange={(e) => setNewStage(prev => ({ ...prev, max_sales_amount: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Leave empty for unlimited"
                      />
                    </div>
                  </div>
                  <div className="flex justify-end space-x-3 mt-4">
                    <button
                      onClick={() => setShowAddForm(false)}
                      className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={createStage}
                      disabled={!newStage.stage_name || !newStage.commission_percentage}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
                    >
                      <SaveIcon size={16} className="mr-2 inline" />
                      Create Stage
                    </button>
                  </div>
                </div>
              )}

              {/* Stages List */}
              <div className="space-y-3">
                <div className="text-sm text-gray-600 mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center">
                    <CheckCircleIcon size={16} className="text-blue-600 mr-2" />
                    <span className="font-medium">Auto-Ordering Active:</span>
                  </div>
                  <div className="mt-1 text-xs">
                    • Stages are automatically ordered by sales range (lowest to highest)
                    • Only the last stage can have unlimited (∞) maximum sales
                    • Other stages with ∞ will auto-adjust to next stage's minimum - 1
                  </div>
                </div>

                {stages.map((stage, index) => (
                  <StageRow
                    key={stage.stage_id}
                    stage={stage}
                    index={index}
                    totalStages={stages.length}
                    editingStage={editingStage}
                    onEdit={setEditingStage}
                    onUpdate={updateStage}
                    onDelete={deleteStage}
                  />
                ))}
              </div>

              {stages.length === 0 && !showAddForm && (
                <div className="text-center py-8">
                  <AlertTriangleIcon size={48} className="mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Commission Stages</h3>
                  <p className="text-gray-500 mb-4">Create your first commission stage to get started.</p>
                  <button
                    onClick={() => setShowAddForm(true)}
                    className="flex items-center mx-auto px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                  >
                    <PlusIcon size={16} className="mr-2" />
                    Add First Stage
                  </button>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              <CheckCircleIcon size={16} className="inline mr-2 text-green-600" />
              Changes are automatically saved and synced with the database
            </div>
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Individual Stage Row Component
const StageRow = ({ stage, index, totalStages, editingStage, onEdit, onUpdate, onDelete }) => {
  const [editData, setEditData] = useState({
    stage_name: stage.stage_name,
    min_sales_amount: stage.min_sales_amount?.toString() || '',
    max_sales_amount: stage.max_sales_amount?.toString() || '',
    // Convert from decimal (0.02) to percentage (2) for display
    commission_percentage: (parseFloat(stage.commission_percentage) * 100).toString()
  });

  const isEditing = editingStage === stage.stage_id;
  const isLastStage = index === totalStages - 1;
  const hasInfinityMax = !stage.max_sales_amount || stage.max_sales_amount === null;
  const isFirstStage = index === 0;

  const handleSave = () => {
    onUpdate(stage.stage_id, editData);
  };

  const handleCancel = () => {
    setEditData({
      stage_name: stage.stage_name,
      min_sales_amount: stage.min_sales_amount?.toString() || '',
      max_sales_amount: stage.max_sales_amount?.toString() || '',
      commission_percentage: (parseFloat(stage.commission_percentage) * 100).toString()
    });
    onEdit(null);
  };

  return (
    <div className={`bg-white border rounded-lg p-4 hover:shadow-md transition-shadow ${
      isLastStage && hasInfinityMax ? 'border-green-200 bg-green-50' : 'border-gray-200'
    }`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
            isLastStage && hasInfinityMax ? 'bg-green-100' : 'bg-blue-100'
          }`}>
            <span className={`text-sm font-medium ${
              isLastStage && hasInfinityMax ? 'text-green-600' : 'text-blue-600'
            }`}>
              {index + 1}
            </span>
          </div>
          
          {isEditing ? (
            <div className="grid grid-cols-4 gap-3 flex-1">
              <input
                type="text"
                value={editData.stage_name}
                onChange={(e) => setEditData(prev => ({ ...prev, stage_name: e.target.value }))}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Stage Name"
              />
              <input
                type="number"
                value={editData.min_sales_amount}
                onChange={(e) => setEditData(prev => ({ ...prev, min_sales_amount: e.target.value }))}
                disabled={isFirstStage}
                className={`px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  isFirstStage ? 'bg-gray-100 cursor-not-allowed' : ''
                }`}
                placeholder={isFirstStage ? "Must be $0" : "Min Amount"}
                title={isFirstStage ? "First stage must start from $0" : ""}
              />
              <input
                type="number"
                value={editData.max_sales_amount}
                onChange={(e) => setEditData(prev => ({ ...prev, max_sales_amount: e.target.value }))}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Max Amount"
              />
              <input
                type="number"
                step="0.1"
                value={editData.commission_percentage}
                onChange={(e) => setEditData(prev => ({ ...prev, commission_percentage: e.target.value }))}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Commission %"
              />
            </div>
          ) : (
            <div className="flex-1">
              <div className="flex items-center space-x-6">
                <div>
                  <div className="font-medium text-gray-900 flex items-center">
                    {stage.stage_name}
                    {isLastStage && hasInfinityMax && (
                      <span className="ml-2 px-2 py-1 text-xs bg-green-100 text-green-700 rounded-full">
                        Unlimited
                      </span>
                    )}
                  </div>
                  <div className="text-sm text-gray-500">
                    Position {index + 1} of {totalStages}
                    {isLastStage && hasInfinityMax && ' • Final Stage'}
                  </div>
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-900">
                    ${parseFloat(stage.min_sales_amount).toLocaleString()} - {
                      stage.max_sales_amount
                        ? `$${parseFloat(stage.max_sales_amount).toLocaleString()}`
                        : '∞'
                    }
                  </div>
                  <div className="text-sm text-gray-500">
                    Sales Range
                    {!isLastStage && hasInfinityMax && (
                      <span className="text-orange-600 ml-1">(Auto-adjusted)</span>
                    )}
                  </div>
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-900">
                    {(parseFloat(stage.commission_percentage) * 100).toFixed(1)}%
                  </div>
                  <div className="text-sm text-gray-500">Commission</div>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="flex items-center space-x-2">
          {isEditing ? (
            <>
              <button
                onClick={handleSave}
                className="p-2 text-green-600 hover:text-green-800 transition-colors"
                title="Save changes"
              >
                <SaveIcon size={16} />
              </button>
              <button
                onClick={handleCancel}
                className="p-2 text-gray-600 hover:text-gray-800 transition-colors"
                title="Cancel"
              >
                <XIcon size={16} />
              </button>
            </>
          ) : (
            <>
              <button
                onClick={() => onEdit(stage.stage_id)}
                className="p-2 text-blue-600 hover:text-blue-800 transition-colors"
                title="Edit stage"
              >
                <EditIcon size={16} />
              </button>
              <button
                onClick={() => onDelete(stage.stage_id)}
                className="p-2 text-red-600 hover:text-red-800 transition-colors"
                title="Delete stage"
              >
                <TrashIcon size={16} />
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default ManageStagesModal;
