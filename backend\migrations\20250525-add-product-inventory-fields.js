'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Helper to check if a column exists
    async function columnExists(table, column) {
      const tableInfo = await queryInterface.describeTable(table);
      return !!tableInfo[column];
    }

    // Add new fields to products table
    if (!(await columnExists('products', 'storage_location'))) {
      await queryInterface.addColumn('products', 'storage_location', {
        type: Sequelize.STRING,
        allowNull: true
      });
    }
    if (!(await columnExists('products', 'batch_number'))) {
      await queryInterface.addColumn('products', 'batch_number', {
        type: Sequelize.STRING,
        allowNull: true
      });
    }
    if (!(await columnExists('products', 'expiration_date'))) {
      await queryInterface.addColumn('products', 'expiration_date', {
        type: Sequelize.DATE,
        allowNull: true
      });
    }

    // Add new fields to inventory_transactions table
    if (!(await columnExists('inventory_transactions', 'batch_number'))) {
      await queryInterface.addColumn('inventory_transactions', 'batch_number', {
        type: Sequelize.STRING,
        allowNull: true
      });
    }
    if (!(await columnExists('inventory_transactions', 'expiration_date'))) {
      await queryInterface.addColumn('inventory_transactions', 'expiration_date', {
        type: Sequelize.DATE,
        allowNull: true
      });
    }
    if (!(await columnExists('inventory_transactions', 'storage_location'))) {
      await queryInterface.addColumn('inventory_transactions', 'storage_location', {
        type: Sequelize.STRING,
        allowNull: true
      });
    }
  },

  down: async (queryInterface, Sequelize) => {
    // Remove fields from products table
    await queryInterface.removeColumn('products', 'storage_location');
    await queryInterface.removeColumn('products', 'batch_number');
    await queryInterface.removeColumn('products', 'expiration_date');

    // Remove fields from inventory_transactions table
    await queryInterface.removeColumn('inventory_transactions', 'batch_number');
    await queryInterface.removeColumn('inventory_transactions', 'expiration_date');
    await queryInterface.removeColumn('inventory_transactions', 'storage_location');
  }
};
