import { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  PackageIcon,
  PlusIcon,
  SearchIcon,
  TruckIcon,
  CheckIcon,
  AlertCircleIcon,
  CalendarIcon,
  HashIcon,
  MapPinIcon,
  FileTextIcon,
  ShoppingCartIcon,
  UserIcon,
  DownloadIcon,
  FilterIcon,
  RefreshCwIcon,
  ClockIcon,
  XIcon
} from 'lucide-react';
import { useApiData } from '../contexts/ApiDataContext';
import { useAuth } from '../contexts/AuthContext';
import InventoryBrowser from '../components/InventoryBrowser';
import InventoryHistory from '../components/InventoryHistory';
import RefreshButton from '../components/RefreshButton';
import ProductSelector from '../components/ProductSelector';

function InventoryManagement() {
  const {
    products,
    customers,
    orders,
    inventoryTransactions,
    suppliers,
    categories,
    supplierOptions,
    categoryOptions,
    loading,
    addProduct,
    updateProductStock,
    updateOrderStatus,
    getLowStockProducts,
    getInventoryValue,
    recentDeliveries,
    fetchProducts,
    fetchCustomers,
    fetchOrders,
    fetchInventoryTransactions,
    fetchSuppliers,
    fetchCategories,
    addSupplier,
    addCategory
  } = useApiData();
  const { user } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();

  // Note: addSupplier and addCategory functions are now provided by ApiDataContext

  // Tab management based on URL
  const getTabFromPath = (pathname) => {
    if (pathname.includes('/inventory/in')) return 'in';
    if (pathname.includes('/inventory/out')) return 'out';
    if (pathname.includes('/inventory/history')) return 'history';
    return 'browser'; // Default to browser for /inventory/*
  };

  const [activeTab, setActiveTab] = useState(() => getTabFromPath(location.pathname));

  // Inventory In state
  const [inFormData, setInFormData] = useState({
    productName: '',
    category: '',
    sku: '',
    barcode: '',
    supplier: '',
    quantityReceived: '',
    unitPrice: '',
    costPrice: '',
    totalCost: '',
    expirationDate: '',
    batchNumber: '',
    storageLocation: '',
    date: new Date().toISOString().split('T')[0],
    invoiceNumber: '',
    referenceNumber: '',
    notes: '',
    isExistingProduct: false,
    selectedProductId: ''
  });

  // Track original product values for variant detection
  const [originalProductValues, setOriginalProductValues] = useState({
    unitPrice: '',
    costPrice: '',
    name: '',
    category: '',
    sku: '',
    barcode: ''
  });

  // ENHANCEMENT: Company Loss Tracking state (removed customer field)
  const [outFormData, setOutFormData] = useState({
    product: '',
    quantity: '',
    reason: '',
    notes: '',
    date: new Date().toISOString().split('T')[0]
  });

  // Inventory Browser state
  const [browserState, setBrowserState] = useState({
    searchTerm: '',
    categoryFilter: '',
    stockFilter: 'all',
    sortBy: 'name',
    sortOrder: 'asc',
    currentPage: 1,
    itemsPerPage: 20
  });

  // History state
  const [historyState, setHistoryState] = useState({
    searchTerm: '',
    typeFilter: '',
    dateFrom: '',
    dateTo: '',
    userFilter: '',
    sortBy: 'date',
    sortOrder: 'desc',
    currentPage: 1,
    itemsPerPage: 50
  });

  // UI state
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [showError, setShowError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [showNewSupplierForm, setShowNewSupplierForm] = useState(false);
  const [showNewCategoryForm, setShowNewCategoryForm] = useState(false);
  const [newSupplier, setNewSupplier] = useState('');
  const [newCategory, setNewCategory] = useState('');

  // Data loading is handled by ApiDataContext automatically

  const tabs = [
    { id: 'in', label: 'Inventory In', icon: <TruckIcon size={16} /> },
    { id: 'out', label: 'Inventory Out', icon: <ShoppingCartIcon size={16} /> },
    { id: 'browser', label: 'View All Records', icon: <SearchIcon size={16} /> },
    { id: 'history', label: 'Inventory History', icon: <ClockIcon size={16} /> }
  ];

  // Update tab when URL changes
  useEffect(() => {
    const newTab = getTabFromPath(location.pathname);
    setActiveTab(newTab);
  }, [location.pathname]);

  const handleTabChange = (tabId) => {
    setActiveTab(tabId);
    setShowSuccess(false);
    setShowError(false);
    setSuccessMessage('');
    setErrorMessage('');

    // Navigate to the appropriate URL
    const paths = {
      'in': '/inventory/in',
      'out': '/inventory/out',
      'browser': '/inventory',
      'history': '/inventory/history'
    };
    navigate(paths[tabId] || '/inventory');
  };

  // Check if variant creation is needed (price or other critical fields changed)
  const needsVariantCreation = () => {
    if (!inFormData.isExistingProduct) return false;

    // Helper function to safely compare numbers with tolerance for floating point precision
    const numbersEqual = (a, b, tolerance = 0.01) => {
      const numA = parseFloat(a) || 0;
      const numB = parseFloat(b) || 0;
      return Math.abs(numA - numB) < tolerance;
    };

    // Only check for changes if original values exist and current values are different
    const priceChanged = originalProductValues.unitPrice !== '' &&
                        inFormData.unitPrice !== '' &&
                        !numbersEqual(inFormData.unitPrice, originalProductValues.unitPrice);

    const costPriceChanged = originalProductValues.costPrice !== '' &&
                            inFormData.costPrice !== '' &&
                            !numbersEqual(inFormData.costPrice, originalProductValues.costPrice);

    const nameChanged = originalProductValues.name !== '' &&
                       inFormData.productName !== '' &&
                       inFormData.productName.trim() !== originalProductValues.name.trim();

    const categoryChanged = originalProductValues.category !== '' &&
                           inFormData.category !== '' &&
                           inFormData.category !== originalProductValues.category;

    const skuChanged = originalProductValues.sku !== inFormData.sku &&
                      inFormData.sku !== '' &&
                      inFormData.sku !== originalProductValues.sku;

    const barcodeChanged = originalProductValues.barcode !== inFormData.barcode &&
                          inFormData.barcode !== '' &&
                          inFormData.barcode !== originalProductValues.barcode;

    const supplierChanged = originalProductValues.supplier !== inFormData.supplier &&
                           inFormData.supplier !== '' &&
                           inFormData.supplier !== originalProductValues.supplier;

    // Debug logging to help identify why variant creation is triggered
    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 Variant Creation Check:', {
        priceChanged,
        costPriceChanged,
        nameChanged,
        categoryChanged,
        skuChanged,
        barcodeChanged,
        supplierChanged,
        originalValues: originalProductValues,
        currentValues: {
          unitPrice: inFormData.unitPrice,
          costPrice: inFormData.costPrice,
          name: inFormData.productName,
          category: inFormData.category,
          sku: inFormData.sku,
          barcode: inFormData.barcode
        }
      });
    }

    return priceChanged || costPriceChanged || nameChanged || categoryChanged || skuChanged || barcodeChanged || supplierChanged;
  };

  // Generate variant name with suffix
  const generateVariantName = (baseName) => {
    const existingVariants = products.filter(p =>
      p.name.startsWith(baseName) &&
      (p.name === baseName || p.name.match(new RegExp(`^${baseName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}-V\\d+$`)))
    );

    if (existingVariants.length === 0) {
      return `${baseName}-V2`;
    }

    const variantNumbers = existingVariants
      .map(p => {
        if (p.name === baseName) return 1;
        const match = p.name.match(/-V(\d+)$/);
        return match ? parseInt(match[1]) : 1;
      })
      .sort((a, b) => b - a);

    const nextVariantNumber = variantNumbers[0] + 1;
    return `${baseName}-V${nextVariantNumber}`;
  };

  // Inventory In handlers
  const handleInChange = (e) => {
    const { name, value } = e.target;
    setInFormData(prev => {
      const newData = { ...prev, [name]: value };

      // Auto-calculate total cost
      if (name === 'quantityReceived' || name === 'unitPrice') {
        const quantity = name === 'quantityReceived' ? parseFloat(value) : parseFloat(prev.quantityReceived);
        const unitPrice = name === 'unitPrice' ? parseFloat(value) : parseFloat(prev.unitPrice);
        if (!isNaN(quantity) && !isNaN(unitPrice)) {
          newData.totalCost = (quantity * unitPrice).toFixed(2);
        }
      }

      // Auto-fill cost price if not set
      if (name === 'unitPrice' && !prev.costPrice) {
        newData.costPrice = value;
      }

      // Check if product exists
      if (name === 'productName') {
        const existingProduct = products.find(p =>
          p.name.toLowerCase() === value.toLowerCase()
        );
        if (existingProduct) {
          newData.isExistingProduct = true;
          newData.selectedProductId = existingProduct.id;
          newData.category = existingProduct.category;
          newData.sku = existingProduct.sku || '';
          newData.unitPrice = existingProduct.price.toString();
          newData.costPrice = existingProduct.costPrice?.toString() || existingProduct.price.toString();

          // Store original values for variant detection
          setOriginalProductValues({
            unitPrice: existingProduct.price.toString(),
            costPrice: existingProduct.costPrice?.toString() || existingProduct.price.toString(),
            name: existingProduct.name,
            category: existingProduct.category,
            sku: existingProduct.sku || '',
            barcode: existingProduct.barcode || ''
          });
        } else {
          newData.isExistingProduct = false;
          newData.selectedProductId = '';
          // Clear original values
          setOriginalProductValues({
            unitPrice: '',
            costPrice: '',
            name: '',
            category: '',
            sku: '',
            barcode: ''
          });
        }
      }

      return newData;
    });
  };

  const resetInForm = () => {
    setInFormData({
      productName: '',
      category: '',
      sku: '',
      barcode: '',
      supplier: '',
      quantityReceived: '',
      unitPrice: '',
      costPrice: '',
      totalCost: '',
      expirationDate: '',
      batchNumber: '',
      storageLocation: '',
      date: new Date().toISOString().split('T')[0],
      invoiceNumber: '',
      referenceNumber: '',
      notes: '',
      isExistingProduct: false,
      selectedProductId: ''
    });
    // Reset original values tracking
    setOriginalProductValues({
      unitPrice: '',
      costPrice: '',
      name: '',
      category: '',
      sku: '',
      barcode: ''
    });
  };

  const handleInSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setShowError(false);
    setErrorMessage('');

    try {
      // Validate required fields
      if (!inFormData.productName || !inFormData.category || !inFormData.supplier ||
          !inFormData.quantityReceived || !inFormData.unitPrice) {
        throw new Error('Please fill in all required fields');
      }

      const quantity = parseInt(inFormData.quantityReceived);
      const unitPrice = parseFloat(inFormData.unitPrice);
      const costPrice = parseFloat(inFormData.costPrice) || unitPrice;

      if (inFormData.isExistingProduct && inFormData.selectedProductId) {
        const existingProduct = products.find(p => p.id === inFormData.selectedProductId);

        if (needsVariantCreation()) {
          // Create new product variant due to critical changes
          console.log('🔄 Creating product variant due to critical changes detected');
          const variantName = generateVariantName(originalProductValues.name);
          console.log('📝 Generated variant name:', variantName);

          // Validate required fields for variant creation
          if (!variantName?.trim()) {
            throw new Error('Variant name generation failed');
          }
          if (!inFormData.category?.trim() || inFormData.category === 'Select category') {
            throw new Error('Category is required for variant creation');
          }
          if (!unitPrice || parseFloat(unitPrice) <= 0) {
            throw new Error('Valid unit price is required for variant creation');
          }
          if (!inFormData.supplier || inFormData.supplier === 'Select supplier') {
            throw new Error('Supplier is required for variant creation');
          }

          // Transform variant data to match backend validation requirements (same as regular product creation)
          const variantData = {
            // Required fields (backend validation)
            name: variantName.trim(),
            category: inFormData.category.trim(),
            unit_price: parseFloat(unitPrice),

            // Optional fields with proper backend field names
            sku: inFormData.sku ? `${inFormData.sku.trim()}-V` : `SKU-${Date.now()}-V`,
            barcode: inFormData.barcode?.trim() || '',
            description: `Variant of ${originalProductValues.name}`,
            cost_price: parseFloat(costPrice) || parseFloat(unitPrice) * 0.7,
            current_stock: parseInt(quantity),
            min_stock_level: parseInt(existingProduct.minStock || 50),
            max_stock_level: parseInt(existingProduct.maxStock || 1000),
            unit_of_measure: 'pieces',
            supplier_id: inFormData.supplier, // This should be the supplier ID
            storage_location: inFormData.storageLocation?.trim() || existingProduct.storageLocation || '',
            batch_number: inFormData.batchNumber?.trim() || '',
            expiration_date: inFormData.expirationDate || null,

            // Transaction details for inventory transaction
            invoice_number: inFormData.invoiceNumber?.trim() || '',
            reference_number: inFormData.referenceNumber?.trim() || '',
            notes: `Variant created due to critical changes. Original: ${originalProductValues.name}. ${inFormData.notes?.trim() || ''}`,
            recorded_by: user?.user_id || user?.email || 'System'
          };

          console.log('🔄 Transformed variant data for backend:', {
            original: {
              name: variantName,
              category: inFormData.category,
              price: unitPrice,
              stock: quantity,
              supplier: inFormData.supplier
            },
            transformed: {
              name: variantData.name,
              category: variantData.category,
              unit_price: variantData.unit_price,
              current_stock: variantData.current_stock,
              supplier_id: variantData.supplier_id
            }
          });

          const variantResult = await addProduct(variantData);
          console.log('✅ Product variant created successfully:', variantResult);

          // Set custom success message for variant creation
          setSuccessMessage(`New product variant "${variantName}" created successfully! Original product "${originalProductValues.name}" preserved with its historical data.`);
        } else {
          // Normal stock update - only quantity changed
          const newStock = existingProduct.stock + quantity;

          updateProductStock(inFormData.selectedProductId, newStock, 'in', {
            supplier: inFormData.supplier,
            invoiceNumber: inFormData.invoiceNumber,
            referenceNumber: inFormData.referenceNumber,
            batchNumber: inFormData.batchNumber,
            expirationDate: inFormData.expirationDate,
            storageLocation: inFormData.storageLocation,
            notes: inFormData.notes,
            recordedBy: user?.user_id || user?.email || 'System',
            unitPrice: unitPrice,
            costPrice: costPrice
          });

          // Set custom success message for stock update
          setSuccessMessage(`Stock updated successfully! Added ${quantity} units to "${existingProduct.name}".`);
        }
      } else {
        // Create new product
        // Validate required fields before transformation
        if (!inFormData.productName?.trim()) {
          throw new Error('Product name is required');
        }
        if (!inFormData.category?.trim() || inFormData.category === 'Select category') {
          throw new Error('Category is required');
        }
        if (!unitPrice || parseFloat(unitPrice) <= 0) {
          throw new Error('Valid unit price is required');
        }
        if (!inFormData.supplier || inFormData.supplier === 'Select supplier') {
          throw new Error('Supplier is required');
        }

        // Transform data to match backend validation requirements
        const productData = {
          // Required fields (backend validation)
          name: inFormData.productName.trim(),
          category: inFormData.category.trim(),
          unit_price: parseFloat(unitPrice),

          // Optional fields with proper backend field names
          sku: inFormData.sku?.trim() || `SKU-${Date.now()}`,
          barcode: inFormData.barcode?.trim() || '',
          description: inFormData.description?.trim() || '',
          cost_price: parseFloat(costPrice) || parseFloat(unitPrice) * 0.7,
          current_stock: parseInt(quantity),
          min_stock_level: parseInt(inFormData.minStock || 50),
          max_stock_level: parseInt(inFormData.maxStock || 1000),
          unit_of_measure: 'pieces',
          supplier_id: inFormData.supplier, // This should be the supplier ID
          storage_location: inFormData.storageLocation?.trim() || '',
          batch_number: inFormData.batchNumber?.trim() || '',
          expiration_date: inFormData.expirationDate || null,

          // Transaction details for inventory transaction
          invoice_number: inFormData.invoiceNumber?.trim() || '',
          reference_number: inFormData.referenceNumber?.trim() || '',
          notes: inFormData.notes?.trim() || `New product added via InventoryIn - ${quantity} units received`,
          recorded_by: user?.user_id || user?.email || 'System'
        };

        console.log('🔄 Transformed product data for backend:', {
          original: {
            name: inFormData.productName,
            category: inFormData.category,
            price: unitPrice,
            stock: quantity,
            supplier: inFormData.supplier
          },
          transformed: {
            name: productData.name,
            category: productData.category,
            unit_price: productData.unit_price,
            current_stock: productData.current_stock,
            supplier_id: productData.supplier_id
          }
        });

        await addProduct(productData);

        // Set custom success message for new product
        setSuccessMessage(`New product "${inFormData.productName}" added successfully with ${quantity} units!`);
      }

      setShowSuccess(true);
      setTimeout(() => {
        setShowSuccess(false);
        resetInForm();
      }, 3000);

    } catch (error) {
      console.error('Error processing inventory:', error);
      setErrorMessage(error.message || 'Failed to process inventory. Please try again.');
      setShowError(true);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Add new supplier
  const handleAddSupplier = async () => {
    if (newSupplier.trim()) {
      try {
        console.log('🏭 Creating new supplier:', newSupplier);
        const supplier = await addSupplier(newSupplier);
        console.log('✅ Supplier created:', supplier);

        setNewSupplier('');
        setShowNewSupplierForm(false);

        // Update the form to use the new supplier ID
        setInFormData(prev => ({ ...prev, supplier: supplier.id }));

        // Show success message
        setSuccessMessage(`New supplier "${supplier.name}" has been created successfully and is now available in the dropdown!`);
        setShowSuccess(true);
        setTimeout(() => {
          setShowSuccess(false);
          setSuccessMessage('');
        }, 3000);
      } catch (error) {
        console.error('❌ Failed to create supplier:', error);
        setErrorMessage(`Failed to add supplier: ${error.message}`);
        setShowError(true);
        setTimeout(() => setShowError(false), 3000);
      }
    }
  };

  // Add new category
  const handleAddCategory = async () => {
    if (newCategory.trim()) {
      try {
        console.log('📂 Creating new category:', newCategory);
        const category = await addCategory(newCategory);
        console.log('✅ Category created:', category);

        setNewCategory('');
        setShowNewCategoryForm(false);

        // Update the form to use the new category NAME (not ID)
        setInFormData(prev => ({ ...prev, category: category.name }));

        // Show success message
        setSuccessMessage(`New category "${category.name}" has been created successfully and is now available in the dropdown!`);
        setShowSuccess(true);
        setTimeout(() => {
          setShowSuccess(false);
          setSuccessMessage('');
        }, 3000);
      } catch (error) {
        console.error('❌ Failed to create category:', error);
        setErrorMessage(`Failed to add category: ${error.message}`);
        setShowError(true);
        setTimeout(() => setShowError(false), 3000);
      }
    }
  };

  // Refresh inventory data
  const refreshInventory = async () => {
    console.log('🔄 Refreshing inventory data...');
    try {
      await Promise.all([
        fetchProducts(),
        fetchCustomers(),
        fetchOrders(),
        fetchInventoryTransactions(),
        fetchSuppliers(),
        fetchCategories()
      ]);
      console.log('✅ Inventory data refreshed successfully');
    } catch (error) {
      console.error('❌ Failed to refresh inventory:', error);
      setErrorMessage('Failed to refresh inventory data. Please try again.');
      setShowError(true);
      setTimeout(() => setShowError(false), 3000);
    }
  };

  return (
    <div>
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Inventory Management</h1>
          <p className="text-gray-600">Comprehensive inventory control system</p>
        </div>
        <div className="flex items-center space-x-4">
          <RefreshButton
            onRefresh={refreshInventory}
            variant="outline"
            size="md"
            label="Refresh Inventory"
          />
          <div className="flex space-x-3">
            <div className="text-right">
              <p className="text-sm text-gray-500">Current Inventory Value</p>
              <p className="text-lg font-semibold text-green-600">${getInventoryValue().toFixed(2)}</p>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-500">Low Stock Items</p>
              <p className="text-lg font-semibold text-red-600">{getLowStockProducts().length}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white rounded-lg shadow-md mb-6">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => handleTabChange(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.icon}
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Success/Error Messages */}
      {showSuccess && (
        <div className="bg-green-50 border-l-4 border-green-500 p-4 mb-6 flex items-start">
          <CheckIcon size={20} className="text-green-500 mr-2 mt-0.5" />
          <div>
            <p className="text-green-700 font-medium">
              {successMessage ? 'Success!' : (inFormData.isExistingProduct ? 'Stock Updated Successfully!' : 'New Product Added Successfully!')}
            </p>
            <p className="text-green-600">
              {successMessage || (inFormData.isExistingProduct
                ? `Stock increased by ${inFormData.quantityReceived} units for ${inFormData.productName}`
                : `${inFormData.productName} has been added to inventory with ${inFormData.quantityReceived} units`
              )}
            </p>
          </div>
        </div>
      )}

      {showError && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6 flex items-start">
          <AlertCircleIcon size={20} className="text-red-500 mr-2 mt-0.5" />
          <div>
            <p className="text-red-700 font-medium">Error Processing Inventory</p>
            <div className="text-red-600 whitespace-pre-line">{errorMessage}</div>
          </div>
        </div>
      )}

      {/* Tab Content */}
      <div className="bg-white rounded-lg shadow-md p-6">

        {/* Inventory In Tab */}
        {activeTab === 'in' && (
          <div>
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center">
                <PackageIcon size={20} className="text-blue-600 mr-2" />
                <h2 className="text-lg font-medium text-gray-800">Comprehensive Inventory Intake</h2>
              </div>
              {inFormData.isExistingProduct && (
                <div className="bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-sm font-medium">
                  Updating Existing Product
                </div>
              )}
            </div>

            <form onSubmit={handleInSubmit}>
              {/* Product Information Section */}
              <div className="mb-8">
                <h3 className="text-md font-semibold text-gray-700 mb-4 flex items-center">
                  <PackageIcon size={16} className="mr-2" />
                  Product Information
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div>
                    <label htmlFor="productName" className="block text-sm font-medium text-gray-700 mb-1">
                      Product Name *
                    </label>
                    <ProductSelector
                      products={products}
                      value={inFormData.productName}
                      onChange={(productName) => {
                        // Find the selected product for auto-fill
                        const selectedProduct = products.find(p => p.name.toLowerCase() === productName.toLowerCase());

                        if (selectedProduct) {
                          // Auto-fill all related fields for existing product
                          setInFormData(prev => ({
                            ...prev,
                            productName: productName,
                            isExistingProduct: true,
                            selectedProductId: selectedProduct.id,
                            unitPrice: selectedProduct.price?.toString() || '',
                            costPrice: selectedProduct.costPrice?.toString() || '',
                            category: selectedProduct.category || '',
                            supplier: selectedProduct.supplierId || '',
                            sku: selectedProduct.sku || '',
                            barcode: selectedProduct.barcode || '',
                            description: selectedProduct.description || ''
                          }));

                          // Store original values for variant creation detection
                          setOriginalProductValues({
                            name: selectedProduct.name,
                            unitPrice: selectedProduct.price?.toString() || '',
                            costPrice: selectedProduct.costPrice?.toString() || '',
                            category: selectedProduct.category || '',
                            supplier: selectedProduct.supplierId || '',
                            sku: selectedProduct.sku || '',
                            barcode: selectedProduct.barcode || ''
                          });

                          console.log('🔍 Auto-filled existing product:', {
                            product: selectedProduct.name,
                            category: selectedProduct.category,
                            supplier: selectedProduct.supplier,
                            price: selectedProduct.price
                          });
                        } else {
                          // New product - clear auto-filled fields
                          setInFormData(prev => ({
                            ...prev,
                            productName: productName,
                            isExistingProduct: false,
                            selectedProductId: null,
                            unitPrice: '',
                            costPrice: '',
                            category: '',
                            supplier: '',
                            sku: '',
                            barcode: '',
                            description: ''
                          }));

                          setOriginalProductValues({});

                          console.log('🔍 New product selected:', productName);
                        }
                      }}
                      onNewProduct={(isNew) => {
                        console.log('Product selection type:', isNew ? 'New product' : 'Existing product');
                      }}
                      placeholder="Select existing product or type new product name..."
                      className="mb-1"
                    />
                  </div>

                  <div>
                    <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
                      Category *
                    </label>
                    <div className="flex space-x-2">
                      <select
                        id="category"
                        name="category"
                        value={inFormData.category}
                        onChange={handleInChange}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        required
                      >
                        {categoryOptions.map(option => (
                          <option key={option.value} value={option.value}>{option.label}</option>
                        ))}
                      </select>
                      <button
                        type="button"
                        onClick={() => setShowNewCategoryForm(true)}
                        className="px-3 py-2 bg-gray-100 text-gray-600 rounded-md hover:bg-gray-200 transition-colors"
                        title="Add New Category"
                      >
                        <PlusIcon size={16} />
                      </button>
                    </div>
                    {showNewCategoryForm && (
                      <div className="mt-2 flex space-x-2">
                        <input
                          type="text"
                          value={newCategory}
                          onChange={(e) => setNewCategory(e.target.value)}
                          placeholder="Enter new category"
                          className="flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        />
                        <button
                          type="button"
                          onClick={handleAddCategory}
                          className="px-2 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700"
                        >
                          Add
                        </button>
                        <button
                          type="button"
                          onClick={() => {setShowNewCategoryForm(false); setNewCategory('');}}
                          className="px-2 py-1 bg-gray-400 text-white text-sm rounded hover:bg-gray-500"
                        >
                          <XIcon size={12} />
                        </button>
                      </div>
                    )}
                  </div>

                  <div>
                    <label htmlFor="sku" className="block text-sm font-medium text-gray-700 mb-1">
                      SKU/Product Code
                    </label>
                    <div className="relative">
                      <HashIcon size={16} className="absolute left-3 top-3 text-gray-400" />
                      <input
                        type="text"
                        id="sku"
                        name="sku"
                        value={inFormData.sku}
                        onChange={handleInChange}
                        className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Enter SKU"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="barcode" className="block text-sm font-medium text-gray-700 mb-1">
                      Barcode
                    </label>
                    <input
                      type="text"
                      id="barcode"
                      name="barcode"
                      value={inFormData.barcode}
                      onChange={handleInChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter barcode"
                    />
                  </div>
                </div>
              </div>

              {/* Supplier Information Section */}
              <div className="mb-8">
                <h3 className="text-md font-semibold text-gray-700 mb-4 flex items-center">
                  <TruckIcon size={16} className="mr-2" />
                  Supplier Information
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="supplier" className="block text-sm font-medium text-gray-700 mb-1">
                      Supplier *
                    </label>
                    <div className="flex space-x-2">
                      <select
                        id="supplier"
                        name="supplier"
                        value={inFormData.supplier}
                        onChange={handleInChange}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        required
                      >
                        {supplierOptions.map(option => (
                          <option key={option.value} value={option.value}>{option.label}</option>
                        ))}
                      </select>
                      <button
                        type="button"
                        onClick={() => setShowNewSupplierForm(true)}
                        className="px-3 py-2 bg-gray-100 text-gray-600 rounded-md hover:bg-gray-200 transition-colors"
                        title="Add New Supplier"
                      >
                        <PlusIcon size={16} />
                      </button>
                    </div>
                    {showNewSupplierForm && (
                      <div className="mt-2 flex space-x-2">
                        <input
                          type="text"
                          value={newSupplier}
                          onChange={(e) => setNewSupplier(e.target.value)}
                          placeholder="Enter new supplier name"
                          className="flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        />
                        <button
                          type="button"
                          onClick={handleAddSupplier}
                          className="px-2 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700"
                        >
                          Add
                        </button>
                        <button
                          type="button"
                          onClick={() => {setShowNewSupplierForm(false); setNewSupplier('');}}
                          className="px-2 py-1 bg-gray-400 text-white text-sm rounded hover:bg-gray-500"
                        >
                          <XIcon size={12} />
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Quantity & Pricing Section */}
              <div className="mb-8">
                <h3 className="text-md font-semibold text-gray-700 mb-4 flex items-center">
                  <HashIcon size={16} className="mr-2" />
                  Quantity & Pricing
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <div>
                    <label htmlFor="quantityReceived" className="block text-sm font-medium text-gray-700 mb-1">
                      Quantity Received *
                    </label>
                    <input
                      type="number"
                      id="quantityReceived"
                      name="quantityReceived"
                      value={inFormData.quantityReceived}
                      onChange={handleInChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter quantity"
                      min="1"
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="unitPrice" className="block text-sm font-medium text-gray-700 mb-1">
                      Selling Price ($) *
                      <span className="text-xs text-blue-600 block">Price charged to customers</span>
                      {inFormData.isExistingProduct && originalProductValues.unitPrice !== '' &&
                       parseFloat(inFormData.unitPrice) !== parseFloat(originalProductValues.unitPrice) && (
                        <span className="text-xs text-yellow-600 block font-medium">
                          ⚠️ Price changed (was ${originalProductValues.unitPrice})
                        </span>
                      )}
                    </label>
                    <input
                      type="number"
                      id="unitPrice"
                      name="unitPrice"
                      value={inFormData.unitPrice}
                      onChange={handleInChange}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 ${
                        inFormData.isExistingProduct && originalProductValues.unitPrice !== '' &&
                        parseFloat(inFormData.unitPrice) !== parseFloat(originalProductValues.unitPrice)
                          ? 'border-yellow-300 bg-yellow-50 focus:ring-yellow-500'
                          : 'border-gray-300 focus:ring-blue-500'
                      }`}
                      placeholder="0.00"
                      step="0.01"
                      min="0"
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="costPrice" className="block text-sm font-medium text-gray-700 mb-1">
                      Cost Price ($)
                      <span className="text-xs text-gray-500 block">What you paid for this product</span>
                      {inFormData.isExistingProduct && originalProductValues.costPrice !== '' &&
                       parseFloat(inFormData.costPrice || 0) !== parseFloat(originalProductValues.costPrice || 0) && (
                        <span className="text-xs text-yellow-600 block font-medium">
                          ⚠️ Cost price changed (was ${originalProductValues.costPrice})
                        </span>
                      )}
                    </label>
                    <input
                      type="number"
                      id="costPrice"
                      name="costPrice"
                      value={inFormData.costPrice}
                      onChange={handleInChange}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 ${
                        inFormData.isExistingProduct && originalProductValues.costPrice !== '' &&
                        parseFloat(inFormData.costPrice || 0) !== parseFloat(originalProductValues.costPrice || 0)
                          ? 'border-yellow-300 bg-yellow-50 focus:ring-yellow-500'
                          : 'border-gray-300 focus:ring-blue-500'
                      }`}
                      placeholder="0.00"
                      step="0.01"
                      min="0"
                    />
                  </div>

                  <div>
                    <label htmlFor="totalCost" className="block text-sm font-medium text-gray-700 mb-1">
                      Total Cost ($)
                    </label>
                    <input
                      type="text"
                      id="totalCost"
                      name="totalCost"
                      value={inFormData.totalCost}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 focus:outline-none font-semibold text-green-600"
                      placeholder="0.00"
                      readOnly
                    />
                    {inFormData.unitPrice && inFormData.costPrice && (
                      <div className="mt-1 text-xs">
                        <span className={`font-medium ${
                          parseFloat(inFormData.unitPrice) > parseFloat(inFormData.costPrice)
                            ? 'text-green-600'
                            : parseFloat(inFormData.unitPrice) < parseFloat(inFormData.costPrice)
                            ? 'text-red-600'
                            : 'text-gray-600'
                        }`}>
                          Profit: ${(parseFloat(inFormData.unitPrice || 0) - parseFloat(inFormData.costPrice || 0)).toFixed(2)} per unit
                          {parseFloat(inFormData.costPrice) > 0 && (
                            <span className="ml-1">
                              ({(((parseFloat(inFormData.unitPrice || 0) - parseFloat(inFormData.costPrice || 0)) / parseFloat(inFormData.costPrice)) * 100).toFixed(1)}%)
                            </span>
                          )}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Product Details Section */}
              <div className="mb-8">
                <h3 className="text-md font-semibold text-gray-700 mb-4 flex items-center">
                  <FileTextIcon size={16} className="mr-2" />
                  Product Details
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div>
                    <label htmlFor="expirationDate" className="block text-sm font-medium text-gray-700 mb-1">
                      Expiration Date
                    </label>
                    <div className="relative">
                      <CalendarIcon size={16} className="absolute left-3 top-3 text-gray-400" />
                      <input
                        type="date"
                        id="expirationDate"
                        name="expirationDate"
                        value={inFormData.expirationDate}
                        onChange={handleInChange}
                        className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="batchNumber" className="block text-sm font-medium text-gray-700 mb-1">
                      Batch/Lot Number
                    </label>
                    <input
                      type="text"
                      id="batchNumber"
                      name="batchNumber"
                      value={inFormData.batchNumber}
                      onChange={handleInChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter batch number"
                    />
                  </div>

                  <div>
                    <label htmlFor="storageLocation" className="block text-sm font-medium text-gray-700 mb-1">
                      Storage Location
                    </label>
                    <div className="relative">
                      <MapPinIcon size={16} className="absolute left-3 top-3 text-gray-400" />
                      <input
                        type="text"
                        id="storageLocation"
                        name="storageLocation"
                        value={inFormData.storageLocation}
                        onChange={handleInChange}
                        className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="e.g., Warehouse A, Shelf 3"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Transaction Details Section */}
              <div className="mb-8">
                <h3 className="text-md font-semibold text-gray-700 mb-4 flex items-center">
                  <FileTextIcon size={16} className="mr-2" />
                  Transaction Details
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div>
                    <label htmlFor="date" className="block text-sm font-medium text-gray-700 mb-1">
                      Date *
                    </label>
                    <input
                      type="date"
                      id="date"
                      name="date"
                      value={inFormData.date}
                      onChange={handleInChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="invoiceNumber" className="block text-sm font-medium text-gray-700 mb-1">
                      Invoice Number
                    </label>
                    <input
                      type="text"
                      id="invoiceNumber"
                      name="invoiceNumber"
                      value={inFormData.invoiceNumber}
                      onChange={handleInChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter invoice number"
                    />
                  </div>

                  <div>
                    <label htmlFor="referenceNumber" className="block text-sm font-medium text-gray-700 mb-1">
                      Reference Number
                    </label>
                    <input
                      type="text"
                      id="referenceNumber"
                      name="referenceNumber"
                      value={inFormData.referenceNumber}
                      onChange={handleInChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter reference number"
                    />
                  </div>
                </div>

                <div className="mt-6">
                  <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
                    Notes
                  </label>
                  <textarea
                    id="notes"
                    name="notes"
                    value={inFormData.notes}
                    onChange={handleInChange}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter any additional notes about this inventory transaction"
                  ></textarea>
                </div>
              </div>

              {/* Variant Creation Warning */}
              {inFormData.isExistingProduct && needsVariantCreation() && (
                <div className="mb-6 bg-yellow-50 border-l-4 border-yellow-500 p-4">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-yellow-800">
                        Product Variant Will Be Created
                      </h3>
                      <div className="mt-2 text-sm text-yellow-700">
                        <p>
                          <strong>Critical product changes detected.</strong> This will create a new product variant instead of updating the existing product to preserve historical data and pricing integrity.
                        </p>
                        <div className="mt-3 space-y-1">
                          <p><strong>Original Product:</strong> {originalProductValues.name}</p>
                          <p><strong>New Variant Name:</strong> {generateVariantName(originalProductValues.name)}</p>

                          {/* Show specific changes that triggered variant creation */}
                          {originalProductValues.unitPrice !== '' && inFormData.unitPrice !== '' &&
                           Math.abs(parseFloat(inFormData.unitPrice) - parseFloat(originalProductValues.unitPrice)) >= 0.01 && (
                            <p><strong>Unit Price Change:</strong> ${originalProductValues.unitPrice} → ${inFormData.unitPrice}</p>
                          )}
                          {originalProductValues.costPrice !== '' && inFormData.costPrice !== '' &&
                           Math.abs(parseFloat(inFormData.costPrice || 0) - parseFloat(originalProductValues.costPrice || 0)) >= 0.01 && (
                            <p><strong>Cost Price Change:</strong> ${originalProductValues.costPrice} → ${inFormData.costPrice}</p>
                          )}
                          {originalProductValues.name !== '' && inFormData.productName !== '' &&
                           inFormData.productName.trim() !== originalProductValues.name.trim() && (
                            <p><strong>Name Change:</strong> {originalProductValues.name} → {inFormData.productName}</p>
                          )}
                          {originalProductValues.category !== '' && inFormData.category !== '' &&
                           inFormData.category !== originalProductValues.category && (
                            <p><strong>Category Change:</strong> {originalProductValues.category} → {inFormData.category}</p>
                          )}
                          {originalProductValues.sku !== inFormData.sku && inFormData.sku !== '' && (
                            <p><strong>SKU Change:</strong> {originalProductValues.sku || 'None'} → {inFormData.sku}</p>
                          )}
                          {originalProductValues.barcode !== inFormData.barcode && inFormData.barcode !== '' && (
                            <p><strong>Barcode Change:</strong> {originalProductValues.barcode || 'None'} → {inFormData.barcode}</p>
                          )}
                          {originalProductValues.supplier !== inFormData.supplier && inFormData.supplier !== '' && (
                            <p><strong>Supplier Change:</strong> {suppliers.find(s => s.id === originalProductValues.supplier)?.name || 'None'} → {suppliers.find(s => s.id === inFormData.supplier)?.name || 'Unknown'}</p>
                          )}
                        </div>
                        <p className="mt-3 text-xs">
                          <strong>Note:</strong> Adding stock quantity alone will NOT create a variant - only changes to price, name, category, SKU, barcode, or supplier trigger variant creation. The original product will remain unchanged with its historical data intact.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Submit Buttons */}
              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                  onClick={resetInForm}
                >
                  Clear Form
                </button>
                <button
                  type="submit"
                  className="flex items-center bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:bg-blue-400 transition-colors"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      {inFormData.isExistingProduct
                        ? (needsVariantCreation() ? 'Creating Variant...' : 'Updating Stock...')
                        : 'Adding Product...'
                      }
                    </>
                  ) : (
                    <>
                      <PlusIcon size={16} className="mr-2" />
                      {inFormData.isExistingProduct
                        ? (needsVariantCreation() ? 'Add New Product Variant' : 'Update Stock')
                        : 'Add to Inventory'
                      }
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        )}

        {/* Inventory Out Tab */}
        {activeTab === 'out' && (
          <div>
            <div className="flex items-center mb-6">
              <ShoppingCartIcon size={20} className="text-blue-600 mr-2" />
              <h2 className="text-lg font-medium text-gray-800">Inventory Out Management</h2>
            </div>

            {/* Automatic Order Fulfillment Section */}
            {/*
              FIXED: Order details display issues & Order fulfillment errors
              - Fixed field name mismatches (order_date vs date, order_number vs orderNumber, etc.)
              - Fixed product matching logic (product_id vs productId, multiple ID formats)
              - Added comprehensive product lookup with fallbacks
              - Enhanced order item display with SKU and better stock status
              - Added debugging for data flow troubleshooting
              - Fixed order fulfillment validation errors by adding backend status validation
              - Enhanced error handling with detailed error messages and debugging
              - Added proper order ID handling for fulfillment process
            */}
            <div className="mb-8">
              <h3 className="text-md font-semibold text-gray-700 mb-4 flex items-center">
                <CheckIcon size={16} className="mr-2" />
                Automatic Order Fulfillment
              </h3>
              <div className="bg-gray-50 rounded-lg p-4">
                {/* Debug information */}
                {console.log('🔍 Available data for order fulfillment:', {
                  ordersCount: orders.length,
                  pendingOrdersCount: orders.filter(order => order.status === 'pending').length,
                  productsCount: products.length,
                  customersCount: customers.length,
                  sampleOrder: orders.find(order => order.status === 'pending'),
                  sampleProduct: products[0]
                })}

                {orders.filter(order => order.status === 'pending').length > 0 ? (
                  <div className="space-y-4">
                    {orders.filter(order => order.status === 'pending').map((order) => {
                      console.log('🔍 Rendering order:', {
                        id: order.id || order.order_id,
                        orderNumber: order.order_number || order.orderNumber,
                        status: order.status,
                        items: order.items?.length || 0,
                        customer: order.customer?.name || order.customerName,
                        totalAmount: order.total_amount || order.totalAmount
                      });

                      return (
                      <div key={order.id || order.order_id} className="bg-white rounded-lg border p-4">
                        <div className="flex justify-between items-start mb-3">
                          <div>
                            <h4 className="font-medium text-gray-900">
                              Order #{order.order_number || order.orderNumber || order.id}
                            </h4>
                            <p className="text-sm text-gray-600">
                              Customer: {customers.find(c => c.id === (order.customer_id || order.customerId))?.name ||
                                        order.customer?.name ||
                                        order.customerName ||
                                        (customers.length === 0 ? 'No customers in system' : 'Customer not found')}
                            </p>
                            <p className="text-sm text-gray-600">
                              Salesman: {order.salesman?.full_name || order.salesman?.name || 'No salesman assigned'}
                            </p>
                            <p className="text-sm text-gray-600">
                              Date: {order.order_date ? new Date(order.order_date).toLocaleDateString() :
                                     order.date ? new Date(order.date).toLocaleDateString() : 'No date'}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="font-semibold text-lg">
                              ${(order.total_amount || order.totalAmount || 0).toFixed(2)}
                            </p>
                            <span className="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">
                              {order.status || 'Pending'}
                            </span>
                          </div>
                        </div>

                        <div className="mb-3">
                          <h5 className="text-sm font-medium text-gray-700 mb-2">Items:</h5>
                          <div className="space-y-2">
                            {order.items && order.items.length > 0 ? (
                              order.items.map((item, index) => {
                                console.log('🔍 Processing order item:', {
                                  item,
                                  productId: item.product_id || item.productId,
                                  productName: item.product?.name || item.productName || item.name
                                });

                                // Try multiple ways to find the product
                                const productId = item.product_id || item.productId;
                                const product = products.find(p =>
                                  p.id === productId ||
                                  p.product_id === productId ||
                                  (typeof p.id === 'string' && typeof productId === 'string' && p.id === productId) ||
                                  (typeof p.id === 'number' && typeof productId === 'number' && p.id === productId)
                                );

                                // Get product name from multiple sources
                                const productName = item.product?.name || item.productName || item.name || 'Unknown Product';

                                // Get quantity from multiple sources
                                const quantity = item.quantity || 0;

                                // Check stock availability
                                const currentStock = product?.stock || product?.current_stock || 0;
                                const hasStock = product && currentStock >= quantity;

                                console.log('🔍 Product matching result:', {
                                  productId,
                                  productFound: !!product,
                                  productName,
                                  quantity,
                                  currentStock,
                                  hasStock
                                });

                                return (
                                  <div key={index} className="flex justify-between items-center text-sm">
                                    <div className="flex-1">
                                      <span className="font-medium">{productName}</span>
                                      {product?.sku && (
                                        <span className="text-gray-500 text-xs ml-2">SKU: {product.sku}</span>
                                      )}
                                    </div>
                                    <div className="flex items-center space-x-2">
                                      <span className="text-gray-600">Qty: {quantity}</span>
                                      <span className={`px-2 py-1 rounded text-xs font-medium ${
                                        hasStock ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                      }`}>
                                        {product ?
                                          (hasStock ? `${currentStock} available` : `Only ${currentStock} available`) :
                                          'Product not found'
                                        }
                                      </span>
                                    </div>
                                  </div>
                                );
                              })
                            ) : (
                              <div className="text-sm text-gray-500 italic bg-gray-50 p-3 rounded">
                                ⚠️ This order has no items. This may indicate:
                                <ul className="mt-2 ml-4 list-disc text-xs">
                                  <li>Order was created without products</li>
                                  <li>Data structure mismatch</li>
                                  <li>Order is incomplete</li>
                                </ul>
                                <p className="mt-2 text-xs text-red-600">
                                  Cannot fulfill order without items.
                                </p>
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="flex justify-end space-x-2">
                          <button
                            onClick={async () => {
                              // Validate that the order has items
                              if (!order.items || order.items.length === 0) {
                                setErrorMessage('Cannot fulfill order: Order has no items. Please check the order data or recreate the order with products.');
                                setShowError(true);
                                setTimeout(() => setShowError(false), 3000);
                                return;
                              }

                              // Validate that products exist in the system
                              if (products.length === 0) {
                                setErrorMessage('No products available in inventory. Please add products using the "Inventory In" tab first.');
                                setShowError(true);
                                setTimeout(() => setShowError(false), 3000);
                                return;
                              }

                              // Check each item for stock availability and build detailed error message
                              const stockIssues = [];
                              const canFulfill = order.items.every(item => {
                                // Try multiple ways to find the product (same logic as display)
                                const productId = item.product_id || item.productId;
                                const product = products.find(p =>
                                  p.id === productId ||
                                  p.product_id === productId ||
                                  (typeof p.id === 'string' && typeof productId === 'string' && p.id === productId) ||
                                  (typeof p.id === 'number' && typeof productId === 'number' && p.id === productId)
                                );

                                const productName = item.product?.name || item.productName || item.name || 'Unknown Product';
                                const quantity = item.quantity || 0;
                                const currentStock = product?.stock || product?.current_stock || 0;

                                if (!product) {
                                  stockIssues.push(`${productName}: Product not found in inventory`);
                                  return false;
                                } else if (currentStock < quantity) {
                                  stockIssues.push(`${productName}: Need ${quantity}, only ${currentStock} available`);
                                  return false;
                                }
                                return true;
                              });

                              if (canFulfill) {
                                try {
                                  console.log('🚀 Fulfilling order:', {
                                    orderNumber: order.order_number || order.orderNumber,
                                    orderId: order.id || order.order_id,
                                    status: order.status,
                                    itemsCount: order.items?.length
                                  });

                                  // Update order status to delivered (backend will automatically handle inventory reduction and transaction creation)
                                  const orderId = order.id || order.order_id;
                                  console.log('📋 Calling updateOrderStatus with:', { orderId, status: 'delivered' });

                                  await updateOrderStatus(orderId, 'delivered');

                                  // No need to manually create inventory transactions - backend handles this automatically
                                  // The backend updateOrderStatus function already:
                                  // 1. Reduces product stock quantities
                                  // 2. Creates inventory transactions for each item
                                  // 3. Updates order status to delivered

                                  setSuccessMessage(`Order ${order.orderNumber} has been fulfilled successfully!`);
                                  setShowSuccess(true);
                                  setTimeout(() => {
                                    setShowSuccess(false);
                                    setSuccessMessage('');
                                  }, 3000);
                                } catch (error) {
                                  console.error('❌ Failed to fulfill order:', {
                                    error: error.message,
                                    stack: error.stack,
                                    orderId: order.id || order.order_id,
                                    orderNumber: order.order_number || order.orderNumber
                                  });

                                  // Show detailed error message
                                  const errorMsg = error.message || 'Unknown error occurred';
                                  setErrorMessage(`Failed to process order fulfillment: ${errorMsg}\n\nOrder: ${order.order_number || order.orderNumber || order.id}\nPlease check the console for more details.`);
                                  setShowError(true);
                                  setTimeout(() => setShowError(false), 8000); // Longer timeout for detailed error
                                }
                              } else {
                                // Show detailed stock issues
                                const errorMsg = stockIssues.length > 0
                                  ? `Cannot fulfill order:\n${stockIssues.join('\n')}`
                                  : 'Insufficient stock for one or more items';
                                setErrorMessage(errorMsg);
                                setShowError(true);
                                setTimeout(() => setShowError(false), 5000); // Longer timeout for detailed message
                              }
                            }}
                            disabled={!order.items || order.items.length === 0}
                            className={`px-4 py-2 rounded-md transition-colors ${
                              !order.items || order.items.length === 0
                                ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                                : 'bg-green-600 text-white hover:bg-green-700'
                            }`}
                          >
                            {!order.items || order.items.length === 0 ? 'No Items to Fulfill' : 'Fulfill Order'}
                          </button>
                          <button
                            onClick={async () => {
                              // FIXED: Add proper error handling and user feedback for cancel order
                              try {
                                console.log('🚫 Cancelling order:', {
                                  orderId: order.id || order.order_id,
                                  orderNumber: order.order_number || order.orderNumber
                                });

                                await updateOrderStatus(order.id || order.order_id, 'cancelled');

                                // Show success message
                                setSuccessMessage(`Order ${order.order_number || order.orderNumber || order.id} has been cancelled successfully!`);
                                setShowSuccess(true);
                                setTimeout(() => {
                                  setShowSuccess(false);
                                  setSuccessMessage('');
                                }, 3000);
                              } catch (error) {
                                console.error('❌ Failed to cancel order:', {
                                  error: error.message,
                                  orderId: order.id || order.order_id,
                                  orderNumber: order.order_number || order.orderNumber
                                });

                                // Show error message
                                setErrorMessage(`Failed to cancel order: ${error.message || 'Unknown error occurred'}`);
                                setShowError(true);
                                setTimeout(() => setShowError(false), 5000);
                              }
                            }}
                            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                          >
                            Cancel Order
                          </button>
                        </div>
                      </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="text-center py-12 text-gray-500">
                    <ShoppingCartIcon size={64} className="mx-auto mb-4 text-gray-300" />
                    <h3 className="text-lg font-medium text-gray-700 mb-2">No Pending Orders</h3>
                    <p className="text-gray-500">There are currently no pending orders to fulfill.</p>
                  </div>
                )}
              </div>
            </div>

            {/* ENHANCEMENT: Company Loss Tracking Section */}
            <div className="mb-8">
              <h3 className="text-md font-semibold text-red-700 mb-4 flex items-center">
                <AlertCircleIcon size={16} className="mr-2" />
                Company Loss Tracking
              </h3>
              <form onSubmit={(e) => {
                e.preventDefault();

                // ENHANCEMENT: Company Loss Tracking Logic
                // Validate that products exist
                if (products.length === 0) {
                  setErrorMessage('No products available. Please add products using the "Inventory In" tab first.');
                  setShowError(true);
                  setTimeout(() => setShowError(false), 3000);
                  return;
                }

                // Validate that a product is selected
                if (!outFormData.product) {
                  setErrorMessage('Please select a product.');
                  setShowError(true);
                  setTimeout(() => setShowError(false), 3000);
                  return;
                }

                // Validate that a loss reason is selected
                if (!outFormData.reason) {
                  setErrorMessage('Please select a loss reason.');
                  setShowError(true);
                  setTimeout(() => setShowError(false), 3000);
                  return;
                }

                // FIXED: Robust product lookup with comprehensive ID matching
                const selectedProductId = outFormData.product;
                let product = null;

                // Try different matching strategies to handle various ID formats
                if (selectedProductId) {
                  // Strategy 1: Direct ID match (most common)
                  product = products.find(p => p.id == selectedProductId);

                  // Strategy 2: Parse as integer and match
                  if (!product) {
                    const parsedId = parseInt(selectedProductId);
                    if (!isNaN(parsedId)) {
                      product = products.find(p => p.id === parsedId);
                    }
                  }

                  // Strategy 3: String comparison
                  if (!product) {
                    product = products.find(p => String(p.id) === String(selectedProductId));
                  }
                }

                console.log('🔍 Company Loss - Product Lookup:', {
                  selectedId: selectedProductId,
                  found: !!product,
                  productName: product?.name
                });

                if (!product) {
                  console.error('❌ Product lookup failed:', {
                    searchValue: selectedProductId,
                    availableCount: products.length
                  });
                  setErrorMessage('Selected product not found. Please refresh the page and try again.');
                  setShowError(true);
                  setTimeout(() => setShowError(false), 3000);
                  return;
                }

                // Validate quantity
                const requestedQuantity = parseInt(outFormData.quantity);
                if (isNaN(requestedQuantity) || requestedQuantity <= 0) {
                  setErrorMessage('Please enter a valid quantity.');
                  setShowError(true);
                  setTimeout(() => setShowError(false), 3000);
                  return;
                }

                // Check stock availability
                if (product.stock < requestedQuantity) {
                  setErrorMessage(`Insufficient stock. Available: ${product.stock}, Requested: ${requestedQuantity}`);
                  setShowError(true);
                  setTimeout(() => setShowError(false), 3000);
                  return;
                }

                // Process the company loss
                try {
                  const lossNotes = `Company Loss - ${outFormData.reason}: ${outFormData.notes || 'No additional notes'}`;

                  updateProductStock(
                    product.id,
                    product.stock - requestedQuantity,
                    'loss',
                    {
                      reason: outFormData.reason,
                      notes: lossNotes,
                      lossType: 'company_loss',
                      recordedBy: user?.user_id || user?.email || 'System'
                    }
                  );

                  // Reset form
                  setOutFormData({
                    product: '',
                    quantity: '',
                    reason: '',
                    notes: '',
                    date: new Date().toISOString().split('T')[0]
                  });

                  // Show success message
                  setSuccessMessage(`Company loss recorded successfully! ${requestedQuantity} units of ${product.name} removed from inventory.`);
                  setShowSuccess(true);
                  setTimeout(() => {
                    setShowSuccess(false);
                    setSuccessMessage('');
                  }, 3000);
                } catch (error) {
                  setErrorMessage('Failed to record company loss. Please try again.');
                  setShowError(true);
                  setTimeout(() => setShowError(false), 3000);
                }
              }}>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label htmlFor="outProduct" className="block text-sm font-medium text-gray-700 mb-1">
                      Product *
                    </label>
                    <select
                      id="outProduct"
                      name="product"
                      value={outFormData.product}
                      onChange={(e) => setOutFormData(prev => ({...prev, product: e.target.value}))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                      required
                    >
                      <option value="">
                        {products.length === 0 ? 'No products available - Add products first' : 'Select product'}
                      </option>
                      {products.map(product => (
                        <option key={product.id} value={product.id}>
                          {product.name} (Stock: {product.stock})
                        </option>
                      ))}
                    </select>
                    {products.length === 0 && (
                      <p className="text-xs text-gray-500 mt-1">
                        Add products using the "Inventory In" tab first
                      </p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="outQuantity" className="block text-sm font-medium text-gray-700 mb-1">
                      Quantity Lost *
                    </label>
                    <input
                      type="number"
                      id="outQuantity"
                      name="quantity"
                      value={outFormData.quantity}
                      onChange={(e) => setOutFormData(prev => ({...prev, quantity: e.target.value}))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                      placeholder="Enter quantity"
                      min="1"
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="outReason" className="block text-sm font-medium text-gray-700 mb-1">
                      Loss Reason *
                    </label>
                    <select
                      id="outReason"
                      name="reason"
                      value={outFormData.reason}
                      onChange={(e) => setOutFormData(prev => ({...prev, reason: e.target.value}))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                      required
                    >
                      <option value="">Select reason</option>
                      <option value="Damaged">Damaged</option>
                      <option value="Expired">Expired</option>
                      <option value="Other">Other</option>
                    </select>
                  </div>

                  <div className="md:col-span-3">
                    <label htmlFor="outNotes" className="block text-sm font-medium text-gray-700 mb-1">
                      Additional Notes
                    </label>
                    <textarea
                      id="outNotes"
                      name="notes"
                      value={outFormData.notes}
                      onChange={(e) => setOutFormData(prev => ({...prev, notes: e.target.value}))}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                      placeholder="Enter additional details about the loss"
                    ></textarea>
                  </div>
                </div>

                <div className="flex justify-end mt-6">
                  <button
                    type="submit"
                    disabled={products.length === 0}
                    className={`flex items-center px-6 py-2 rounded-md transition-colors ${
                      products.length === 0
                        ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                        : 'bg-red-600 text-white hover:bg-red-700'
                    }`}
                  >
                    <AlertCircleIcon size={16} className="mr-2" />
                    {products.length === 0 ? 'No Products Available' : 'Record Loss'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Inventory Browser Tab */}
        {activeTab === 'browser' && (
          loading?.products ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <RefreshCwIcon size={48} className="text-blue-500 mx-auto mb-4 animate-spin" />
                <h3 className="text-lg font-medium text-blue-700 mb-2">Loading Products...</h3>
                <p className="text-blue-600">Please wait while we fetch your product data.</p>
              </div>
            </div>
          ) : (
            <InventoryBrowser
              products={products}
            />
          )
        )}

        {/* Inventory History Tab */}
        {activeTab === 'history' && (
          loading?.inventoryTransactions ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <RefreshCwIcon size={48} className="text-blue-500 mx-auto mb-4 animate-spin" />
                <h3 className="text-lg font-medium text-blue-700 mb-2">Loading Transaction History...</h3>
                <p className="text-blue-600">Please wait while we fetch your transaction data.</p>
              </div>
            </div>
          ) : (
            <InventoryHistory
              inventoryTransactions={inventoryTransactions || []}
              products={products}
              customers={customers}
              users={[]} // You would pass actual users data here
            />
          )
        )}
      </div>
    </div>
  );
}

export default InventoryManagement;
