import { useEffect } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import Sidebar from './Sidebar';
import Header from './Header';
const Layout = () => {
  const {
    isAuthenticated,
    user
  } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
    } else if (location.pathname === '/') {
      // Only redirect if we're at the root path
      if (user?.role === 'admin') {
        navigate('/admin/dashboard');
      } else {
        navigate('/salesman/dashboard');
      }
    }
  }, [isAuthenticated, navigate, user, location.pathname]);
  if (!isAuthenticated) {
    return null;
  }
  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar />
      <div className="flex flex-col flex-1 overflow-hidden">
        <Header />
        <main className="flex-1 overflow-y-auto p-4 md:p-6 bg-gray-50">
          <div className="container mx-auto max-w-7xl">
            <Outlet />
          </div>
        </main>
      </div>
    </div>
  );
};
export default Layout;
