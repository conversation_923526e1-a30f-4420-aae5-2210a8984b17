const express = require("express");
const router = express.Router();
// eslint-disable-next-line no-undef
const { authenticateToken, requireAdmin } = require("../middleware/auth");
// eslint-disable-next-line no-undef
const controller = require("../controllers/commissionController");
const { CommissionStage, CommissionAssignment } = require("../models");

// GET /api/commissions/stages - Get all commission stages (accessible to all authenticated users)
router.get("/stages", authenticateToken, (req, res, next) => controller.getCommissionStages(req, res, next));

// PUT /api/commissions/stages - Update commission stages (Admin only)
router.put("/stages", authenticateToken, requireAdmin, (req, res, next) => controller.updateCommissionStages(req, res, next));

// GET /api/commissions/assignments/:salesmanId - Get commission assignments for a salesman
router.get("/assignments/:salesmanId", authenticateToken, (req, res, next) => controller.getCommissionAssignments(req, res, next));

// POST /api/commissions/calculate/:orderId - Calculate commission for an order (Admin only)
router.post("/calculate/:orderId", authenticateToken, requireAdmin, (req, res, next) => controller.calculateCommission(req, res, next));

// POST /api/commissions/stages - Create a new commission stage (Admin only)
router.post("/stages", authenticateToken, requireAdmin, (req, res, next) => controller.createCommissionStage(req, res, next));

// DELETE /api/commissions/stages/:stageId - Delete a commission stage (Admin only)
router.delete('/stages/:stageId', authenticateToken, requireAdmin, async (req, res, next) => {
  try {
    const { stageId } = req.params;
    const stage = await CommissionStage.findByPk(stageId);
    if (!stage) return res.status(404).json({ success: false, message: 'Stage not found' });
    // Check if this stage is referenced in CommissionAssignment
    const assignmentCount = await CommissionAssignment.count({ where: { stage_id: stageId } });
    if (assignmentCount > 0) {
      return res.status(400).json({ success: false, message: 'Cannot delete: This stage is referenced in commission assignments.' });
    }
    await stage.destroy();
    res.json({ success: true, message: 'Commission stage deleted' });
  } catch (err) {
    next(err);
  }
});

// GET /api/commissions/summary - Get commission summary for all salesmen
router.get("/summary", authenticateToken, (req, res, next) => controller.getCommissionSummary(req, res, next));

// PUT /api/commissions/overrides - Create or update a commission override
router.put("/overrides", authenticateToken, (req, res, next) => controller.setCommissionOverride(req, res, next));

// GET /api/commissions/overrides - Get all commission overrides
router.get("/overrides", authenticateToken, (req, res, next) => controller.getCommissionOverrides(req, res, next));

module.exports = router;
