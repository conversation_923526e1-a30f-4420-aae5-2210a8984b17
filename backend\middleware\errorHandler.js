const errorHandler = (err, req, res, next) => {
  // Prevent server crashes by ensuring we always handle errors gracefully
  try {
    let error = { ...err };
    error.message = err.message;

    // Enhanced error logging with context
    console.error('❌ Error Handler - Request Details:', {
      url: req.url,
      method: req.method,
      body: req.body,
      params: req.params,
      query: req.query,
      timestamp: new Date().toISOString()
    });
    console.error('❌ Error Details:', err);

    // Sequelize validation error
    if (err.name === 'SequelizeValidationError') {
      const message = err.errors?.map(error => error.message).join(', ') || 'Validation failed';
      error = {
        statusCode: 400,
        message: `Validation Error: ${message}`
      };
    }

    // Sequelize unique constraint error (common in supplier creation)
    else if (err.name === 'SequelizeUniqueConstraintError') {
      const field = err.errors?.[0]?.path || 'field';
      const value = err.errors?.[0]?.value || 'value';
      error = {
        statusCode: 409,
        message: `Duplicate ${field}: '${value}' already exists`
      };
    }

    // Sequelize foreign key constraint error
    else if (err.name === 'SequelizeForeignKeyConstraintError') {
      error = {
        statusCode: 400,
        message: 'Invalid reference to related resource'
      };
    }

    // Sequelize database connection error
    else if (err.name === 'SequelizeConnectionError') {
      console.error('🚨 Database connection lost!');
      error = {
        statusCode: 503,
        message: 'Database connection error - please try again'
      };
    }

    // Sequelize database error (general)
    else if (err.name === 'SequelizeDatabaseError') {
      console.error('🚨 Database error:', err.original);
      error = {
        statusCode: 500,
        message: 'Database operation failed'
      };
    }

    // Sequelize timeout error
    else if (err.name === 'SequelizeTimeoutError') {
      error = {
        statusCode: 408,
        message: 'Database operation timed out'
      };
    }

    // JWT errors
    else if (err.name === 'JsonWebTokenError') {
      error = {
        statusCode: 401,
        message: 'Invalid authentication token'
      };
    }

    else if (err.name === 'TokenExpiredError') {
      error = {
        statusCode: 401,
        message: 'Authentication token expired'
      };
    }

    // Handle async errors and promise rejections
    else if (err.message && err.message.includes('Cannot set headers after they are sent')) {
      console.error('⚠️ Headers already sent error - preventing crash');
      return; // Don't try to send response
    }

    // Prevent server crash by ensuring response is sent only once
    if (!res.headersSent) {
      const statusCode = error.statusCode || 500;
      const responseData = {
        success: false,
        message: error.message || 'Internal server error',
        error_type: err.name || 'UnknownError',
        timestamp: new Date().toISOString()
      };

      // Add debug info in development
      if (process.env.NODE_ENV === 'development') {
        responseData.stack = err.stack;
        responseData.original_error = err.original?.message;
      }

      res.status(statusCode).json(responseData);

      // Log critical errors
      if (statusCode >= 500) {
        console.error('🚨 CRITICAL ERROR:', {
          message: error.message,
          type: err.name,
          url: req.url,
          method: req.method,
          timestamp: new Date().toISOString()
        });
      }
    } else {
      console.warn('⚠️ Response already sent, cannot send error response');
    }

  } catch (handlerError) {
    // Last resort error handling to prevent complete server crash
    console.error('🚨 CRITICAL: Error in error handler itself!', handlerError);

    if (!res.headersSent) {
      try {
        res.status(500).json({
          success: false,
          message: 'Critical server error',
          timestamp: new Date().toISOString()
        });
      } catch (finalError) {
        console.error('🚨 FATAL: Cannot send error response!', finalError);
      }
    }
  }
};

module.exports = errorHandler;
