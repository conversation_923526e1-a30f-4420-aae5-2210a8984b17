
import { Link } from 'react-router-dom';
import { BarChart2Icon, DollarSignIcon, PackageIcon, TrendingUpIcon, TrendingDownIcon, ShoppingCartIcon, UsersIcon, AlertCircleIcon, RefreshCwIcon } from 'lucide-react';
import { useData } from '../contexts/DataContext';
import { useApiData } from '../contexts/ApiDataContext';
import { useState, useEffect } from 'react';
import useAutoRefresh from '../hooks/useAutoRefresh';
import AutoRefreshControl from '../components/AutoRefreshControl';
const AdminDashboard = () => {
  const {
    getTotalRevenue,
    getTotalExpenses,
    getTotalProfit,
    getPendingPayments,
    getInventoryValue,
    getRecentOrders,
    getLowStockProducts,
    customers,
    refreshAllData // Add refresh function from DataContext
  } = useData();

  // ENHANCEMENT: Company Losses from ApiDataContext
  const { getCompanyLosses } = useApiData();

  // State for refresh functionality
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastRefresh, setLastRefresh] = useState(new Date());

  // State for dynamic statistics
  const [monthlyStats, setMonthlyStats] = useState({
    currentMonth: {
      revenue: 0,
      expenses: 0,
      profit: 0
    },
    previousMonth: {
      revenue: 0,
      expenses: 0,
      profit: 0
    },
    percentageChanges: {
      revenue: 0,
      expenses: 0,
      profit: 0
    }
  });

  const recentOrders = getRecentOrders(5);
  const lowStockProducts = getLowStockProducts();

  // Calculate monthly statistics
  const calculateMonthlyStats = () => {
    const now = new Date();
    const currentMonth = now.getMonth();
    const currentYear = now.getFullYear();
    const previousMonth = currentMonth === 0 ? 11 : currentMonth - 1;
    const previousYear = currentMonth === 0 ? currentYear - 1 : currentYear;

    // Get current month data
    const currentRevenue = getTotalRevenue();
    const currentExpenses = getTotalExpenses();
    const currentProfit = getTotalProfit();

    // For now, simulate previous month data (in a real app, this would come from the database)
    // You can implement actual historical data fetching later
    const previousRevenue = currentRevenue * 0.9; // Simulate 10% growth
    const previousExpenses = currentExpenses * 0.95; // Simulate 5% growth
    const previousProfit = currentProfit * 0.85; // Simulate 15% growth

    // Calculate percentage changes
    const revenueChange = previousRevenue > 0 ? ((currentRevenue - previousRevenue) / previousRevenue) * 100 : 0;
    const expensesChange = previousExpenses > 0 ? ((currentExpenses - previousExpenses) / previousExpenses) * 100 : 0;
    const profitChange = previousProfit > 0 ? ((currentProfit - previousProfit) / previousProfit) * 100 : 0;

    return {
      currentMonth: {
        revenue: currentRevenue,
        expenses: currentExpenses,
        profit: currentProfit
      },
      previousMonth: {
        revenue: previousRevenue,
        expenses: previousExpenses,
        profit: previousProfit
      },
      percentageChanges: {
        revenue: revenueChange,
        expenses: expensesChange,
        profit: profitChange
      }
    };
  };

  // Refresh dashboard data
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      // Refresh data from DataContext
      if (refreshAllData) {
        await refreshAllData();
      }

      // Recalculate monthly statistics
      const newStats = calculateMonthlyStats();
      setMonthlyStats(newStats);

      // Update last refresh time
      setLastRefresh(new Date());
    } catch (error) {
      console.error('Error refreshing dashboard:', error);
      throw error; // Re-throw for auto-refresh error handling
    } finally {
      setIsRefreshing(false);
    }
  };

  // ENHANCEMENT: Auto-refresh functionality
  const {
    isAutoRefreshEnabled,
    isPaused,
    lastRefresh: autoLastRefresh,
    toggleAutoRefresh,
    manualRefresh
  } = useAutoRefresh(handleRefresh, {
    interval: 3000, // 3 seconds
    enabled: true,
    pauseOnInteraction: true,
    interactionTimeout: 5000 // 5 seconds
  });

  // Calculate stats on component mount and when data changes
  useEffect(() => {
    const newStats = calculateMonthlyStats();
    setMonthlyStats(newStats);
  }, [getTotalRevenue, getTotalExpenses, getTotalProfit]);

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  // Format percentage change
  const formatPercentageChange = (change) => {
    const isPositive = change >= 0;
    const color = isPositive ? 'text-green-600' : 'text-red-600';
    const sign = isPositive ? '+' : '';
    return (
      <span className={color}>
        {sign}{change.toFixed(1)}%
      </span>
    );
  };
  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Dashboard</h1>
          <p className="text-gray-600">Welcome to your admin dashboard</p>
          <p className="text-xs text-gray-500 mt-1">
            Last updated: {lastRefresh.toLocaleTimeString()}
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <AutoRefreshControl
            isAutoRefreshEnabled={isAutoRefreshEnabled}
            isPaused={isPaused}
            lastRefresh={autoLastRefresh}
            toggleAutoRefresh={toggleAutoRefresh}
            interval={3000}
            size="sm"
          />
          <button
            onClick={manualRefresh}
            disabled={isRefreshing}
            className="flex items-center bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 disabled:bg-gray-400 transition-colors"
          >
            <RefreshCwIcon size={16} className={`mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            {isRefreshing ? 'Refreshing...' : 'Refresh Now'}
          </button>
          <Link to="/reports" className="flex items-center bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
            <BarChart2Icon size={16} className="mr-2" />
            View Reports
          </Link>
        </div>
      </div>
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6 mb-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-sm font-medium text-gray-500">REVENUE</h3>
            <DollarSignIcon size={20} className="text-green-600" />
          </div>
          <p className="text-2xl font-bold text-gray-900">
            {formatCurrency(getTotalRevenue())}
          </p>
          <div className="mt-2 flex items-center text-sm">
            {formatPercentageChange(monthlyStats.percentageChanges.revenue)}
            <span className="text-gray-600 ml-1">from last month</span>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-sm font-medium text-gray-500">EXPENSES</h3>
            <DollarSignIcon size={20} className="text-red-600" />
          </div>
          <p className="text-2xl font-bold text-gray-900">
            {formatCurrency(getTotalExpenses())}
          </p>
          <div className="mt-2 flex items-center text-sm">
            {formatPercentageChange(monthlyStats.percentageChanges.expenses)}
            <span className="text-gray-600 ml-1">from last month</span>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-sm font-medium text-gray-500">PROFIT</h3>
            <TrendingUpIcon size={20} className="text-blue-600" />
          </div>
          <p className="text-2xl font-bold text-gray-900">
            {formatCurrency(getTotalProfit())}
          </p>
          <div className="mt-2 flex items-center text-sm">
            {formatPercentageChange(monthlyStats.percentageChanges.profit)}
            <span className="text-gray-600 ml-1">from last month</span>
          </div>
        </div>
        {/* ENHANCEMENT: Company Losses Card */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-sm font-medium text-gray-500">COMPANY LOSSES</h3>
            <TrendingDownIcon size={20} className="text-red-600" />
          </div>
          <p className="text-2xl font-bold text-gray-900">
            {formatCurrency(getCompanyLosses())}
          </p>
          <div className="mt-2 flex items-center text-sm">
            <Link to="/inventory/out" className="text-red-600 hover:underline">
              Record loss
            </Link>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-sm font-medium text-gray-500">
              PENDING PAYMENTS
            </h3>
            <DollarSignIcon size={20} className="text-yellow-600" />
          </div>
          <p className="text-2xl font-bold text-gray-900">
            {formatCurrency(getPendingPayments())}
          </p>
          <div className="mt-2 flex items-center text-sm">
            <Link to="/sales/credits" className="text-blue-600 hover:underline">
              View payments
            </Link>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-sm font-medium text-gray-500">
              INVENTORY VALUE
            </h3>
            <PackageIcon size={20} className="text-indigo-600" />
          </div>
          <p className="text-2xl font-bold text-gray-900">
            {formatCurrency(getInventoryValue())}
          </p>
          <div className="mt-2 flex items-center text-sm">
            <Link to="/inventory/in" className="text-blue-600 hover:underline">
              Manage inventory
            </Link>
          </div>
        </div>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* Recent Orders */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 className="text-lg font-medium text-gray-800">Recent Orders</h2>
            <Link to="/sales/orders" className="text-sm text-blue-600 hover:text-blue-800">
              View All
            </Link>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Order #
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Customer
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {recentOrders.length > 0 ? (
                  recentOrders.map(order => {
                    const customer = customers.find(c => c.id === order.customerId);
                    return (
                      <tr key={order.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">
                          {order.orderNumber}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {customer ? customer.name : 'Unknown Customer'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {order.date}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatCurrency(order.totalAmount)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {(() => {
                            // FIXED: Apply cancelled order payment status logic
                            const isCancelledOrder = order.status === 'cancelled';
                            const actualPaymentStatus = isCancelledOrder ? 'cancelled' : order.paymentStatus;

                            const getStatusBadgeClass = (status) => {
                              switch (status) {
                                case 'paid': return 'bg-green-100 text-green-800';
                                case 'partial': return 'bg-blue-100 text-blue-800';
                                case 'pending': return 'bg-yellow-100 text-yellow-800';
                                case 'cancelled': return 'bg-red-100 text-red-800';
                                default: return 'bg-gray-100 text-gray-800';
                              }
                            };

                            const getStatusText = (status) => {
                              switch (status) {
                                case 'paid': return 'Paid';
                                case 'partial': return 'Partially Paid';
                                case 'pending': return 'Pending';
                                case 'cancelled': return 'Cancelled';
                                default: return 'Unknown';
                              }
                            };

                            return (
                              <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(actualPaymentStatus)}`}>
                                {getStatusText(actualPaymentStatus)}
                              </span>
                            );
                          })()}
                        </td>
                      </tr>
                    );
                  })
                ) : (
                  <tr>
                    <td colSpan="5" className="px-6 py-8 text-center text-gray-500">
                      No orders yet. Create your first order to see it here.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
        {/* Low Stock Alerts */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 className="text-lg font-medium text-gray-800">
              Low Stock Alerts
            </h2>
            <Link to="/inventory/in" className="text-sm text-blue-600 hover:text-blue-800">
              Order Inventory
            </Link>
          </div>
          {lowStockProducts.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Product
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Category
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Current Stock
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Supplier
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Last Restocked
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {lowStockProducts.map(product => (
                    <tr key={product.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {product.name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {product.category}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800`}>
                          {product.stock} units
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {product.supplier}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {product.lastRestocked}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="p-6 text-center">
              <PackageIcon size={40} className="mx-auto text-gray-400 mb-4" />
              <p className="text-gray-600">
                All products have sufficient stock.
              </p>
            </div>
          )}
        </div>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Customer Overview */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 className="text-lg font-medium text-gray-800">
              Customer Overview
            </h2>
            <Link to="/customers" className="text-sm text-blue-600 hover:text-blue-800">
              View All
            </Link>
          </div>
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <p className="text-sm text-gray-500">Total Customers</p>
                <p className="text-2xl font-bold">{customers.length}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Active This Month</p>
                <p className="text-2xl font-bold">
                  {customers.filter(c => {
                    const lastOrderDate = new Date(c.lastOrderDate);
                    const oneMonthAgo = new Date();
                    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
                    return lastOrderDate > oneMonthAgo;
                  }).length}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Average Order Value</p>
                <p className="text-2xl font-bold">
                  {(() => {
                    const totalSpent = customers.reduce((sum, c) => sum + c.totalSpent, 0);
                    const totalOrders = customers.reduce((sum, c) => sum + c.totalOrders, 0);
                    return formatCurrency(totalOrders > 0 ? totalSpent / totalOrders : 0);
                  })()}
                </p>
              </div>
            </div>
            <h3 className="text-sm font-medium text-gray-700 mb-2">
              Top Customers
            </h3>
            <div className="space-y-3">
              {customers.length > 0 ? (
                [...customers].sort((a, b) => b.totalSpent - a.totalSpent).slice(0, 3).map(customer => (
                  <div key={customer.id} className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="bg-blue-100 p-2 rounded-full">
                        <UsersIcon size={16} className="text-blue-600" />
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-gray-900">
                          {customer.name}
                        </p>
                        <p className="text-xs text-gray-500">
                          {customer.contact}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-900">
                        {formatCurrency(customer.totalSpent)}
                      </p>
                      <p className="text-xs text-gray-500">
                        {customer.totalOrders} orders
                      </p>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-4">
                  <UsersIcon size={32} className="mx-auto text-gray-400 mb-2" />
                  <p className="text-sm text-gray-500">No customers yet. Add customers to see them here.</p>
                </div>
              )}
            </div>
          </div>
        </div>
        {/* Quick Actions */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-800">Quick Actions</h2>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-2 gap-4">
              <Link to="/inventory/in" className="flex flex-col items-center justify-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <PackageIcon size={24} className="text-blue-600 mb-2" />
                <span className="text-sm font-medium text-gray-900">
                  Add Inventory
                </span>
              </Link>
              <Link to="/sales/orders" className="flex flex-col items-center justify-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <ShoppingCartIcon size={24} className="text-green-600 mb-2" />
                <span className="text-sm font-medium text-gray-900">
                  New Order
                </span>
              </Link>
              <Link to="/sales/credits" className="flex flex-col items-center justify-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <DollarSignIcon size={24} className="text-yellow-600 mb-2" />
                <span className="text-sm font-medium text-gray-900">
                  Record Payment
                </span>
              </Link>
              <Link to="/finances/expenses" className="flex flex-col items-center justify-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <DollarSignIcon size={24} className="text-red-600 mb-2" />
                <span className="text-sm font-medium text-gray-900">
                  Add Expense
                </span>
              </Link>
            </div>
            <div className="mt-6">
              <h3 className="text-sm font-medium text-gray-700 mb-2">
                System Notifications
              </h3>
              <div className="space-y-3">
                <div className="flex items-start p-3 bg-yellow-50 rounded-lg">
                  <AlertCircleIcon size={16} className="text-yellow-600 mr-2 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-yellow-800">
                      Pending payments require attention
                    </p>
                    <p className="text-xs text-yellow-700">
                      There are{' '}
                      {recentOrders.filter(o => o.paymentStatus !== 'paid' && o.status !== 'cancelled').length}{' '}
                      orders with pending payments.
                    </p>
                  </div>
                </div>
                <div className="flex items-start p-3 bg-blue-50 rounded-lg">
                  <AlertCircleIcon size={16} className="text-blue-600 mr-2 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-blue-800">
                      Monthly reports available
                    </p>
                    <p className="text-xs text-blue-700">
                      June 2023 reports are ready for review.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default AdminDashboard;
