import React, { useState, useEffect, useCallback } from 'react';
import {
  CalendarIcon,
  DownloadIcon,
  RefreshCwIcon,
  TrendingUpIcon,
  DollarSignIcon,
  UsersIcon,
  BarChart3Icon,
  SettingsIcon
} from 'lucide-react';
import MonthlySummary from './MonthlySummary';
import CommissionHistory from './CommissionHistory';
import NotificationBanner from './NotificationBanner';
import MonthlyResetPanel from './MonthlyResetPanel';
import ManageStagesModal from './ManageStagesModal';

const CommissionDashboard = ({ user }) => {
  const [activeTab, setActiveTab] = useState('monthly');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [refreshKey, setRefreshKey] = useState(0);
  const [showManageStages, setShowManageStages] = useState(false);

  // Manual refresh function
  const handleRefresh = useCallback(() => {
    setRefreshKey(prev => prev + 1);
    setError(null);
  }, []);

  const handleStagesUpdated = () => {
    setRefreshKey(prev => prev + 1);
  };

  const tabs = [
    {
      id: 'monthly',
      label: 'Current Month',
      icon: CalendarIcon,
      description: 'Monthly commission summary and performance'
    },
    {
      id: 'history',
      label: 'Commission History',
      icon: BarChart3Icon,
      description: 'Historical commission data and reports'
    }
  ];

  return (
    <div className="bg-white rounded-lg shadow-lg overflow-hidden">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-green-50 to-blue-50">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-xl font-semibold text-gray-800 flex items-center">
              <DollarSignIcon size={24} className="mr-2 text-green-600" />
              Commission Dashboard
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              Track commission performance and analyze historical data
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            {user?.role === 'admin' && (
              <button
                onClick={() => setShowManageStages(true)}
                className="flex items-center px-3 py-2 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
              >
                <SettingsIcon size={16} className="mr-2" />
                Manage Stages
              </button>
            )}
            <button
              onClick={handleRefresh}
              disabled={loading}
              className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
            >
              <RefreshCwIcon
                size={16}
                className={`mr-2 ${loading ? 'animate-spin' : ''}`}
              />
              Refresh
            </button>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="mt-4">
          <nav className="flex space-x-8">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon size={16} className="mr-2" />
                  {tab.label}
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="px-6 py-4 bg-red-50 border-l-4 border-red-400">
          <div className="flex">
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Tab Content */}
      <div className="p-6">
        {/* Notification Banner */}
        <NotificationBanner user={user} />

        {/* Monthly Reset Panel */}
        <MonthlyResetPanel user={user} />

        {activeTab === 'monthly' && (
          <MonthlySummary
            user={user}
            refreshKey={refreshKey}
            onError={setError}
            onLoadingChange={setLoading}
          />
        )}

        {activeTab === 'history' && (
          <CommissionHistory
            user={user}
            refreshKey={refreshKey}
            onError={setError}
            onLoadingChange={setLoading}
          />
        )}
      </div>

      {/* Manage Stages Modal */}
      <ManageStagesModal
        isOpen={showManageStages}
        onClose={() => setShowManageStages(false)}
        onStagesUpdated={handleStagesUpdated}
        user={user}
      />
    </div>
  );
};

export default CommissionDashboard;
