import { useState } from 'react';
import { PackageIcon, PlusIcon, AlertTriangleIcon, CheckIcon, TrendingDownIcon } from 'lucide-react';
import { useApiData } from '../contexts/ApiDataContext';

function InventoryOut() {
  // ENHANCEMENT: Company Loss Tracking System
  // Transformed from customer-based inventory out to company loss tracking
  const { products, updateProductStock, inventoryTransactions } = useApiData();





  // ENHANCEMENT: Company Loss Tracking Form Data
  const [formData, setFormData] = useState({
    product: '',
    quantity: '',
    reason: '', // New field: Damaged, Expired, Other
    date: new Date().toISOString().split('T')[0],
    notes: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // ENHANCEMENT: Company Loss Tracking Logic
      // Find the selected product
      const selectedProduct = products.find(p => p.id === parseInt(formData.product));

      if (!selectedProduct) {
        alert('Please select a valid product');
        setIsSubmitting(false);
        return;
      }

      if (!formData.reason) {
        alert('Please select a loss reason');
        setIsSubmitting(false);
        return;
      }

      const quantity = parseInt(formData.quantity);
      if (selectedProduct.stock < quantity) {
        alert(`Insufficient stock. Available: ${selectedProduct.stock}, Requested: ${quantity}`);
        setIsSubmitting(false);
        return;
      }

      // Update product stock and record as company loss
      const newStock = selectedProduct.stock - quantity;
      const lossNotes = `Company Loss - ${formData.reason}: ${formData.notes || 'No additional notes'}`;

      updateProductStock(selectedProduct.id, newStock, 'loss', {
        reason: formData.reason,
        notes: lossNotes,
        lossType: 'company_loss'
      });

      setTimeout(() => {
        setIsSubmitting(false);
        setShowSuccess(true);
        setTimeout(() => {
          setShowSuccess(false);
          setFormData({
            product: '',
            quantity: '',
            reason: '',
            date: new Date().toISOString().split('T')[0],
            notes: ''
          });
        }, 2000);
      }, 1000);
    } catch (error) {
      console.error('Error processing company loss:', error);
      setIsSubmitting(false);
    }
  };

  // ENHANCEMENT: Company Loss Tracking Options
  // Generate product options from real data (only products with stock)
  const productOptions = [
    { value: '', label: 'Select product' },
    ...products
      .filter(product => product.stock > 0)
      .map(product => ({
        value: product.id.toString(),
        label: `${product.name} (Stock: ${product.stock})`
      }))
  ];

  // Loss reason options as specified in requirements
  const lossReasonOptions = [
    { value: '', label: 'Select reason' },
    { value: 'Damaged', label: 'Damaged' },
    { value: 'Expired', label: 'Expired' },
    { value: 'Other', label: 'Other' }
  ];

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Company Loss Tracking</h1>
          <p className="text-gray-600">Record inventory losses due to damage, expiration, or other reasons</p>
        </div>
        <div>
          <button className="flex items-center bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700">
            <TrendingDownIcon size={16} className="mr-2" />
            View Loss History
          </button>
        </div>
      </div>
      {showSuccess && (
        <div className="bg-green-50 border-l-4 border-green-500 p-4 mb-6 flex items-start">
          <CheckIcon size={20} className="text-green-500 mr-2 mt-0.5" />
          <div>
            <p className="text-green-700 font-medium">Company loss recorded successfully!</p>
            <p className="text-green-600">The inventory loss has been saved and stock has been updated.</p>
          </div>
        </div>
      )}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="flex items-center mb-4">
          <AlertTriangleIcon size={20} className="text-red-600 mr-2" />
          <h2 className="text-lg font-medium text-gray-800">Record Company Loss</h2>
        </div>
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <div>
              <label htmlFor="product" className="block text-sm font-medium text-gray-700 mb-1">Product</label>
              <select id="product" name="product" value={formData.product} onChange={handleChange} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500" required>
                {productOptions.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>
            </div>
            <div>
              <label htmlFor="quantity" className="block text-sm font-medium text-gray-700 mb-1">Quantity Lost</label>
              <input type="number" id="quantity" name="quantity" value={formData.quantity} onChange={handleChange} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500" placeholder="Enter quantity" min="1" required />
            </div>
            <div>
              <label htmlFor="reason" className="block text-sm font-medium text-gray-700 mb-1">Loss Reason</label>
              <select id="reason" name="reason" value={formData.reason} onChange={handleChange} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500" required>
                {lossReasonOptions.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>
            </div>
            <div>
              <label htmlFor="date" className="block text-sm font-medium text-gray-700 mb-1">Date</label>
              <input type="date" id="date" name="date" value={formData.date} onChange={handleChange} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500" required />
            </div>
          </div>
          <div className="mb-6">
            <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">Notes</label>
            <textarea id="notes" name="notes" value={formData.notes} onChange={handleChange} rows={3} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Enter any additional notes"></textarea>
          </div>
          <div className="flex justify-end">
            <button type="button" className="px-4 py-2 mr-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50" onClick={() => setFormData({
              product: '',
              quantity: '',
              reason: '',
              date: new Date().toISOString().split('T')[0],
              notes: ''
            })}>
              Clear Form
            </button>
            <button type="submit" className="flex items-center bg-red-600 text-white px-6 py-2 rounded-md hover:bg-red-700 disabled:bg-red-400" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Recording Loss...
                </>
              ) : (
                <>
                  <AlertTriangleIcon size={16} className="mr-2" />
                  Record Loss
                </>
              )}
            </button>
          </div>
        </form>
      </div>
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center mb-4">
          <TrendingDownIcon size={20} className="text-red-600 mr-2" />
          <h2 className="text-lg font-medium text-gray-800">Recent Company Losses</h2>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity Lost</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reason</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Loss Value</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Recorded By</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {inventoryTransactions
                .filter(t => t.type === 'loss' || (t.type === 'out' && t.notes?.includes('Company Loss')))
                .sort((a, b) => new Date(b.date) - new Date(a.date))
                .slice(0, 5)
                .map(transaction => (
                  <tr key={transaction.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{transaction.date}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{transaction.productName}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{transaction.quantity}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        transaction.notes?.includes('Damaged') ? 'bg-red-100 text-red-800' :
                        transaction.notes?.includes('Expired') ? 'bg-yellow-100 text-yellow-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {transaction.notes?.includes('Damaged') ? 'Damaged' :
                         transaction.notes?.includes('Expired') ? 'Expired' : 'Other'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${(transaction.totalCost || 0).toFixed(2)}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{transaction.recordedBy || 'System'}</td>
                  </tr>
                ))}
              {inventoryTransactions.filter(t => t.type === 'loss' || (t.type === 'out' && t.notes?.includes('Company Loss'))).length === 0 && (
                <tr>
                  <td colSpan="6" className="px-6 py-8 text-center text-gray-500">
                    No company losses recorded yet. Record your first loss above.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

export default InventoryOut;
