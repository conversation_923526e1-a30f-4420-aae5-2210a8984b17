// Test commission authentication and endpoints
import fetch from 'node-fetch';

const API_BASE_URL = 'http://localhost:3001/api';

async function testCommissionAuth() {
  try {
    console.log('🔐 Step 1: Testing authentication...');
    
    // Step 1: Authenticate as admin
    const authResponse = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Admin123!'
      })
    });

    console.log('Auth response status:', authResponse.status);
    const authResult = await authResponse.json();
    console.log('Auth result:', authResult);
    
    if (!authResult.success) {
      console.error('❌ Authentication failed:', authResult.message);
      return;
    }

    const token = authResult.data.token;
    console.log('✅ Authentication successful');
    console.log('🔑 Token (first 50 chars):', token.substring(0, 50) + '...');

    // Step 2: Test commission stages endpoint
    console.log('\n📊 Step 2: Testing commission stages endpoint...');
    
    const stagesResponse = await fetch(`${API_BASE_URL}/commissions/stages`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    console.log('Stages response status:', stagesResponse.status);
    console.log('Stages response headers:', Object.fromEntries(stagesResponse.headers.entries()));
    
    const stagesResult = await stagesResponse.json();
    console.log('Stages result:', stagesResult);

    // Step 3: Test commission summary endpoint
    console.log('\n💰 Step 3: Testing commission summary endpoint...');
    
    const summaryResponse = await fetch(`${API_BASE_URL}/commissions/summary`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    console.log('Summary response status:', summaryResponse.status);
    const summaryResult = await summaryResponse.json();
    console.log('Summary result:', summaryResult);

    // Step 4: Test user info to verify token
    console.log('\n👤 Step 4: Testing user info with token...');
    
    const userResponse = await fetch(`${API_BASE_URL}/auth/me`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    console.log('User response status:', userResponse.status);
    if (userResponse.ok) {
      const userResult = await userResponse.json();
      console.log('User info:', userResult);
    } else {
      console.log('User response error:', await userResponse.text());
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test
testCommissionAuth();
