// Robust migration: add missing columns to commission_stage_overrides only if they do not exist
const tableName = 'commission_stage_overrides';

async function columnExists(queryInterface, column) {
  const table = await queryInterface.describeTable(tableName);
  return !!table[column];
}

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // effective_date
    if (!(await columnExists(queryInterface, 'effective_date'))) {
      await queryInterface.addColumn(tableName, 'effective_date', {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      });
    }
    // end_date
    if (!(await columnExists(queryInterface, 'end_date'))) {
      await queryInterface.addColumn(tableName, 'end_date', {
        type: Sequelize.DATE,
        allowNull: true
      });
    }
    // notes
    if (!(await columnExists(queryInterface, 'notes'))) {
      await queryInterface.addColumn(tableName, 'notes', {
        type: Sequelize.TEXT,
        allowNull: true
      });
    }
    // created_by
    if (!(await columnExists(queryInterface, 'created_by'))) {
      await queryInterface.addColumn(tableName, 'created_by', {
        type: Sequelize.UUID,
        allowNull: false
      });
    }
    // updated_by
    if (!(await columnExists(queryInterface, 'updated_by'))) {
      await queryInterface.addColumn(tableName, 'updated_by', {
        type: Sequelize.UUID,
        allowNull: true
      });
    }
  },

  down: async (queryInterface) => {
    // Remove columns if they exist
    const columns = ['effective_date', 'end_date', 'notes', 'created_by', 'updated_by'];
    for (const column of columns) {
      const table = await queryInterface.describeTable(tableName);
      if (table[column]) {
        await queryInterface.removeColumn(tableName, column);
      }
    }
  }
};
