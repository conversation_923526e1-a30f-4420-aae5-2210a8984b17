const { User, Salesman } = require('../models');
const { generateToken } = require('../middleware/auth');
const emailService = require('../services/emailService');
const crypto = require('crypto');

// Register new user
const register = async (req, res, next) => {
  try {
    const { email, password, role, full_name, contact_info } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({ where: { email } });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'User with this email already exists'
      });
    }

    // Create user (email verification required)
    const user = await User.create({
      email,
      password_hash: password, // Will be hashed by the model hook
      role,
      email_verified: false // Require email verification
    });

    // Generate email verification token
    const verificationToken = user.generateEmailVerificationToken();
    await user.save();

    // If role is salesman, create salesman profile
    if (role === 'salesman') {
      await Salesman.create({
        user_id: user.user_id,
        full_name: full_name || email.split('@')[0],
        contact_info: contact_info || {}
      });
    }

    // Send verification email
    const emailResult = await emailService.sendEmailVerification(user, verificationToken);

    if (!emailResult.success) {
      console.error('Failed to send verification email:', emailResult.error);
      // Don't fail registration if email fails, just log it
    }

    res.status(201).json({
      success: true,
      message: 'User registered successfully. Please check your email to verify your account.',
      data: {
        user: {
          user_id: user.user_id,
          email: user.email,
          role: user.role,
          email_verified: user.email_verified
        },
        requires_verification: true
      }
    });
  } catch (error) {
    next(error);
  }
};

// Login user
const login = async (req, res, next) => {
  try {
    const { email, password } = req.body;

    // Find user with salesman profile if exists
    const user = await User.findOne({
      where: { email },
      include: [
        {
          model: Salesman,
          as: 'salesmanProfile',
          required: false
        }
      ]
    });

    if (!user || !user.is_active) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Check if account is locked
    if (user.isAccountLocked()) {
      return res.status(423).json({
        success: false,
        message: 'Account is temporarily locked due to multiple failed login attempts. Please try again later.'
      });
    }

    // Check if email is verified
    if (!user.email_verified) {
      return res.status(403).json({
        success: false,
        message: 'Please verify your email address before logging in.',
        requires_verification: true
      });
    }

    // Validate password
    const isValidPassword = await user.validatePassword(password);
    if (!isValidPassword) {
      // Increment failed login attempts
      await user.incrementFailedLogins();

      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Reset failed login attempts on successful login
    if (user.failed_login_attempts > 0) {
      await user.resetFailedLogins();
    }

    // Update last login
    await user.update({ last_login: new Date() });

    // Generate token
    const token = generateToken(user.user_id, user.role);

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: {
          user_id: user.user_id,
          email: user.email,
          role: user.role,
          salesmanProfile: user.salesmanProfile
        },
        token
      }
    });
  } catch (error) {
    next(error);
  }
};

// Get current user profile
const getProfile = async (req, res, next) => {
  try {
    const user = await User.findByPk(req.user.user_id, {
      include: [
        {
          model: Salesman,
          as: 'salesmanProfile',
          required: false
        }
      ]
    });

    res.json({
      success: true,
      data: {
        user: {
          user_id: user.user_id,
          email: user.email,
          role: user.role,
          salesmanProfile: user.salesmanProfile,
          last_login: user.last_login,
          created_at: user.created_at
        }
      }
    });
  } catch (error) {
    next(error);
  }
};

// Update user profile
const updateProfile = async (req, res, next) => {
  try {
    const { email, full_name, contact_info } = req.body;
    const user = req.user;

    // Update user email if provided
    if (email && email !== user.email) {
      const existingUser = await User.findOne({ where: { email } });
      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: 'Email already in use'
        });
      }
      await user.update({ email });
    }

    // Update salesman profile if user is salesman
    if (user.role === 'salesman' && (full_name || contact_info)) {
      const salesman = await Salesman.findOne({ where: { user_id: user.user_id } });
      if (salesman) {
        await salesman.update({
          ...(full_name && { full_name }),
          ...(contact_info && { contact_info })
        });
      }
    }

    // Get updated user data
    const updatedUser = await User.findByPk(user.user_id, {
      include: [
        {
          model: Salesman,
          as: 'salesmanProfile',
          required: false
        }
      ]
    });

    res.json({
      success: true,
      message: 'Profile updated successfully',
      data: { user: updatedUser }
    });
  } catch (error) {
    next(error);
  }
};

// Verify email address
const verifyEmail = async (req, res, next) => {
  try {
    const { token } = req.body;

    // Find user with matching verification token
    const user = await User.findOne({
      where: {
        email_verification_token: token,
        email_verification_expires: {
          [require('sequelize').Op.gt]: new Date()
        }
      }
    });

    if (!user) {
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired verification token'
      });
    }

    // Verify the email
    user.email_verified = true;
    user.email_verification_token = null;
    user.email_verification_expires = null;
    await user.save();

    // Send welcome email
    await emailService.sendWelcomeEmail(user);

    res.json({
      success: true,
      message: 'Email verified successfully! You can now log in.'
    });
  } catch (error) {
    next(error);
  }
};

// Forgot password - send reset email
const forgotPassword = async (req, res, next) => {
  try {
    const { email } = req.body;

    const user = await User.findOne({ where: { email } });
    if (!user) {
      // Don't reveal if email exists or not for security
      return res.json({
        success: true,
        message: 'If an account with that email exists, a password reset link has been sent.'
      });
    }

    // Generate password reset token
    const resetToken = user.generatePasswordResetToken();
    await user.save();

    // Send password reset email
    const emailResult = await emailService.sendPasswordReset(user, resetToken);

    if (!emailResult.success) {
      console.error('Failed to send password reset email:', emailResult.error);
      return res.status(500).json({
        success: false,
        message: 'Failed to send password reset email. Please try again.'
      });
    }

    res.json({
      success: true,
      message: 'If an account with that email exists, a password reset link has been sent.'
    });
  } catch (error) {
    next(error);
  }
};

// Reset password with token
const resetPassword = async (req, res, next) => {
  try {
    const { token, password } = req.body;

    // Find user with matching reset token
    const user = await User.findOne({
      where: {
        password_reset_token: token,
        password_reset_expires: {
          [require('sequelize').Op.gt]: new Date()
        }
      }
    });

    if (!user) {
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired reset token'
      });
    }

    // Update password and clear reset token
    user.password_hash = password; // Will be hashed by model hook
    user.password_reset_token = null;
    user.password_reset_expires = null;
    user.failed_login_attempts = 0; // Reset failed attempts
    user.account_locked_until = null; // Unlock account
    await user.save();

    res.json({
      success: true,
      message: 'Password reset successfully! You can now log in with your new password.'
    });
  } catch (error) {
    next(error);
  }
};

// Resend verification email
const resendVerification = async (req, res, next) => {
  try {
    const { email } = req.body;

    const user = await User.findOne({ where: { email } });
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    if (user.email_verified) {
      return res.status(400).json({
        success: false,
        message: 'Email is already verified'
      });
    }

    // Generate new verification token
    const verificationToken = user.generateEmailVerificationToken();
    await user.save();

    // Send verification email
    const emailResult = await emailService.sendEmailVerification(user, verificationToken);

    if (!emailResult.success) {
      console.error('Failed to send verification email:', emailResult.error);
      return res.status(500).json({
        success: false,
        message: 'Failed to send verification email. Please try again.'
      });
    }

    res.json({
      success: true,
      message: 'Verification email sent successfully!'
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  register,
  login,
  getProfile,
  updateProfile,
  verifyEmail,
  forgotPassword,
  resetPassword,
  resendVerification
};
