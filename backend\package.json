{"name": "inventory-management-backend", "version": "1.0.0", "description": "Backend API for Inventory Management System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "production": "NODE_ENV=production node server.js", "migrate": "node scripts/migrate.js", "seed": "node scripts/seed.js", "test": "jest"}, "keywords": ["inventory", "management", "api", "nodejs", "express"], "author": "Your Name", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "chalk": "^4.1.2", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "morgan": "^1.10.0", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "sequelize": "^6.35.0", "sqlite3": "^5.1.7", "uuid": "^9.0.1"}, "devDependencies": {"@types/jest": "^29.5.8", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}