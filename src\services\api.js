// API Configuration and Base Service
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api';

class ApiService {
  constructor() {
    this.baseURL = API_BASE_URL;
    this.token = localStorage.getItem('auth_token');
    console.log('🔗 API Service initialized with base URL:', this.baseURL);
  }

  // Set authentication token
  setToken(token) {
    this.token = token;
    if (token) {
      localStorage.setItem('auth_token', token);
    } else {
      localStorage.removeItem('auth_token');
    }
  }

  // Get authentication headers
  getHeaders() {
    const headers = {
      'Content-Type': 'application/json',
    };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    return headers;
  }

  // Generic request method
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: this.getHeaders(),
      ...options,
    };

    console.log('🚀 API Request:', options.method || 'GET', url);

    try {
      const response = await fetch(url, config);

      if (!response.ok) {
        console.error('❌ API Error:', response.status, response.statusText);
        const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ API Success:', response.status, url);
      return data;
    } catch (error) {
      console.error('❌ API Request failed:', error.message);
      throw error;
    }
  }

  // GET request
  async get(endpoint, params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const url = queryString ? `${endpoint}?${queryString}` : endpoint;

    return this.request(url, {
      method: 'GET',
    });
  }

  // POST request
  async post(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // PUT request
  async put(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  // DELETE request
  async delete(endpoint) {
    return this.request(endpoint, {
      method: 'DELETE',
    });
  }
}

// Create singleton instance
const apiService = new ApiService();

// Authentication API
export const authAPI = {
  login: async (credentials) => {
    const response = await apiService.post('/auth/login', credentials);
    if (response.success && response.data.token) {
      apiService.setToken(response.data.token);
    }
    return response;
  },

  register: async (userData) => {
    const response = await apiService.post('/auth/register', userData);
    // Don't auto-login on registration since email verification is required
    return response;
  },

  verifyEmail: async (token) => {
    return apiService.post('/auth/verify-email', { token });
  },

  forgotPassword: async (email) => {
    return apiService.post('/auth/forgot-password', { email });
  },

  resetPassword: async (token, password, confirmPassword) => {
    return apiService.post('/auth/reset-password', {
      token,
      password,
      confirm_password: confirmPassword
    });
  },

  resendVerification: async (email) => {
    return apiService.post('/auth/resend-verification', { email });
  },

  logout: () => {
    apiService.setToken(null);
    return Promise.resolve({ success: true });
  },

  getProfile: async () => {
    return apiService.get('/auth/profile');
  },

  updateProfile: async (profileData) => {
    return apiService.put('/auth/profile', profileData);
  }
};

// Products API
export const productsAPI = {
  getAll: async (params = {}) => {
    return apiService.get('/products', params);
  },

  getById: async (id) => {
    return apiService.get(`/products/${id}`);
  },

  create: async (productData) => {
    return apiService.post('/products', productData);
  },

  update: async (id, productData) => {
    return apiService.put(`/products/${id}`, productData);
  },

  delete: async (id) => {
    return apiService.delete(`/products/${id}`);
  },

  getCategories: async () => {
    return apiService.get('/products/categories');
  },

  getSuppliers: async () => {
    return apiService.get('/products/suppliers');
  }
};

// Customers API
export const customersAPI = {
  getAll: async (params = {}) => {
    return apiService.get('/customers', params);
  },

  getById: async (id) => {
    return apiService.get(`/customers/${id}`);
  },

  create: async (customerData) => {
    return apiService.post('/customers', customerData);
  },

  update: async (id, customerData) => {
    return apiService.put(`/customers/${id}`, customerData);
  },

  delete: async (id) => {
    return apiService.delete(`/customers/${id}`);
  },

  getTypes: async () => {
    return apiService.get('/customers/types');
  }
};

// Orders API
export const ordersAPI = {
  getAll: async (params = {}) => {
    return apiService.get('/orders', params);
  },

  getById: async (id) => {
    return apiService.get(`/orders/${id}`);
  },

  create: async (orderData) => {
    return apiService.post('/orders', orderData);
  },

  update: async (id, orderData) => {
    return apiService.put(`/orders/${id}`, orderData);
  },

  updateStatus: async (id, status) => {
    return apiService.put(`/orders/${id}/status`, { status });
  },

  updatePaymentStatus: async (id, payment_status, paid_amount) => {
    return apiService.put(`/orders/${id}/payment-status`, { payment_status, paid_amount });
  },

  delete: async (id) => {
    return apiService.delete(`/orders/${id}`);
  }
};

// Inventory Transactions API
export const inventoryAPI = {
  getTransactions: async (params = {}) => {
    return apiService.get('/inventory/transactions', params);
  },

  createTransaction: async (transactionData) => {
    return apiService.post('/inventory/transactions', transactionData);
  },

  updateStock: async (productId, stockData) => {
    return apiService.put(`/inventory/products/${productId}/stock`, stockData);
  }
};

// Suppliers API
export const suppliersAPI = {
  getAll: async () => {
    return apiService.get('/suppliers');
  },

  getById: async (id) => {
    return apiService.get(`/suppliers/${id}`);
  },

  create: async (supplierData) => {
    return apiService.post('/suppliers', supplierData);
  },

  update: async (id, supplierData) => {
    return apiService.put(`/suppliers/${id}`, supplierData);
  },

  delete: async (id) => {
    return apiService.delete(`/suppliers/${id}`);
  }
};

// Categories API
export const categoriesAPI = {
  getAll: async () => {
    return apiService.get('/products/categories');
  },

  create: async (categoryData) => {
    return apiService.post('/products/categories', categoryData);
  }
};

// Payments API
export const paymentsAPI = {
  getAll: async (params = {}) => {
    return apiService.get('/payments', params);
  },

  getById: async (id) => {
    return apiService.get(`/payments/${id}`);
  },

  create: async (paymentData) => {
    return apiService.post('/payments', paymentData);
  },

  update: async (id, paymentData) => {
    return apiService.put(`/payments/${id}`, paymentData);
  }
};

// Expenses API
export const expensesAPI = {
  getAll: async (params = {}) => {
    return apiService.get('/expenses', params);
  },

  getById: async (id) => {
    return apiService.get(`/expenses/${id}`);
  },

  create: async (expenseData) => {
    return apiService.post('/expenses', expenseData);
  },

  update: async (id, expenseData) => {
    return apiService.put(`/expenses/${id}`, expenseData);
  },

  delete: async (id) => {
    return apiService.delete(`/expenses/${id}`);
  }
};

// Dashboard API
export const dashboardAPI = {
  getMetrics: async () => {
    return apiService.get('/dashboard/metrics');
  },

  getRecentOrders: async (limit = 5) => {
    return apiService.get('/dashboard/recent-orders', { limit });
  },

  getLowStockProducts: async () => {
    return apiService.get('/dashboard/low-stock');
  }
};

export default apiService;
