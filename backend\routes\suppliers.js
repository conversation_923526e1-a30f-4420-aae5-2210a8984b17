const express = require('express');
const router = express.Router();
const { Supplier } = require('../models');
const { authenticateToken } = require('../middleware/auth');

// Get all suppliers
router.get('/', authenticateToken, async (req, res) => {
  try {
    console.log('🏭 Fetching all suppliers...');

    const suppliers = await Supplier.findAll({
      where: { is_active: true },
      order: [['name', 'ASC']]
    });

    console.log(`✅ Found ${suppliers.length} suppliers`);

    // Log data change for monitoring
    if (global.logDataChange) {
      global.logDataChange('READ', 'suppliers', { count: suppliers.length }, req.user?.user_id);
    }

    res.json({
      success: true,
      data: {
        suppliers: suppliers,
        count: suppliers.length
      }
    });
  } catch (error) {
    console.error('❌ Error fetching suppliers:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch suppliers',
      error: error.message
    });
  }
});

// Get supplier by ID
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`🏭 Fetching supplier with ID: ${id}`);

    const supplier = await Supplier.findByPk(id);

    if (!supplier) {
      return res.status(404).json({
        success: false,
        message: 'Supplier not found'
      });
    }

    console.log(`✅ Found supplier: ${supplier.name}`);

    // Log data change for monitoring
    if (global.logDataChange) {
      global.logDataChange('READ', 'suppliers', supplier, req.user?.user_id);
    }

    res.json({
      success: true,
      data: supplier
    });
  } catch (error) {
    console.error('❌ Error fetching supplier:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch supplier',
      error: error.message
    });
  }
});

// Create new supplier
router.post('/', authenticateToken, async (req, res) => {
  try {
    // Basic validation
    if (!req.body.name || req.body.name.trim().length < 2) {
      return res.status(400).json({
        success: false,
        message: 'Supplier name is required and must be at least 2 characters'
      });
    }

    const {
      name,
      contact_person,
      phone,
      email,
      address,
      payment_terms
    } = req.body;

    console.log(`🏭 Creating new supplier: ${name}`);

    // Check if supplier with same name already exists
    const existingSupplier = await Supplier.findOne({
      where: { name: name.trim() }
    });

    if (existingSupplier) {
      return res.status(400).json({
        success: false,
        message: 'Supplier with this name already exists'
      });
    }

    // Create new supplier
    const supplier = await Supplier.create({
      name: name.trim(),
      contact_person: contact_person?.trim() || null,
      phone: phone?.trim() || null,
      email: email?.trim() || null,
      address: address?.trim() || null,
      payment_terms: payment_terms?.trim() || null,
      is_active: true
    });

    console.log(`✅ Supplier created successfully: ${supplier.name} (ID: ${supplier.supplier_id})`);

    // Log data change for monitoring
    if (global.logDataChange) {
      global.logDataChange('CREATE', 'suppliers', supplier, req.user?.user_id);
    }

    res.status(201).json({
      success: true,
      message: 'Supplier created successfully',
      data: supplier
    });
  } catch (error) {
    console.error('❌ Error creating supplier:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create supplier',
      error: error.message
    });
  }
});

// Update supplier
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    // Basic validation
    if (req.body.name && req.body.name.trim().length < 2) {
      return res.status(400).json({
        success: false,
        message: 'Supplier name must be at least 2 characters'
      });
    }

    const { id } = req.params;
    const updateData = req.body;

    console.log(`🏭 Updating supplier with ID: ${id}`);

    const supplier = await Supplier.findByPk(id);

    if (!supplier) {
      return res.status(404).json({
        success: false,
        message: 'Supplier not found'
      });
    }

    // If name is being updated, check for duplicates
    if (updateData.name && updateData.name.trim() !== supplier.name) {
      const existingSupplier = await Supplier.findOne({
        where: {
          name: updateData.name.trim(),
          supplier_id: { [require('sequelize').Op.ne]: id }
        }
      });

      if (existingSupplier) {
        return res.status(400).json({
          success: false,
          message: 'Supplier with this name already exists'
        });
      }
    }

    // Update supplier
    await supplier.update({
      name: updateData.name?.trim() || supplier.name,
      contact_person: updateData.contact_person?.trim() || supplier.contact_person,
      phone: updateData.phone?.trim() || supplier.phone,
      email: updateData.email?.trim() || supplier.email,
      address: updateData.address?.trim() || supplier.address,
      payment_terms: updateData.payment_terms?.trim() || supplier.payment_terms,
      is_active: updateData.is_active !== undefined ? updateData.is_active : supplier.is_active
    });

    console.log(`✅ Supplier updated successfully: ${supplier.name}`);

    // Log data change for monitoring
    if (global.logDataChange) {
      global.logDataChange('UPDATE', 'suppliers', supplier, req.user?.user_id);
    }

    res.json({
      success: true,
      message: 'Supplier updated successfully',
      data: supplier
    });
  } catch (error) {
    console.error('❌ Error updating supplier:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update supplier',
      error: error.message
    });
  }
});

// Delete supplier (soft delete)
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`🏭 Deleting supplier with ID: ${id}`);

    const supplier = await Supplier.findByPk(id);

    if (!supplier) {
      return res.status(404).json({
        success: false,
        message: 'Supplier not found'
      });
    }

    // Soft delete by setting is_active to false
    await supplier.update({ is_active: false });

    console.log(`✅ Supplier deleted successfully: ${supplier.name}`);

    // Log data change for monitoring
    if (global.logDataChange) {
      global.logDataChange('DELETE', 'suppliers', supplier, req.user?.user_id);
    }

    res.json({
      success: true,
      message: 'Supplier deleted successfully'
    });
  } catch (error) {
    console.error('❌ Error deleting supplier:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete supplier',
      error: error.message
    });
  }
});

module.exports = router;
