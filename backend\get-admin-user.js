const { User } = require('./models');

async function getAdminUser() {
  try {
    const adminUser = await User.findOne({
      where: { email: '<EMAIL>' }
    });
    
    if (adminUser) {
      console.log('Admin User ID:', adminUser.user_id);
      console.log('Admin Email:', adminUser.email);
      console.log('Admin Role:', adminUser.role);
    } else {
      console.log('Admin user not found');
    }
  } catch (error) {
    console.error('Error:', error);
  }
}

getAdminUser();
