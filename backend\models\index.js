const { sequelize } = require('../config/database');

// Import all models
const User = require('./User');
const Salesman = require('./Salesman');
const Customer = require('./Customer');
const Product = require('./Product');
const Supplier = require('./Supplier');
const Category = require('./Category');
const InventoryTransaction = require('./InventoryTransaction');
const Order = require('./Order');
const OrderItem = require('./OrderItem');
const Payment = require('./Payment');
const Expense = require('./Expense');
const Employee = require('./Employee');
const SalaryAssignment = require('./SalaryAssignment');
const SalaryPayment = require('./SalaryPayment');
const SalaryHistory = require('./SalaryHistory');
const CommissionStage = require('./CommissionStage');
const CommissionStageOverride = require('./CommissionStageOverride');
const CommissionAssignment = require('./CommissionAssignment');
const CommissionHistory = require('./CommissionHistory');

// Define associations
// User associations
User.hasOne(Salesman, { foreignKey: 'user_id', as: 'salesmanProfile' });
Salesman.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

// Salesman associations
Salesman.hasMany(Customer, { foreignKey: 'salesman_id', as: 'customers' });
Customer.belongsTo(Salesman, { foreignKey: 'salesman_id', as: 'salesman' });

Salesman.hasMany(Order, { foreignKey: 'salesman_id', as: 'orders' });
Order.belongsTo(Salesman, { foreignKey: 'salesman_id', as: 'salesman' });

// Customer associations
Customer.hasMany(Order, { foreignKey: 'customer_id', as: 'orders' });
Order.belongsTo(Customer, { foreignKey: 'customer_id', as: 'customer' });

Customer.hasMany(Payment, { foreignKey: 'customer_id', as: 'payments' });
Payment.belongsTo(Customer, { foreignKey: 'customer_id', as: 'customer' });

Customer.hasMany(InventoryTransaction, { foreignKey: 'customer_id', as: 'transactions' });
InventoryTransaction.belongsTo(Customer, { foreignKey: 'customer_id', as: 'customer' });

// Supplier associations
Supplier.hasMany(Product, { foreignKey: 'supplier_id', as: 'products' });
Product.belongsTo(Supplier, { foreignKey: 'supplier_id', as: 'supplier' });

Supplier.hasMany(InventoryTransaction, { foreignKey: 'supplier_id', as: 'transactions' });
InventoryTransaction.belongsTo(Supplier, { foreignKey: 'supplier_id', as: 'supplier' });

// Product associations
Product.hasMany(InventoryTransaction, { foreignKey: 'product_id', as: 'transactions' });
InventoryTransaction.belongsTo(Product, { foreignKey: 'product_id', as: 'product' });

Product.hasMany(OrderItem, { foreignKey: 'product_id', as: 'orderItems' });
OrderItem.belongsTo(Product, { foreignKey: 'product_id', as: 'product' });

// Category associations
Product.belongsTo(Category, { foreignKey: 'category_id', as: 'categoryInfo' });
Category.hasMany(Product, { foreignKey: 'category_id', as: 'products' });

// Order associations
Order.hasMany(OrderItem, { foreignKey: 'order_id', as: 'items' });
OrderItem.belongsTo(Order, { foreignKey: 'order_id', as: 'order' });

Order.hasMany(Payment, { foreignKey: 'order_id', as: 'payments' });
Payment.belongsTo(Order, { foreignKey: 'order_id', as: 'order' });

Order.hasMany(InventoryTransaction, { foreignKey: 'order_id', as: 'transactions' });
InventoryTransaction.belongsTo(Order, { foreignKey: 'order_id', as: 'order' });

// User tracking associations
User.hasMany(Order, { foreignKey: 'created_by', as: 'createdOrders' });
Order.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });

User.hasMany(InventoryTransaction, { foreignKey: 'recorded_by', as: 'recordedTransactions' });
InventoryTransaction.belongsTo(User, { foreignKey: 'recorded_by', as: 'recorder' });

User.hasMany(Payment, { foreignKey: 'recorded_by', as: 'recordedPayments' });
Payment.belongsTo(User, { foreignKey: 'recorded_by', as: 'recorder' });

User.hasMany(Expense, { foreignKey: 'recorded_by', as: 'recordedExpenses' });
Expense.belongsTo(User, { foreignKey: 'recorded_by', as: 'recorder' });

// Employee associations
User.hasMany(Employee, { foreignKey: 'created_by', as: 'createdEmployees' });
Employee.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });

User.hasMany(Employee, { foreignKey: 'updated_by', as: 'updatedEmployees' });
Employee.belongsTo(User, { foreignKey: 'updated_by', as: 'updater' });

Employee.hasOne(Employee, { foreignKey: 'manager_id', as: 'manager' });
Employee.hasMany(Employee, { foreignKey: 'manager_id', as: 'subordinates' });

// Salary Assignment associations
Employee.hasMany(SalaryAssignment, { foreignKey: 'employee_id', as: 'salaryAssignments' });
SalaryAssignment.belongsTo(Employee, { foreignKey: 'employee_id', as: 'employee' });

User.hasMany(SalaryAssignment, { foreignKey: 'created_by', as: 'createdSalaryAssignments' });
SalaryAssignment.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });

User.hasMany(SalaryAssignment, { foreignKey: 'approved_by', as: 'approvedSalaryAssignments' });
SalaryAssignment.belongsTo(User, { foreignKey: 'approved_by', as: 'approver' });

// Salary Payment associations
Employee.hasMany(SalaryPayment, { foreignKey: 'employee_id', as: 'salaryPayments' });
SalaryPayment.belongsTo(Employee, { foreignKey: 'employee_id', as: 'employee' });

SalaryAssignment.hasMany(SalaryPayment, { foreignKey: 'assignment_id', as: 'payments' });
SalaryPayment.belongsTo(SalaryAssignment, { foreignKey: 'assignment_id', as: 'assignment' });

User.hasMany(SalaryPayment, { foreignKey: 'processed_by', as: 'processedSalaryPayments' });
SalaryPayment.belongsTo(User, { foreignKey: 'processed_by', as: 'processor' });

User.hasMany(SalaryPayment, { foreignKey: 'approved_by', as: 'approvedSalaryPayments' });
SalaryPayment.belongsTo(User, { foreignKey: 'approved_by', as: 'approver' });

// Salary History associations
Employee.hasMany(SalaryHistory, { foreignKey: 'employee_id', as: 'salaryHistory' });
SalaryHistory.belongsTo(Employee, { foreignKey: 'employee_id', as: 'employee' });

SalaryAssignment.hasMany(SalaryHistory, { foreignKey: 'assignment_id', as: 'history' });
SalaryHistory.belongsTo(SalaryAssignment, { foreignKey: 'assignment_id', as: 'assignment' });

SalaryPayment.hasMany(SalaryHistory, { foreignKey: 'payment_id', as: 'history' });
SalaryHistory.belongsTo(SalaryPayment, { foreignKey: 'payment_id', as: 'payment' });

User.hasMany(SalaryHistory, { foreignKey: 'created_by', as: 'createdSalaryHistory' });
SalaryHistory.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });

User.hasMany(SalaryHistory, { foreignKey: 'approved_by', as: 'approvedSalaryHistory' });
SalaryHistory.belongsTo(User, { foreignKey: 'approved_by', as: 'approver' });

// Commission Stage associations
User.hasMany(CommissionStage, { foreignKey: 'created_by', as: 'createdCommissionStages' });
CommissionStage.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });

User.hasMany(CommissionStage, { foreignKey: 'updated_by', as: 'updatedCommissionStages' });
CommissionStage.belongsTo(User, { foreignKey: 'updated_by', as: 'updater' });

// Commission Assignment associations
Salesman.hasMany(CommissionAssignment, { foreignKey: 'salesman_id', as: 'commissionAssignments' });
CommissionAssignment.belongsTo(Salesman, { foreignKey: 'salesman_id', as: 'salesman' });

Order.hasMany(CommissionAssignment, { foreignKey: 'order_id', as: 'commissionAssignments' });
CommissionAssignment.belongsTo(Order, { foreignKey: 'order_id', as: 'order' });

CommissionStage.hasMany(CommissionAssignment, { foreignKey: 'stage_id', as: 'assignments' });
CommissionAssignment.belongsTo(CommissionStage, { foreignKey: 'stage_id', as: 'stage' });

User.hasMany(CommissionAssignment, { foreignKey: 'created_by', as: 'createdCommissionAssignments' });
CommissionAssignment.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });

User.hasMany(CommissionAssignment, { foreignKey: 'approved_by', as: 'approvedCommissionAssignments' });
CommissionAssignment.belongsTo(User, { foreignKey: 'approved_by', as: 'approver' });

User.hasMany(CommissionAssignment, { foreignKey: 'paid_by', as: 'paidCommissionAssignments' });
CommissionAssignment.belongsTo(User, { foreignKey: 'paid_by', as: 'payer' });

// Commission History associations
Salesman.hasMany(CommissionHistory, { foreignKey: 'salesman_id', as: 'commissionHistory' });
CommissionHistory.belongsTo(Salesman, { foreignKey: 'salesman_id', as: 'salesman' });

CommissionAssignment.hasMany(CommissionHistory, { foreignKey: 'assignment_id', as: 'history' });
CommissionHistory.belongsTo(CommissionAssignment, { foreignKey: 'assignment_id', as: 'assignment' });

CommissionStage.hasMany(CommissionHistory, { foreignKey: 'stage_id', as: 'history' });
CommissionHistory.belongsTo(CommissionStage, { foreignKey: 'stage_id', as: 'stage' });

User.hasMany(CommissionHistory, { foreignKey: 'created_by', as: 'createdCommissionHistory' });
CommissionHistory.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });

// Commission Stage Override associations
CommissionStage.hasMany(CommissionStageOverride, { foreignKey: 'stage_id', as: 'overrides' });
CommissionStageOverride.belongsTo(CommissionStage, { foreignKey: 'stage_id', as: 'stage' });
Salesman.hasMany(CommissionStageOverride, { foreignKey: 'salesman_id', as: 'commissionOverrides' });
CommissionStageOverride.belongsTo(Salesman, { foreignKey: 'salesman_id', as: 'salesman' });
User.hasMany(CommissionStageOverride, { foreignKey: 'created_by', as: 'createdCommissionStageOverrides' });
CommissionStageOverride.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });
User.hasMany(CommissionStageOverride, { foreignKey: 'updated_by', as: 'updatedCommissionStageOverrides' });
CommissionStageOverride.belongsTo(User, { foreignKey: 'updated_by', as: 'updater' });

// Export all models
module.exports = {
  sequelize,
  User,
  Salesman,
  Customer,
  Product,
  Supplier,
  Category,
  InventoryTransaction,
  Order,
  OrderItem,
  Payment,
  Expense,
  Employee,
  SalaryAssignment,
  SalaryPayment,
  SalaryHistory,
  CommissionStage,
  CommissionStageOverride,
  CommissionAssignment,
  CommissionHistory
};
