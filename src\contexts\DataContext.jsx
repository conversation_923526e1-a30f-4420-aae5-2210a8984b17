import { useState, createContext, useContext, useMemo, useEffect, useCallback } from 'react';
import axios from 'axios';

/**
 * DataContext - Clean Slate Business Data Management
 *
 * This context starts with completely empty data to allow testing the full business workflow:
 * - Start with no products, customers, orders, or other data
 * - Add functionality to create all business entities from scratch
 * - Maintain all interconnected relationships and calculations
 * - Support complete end-to-end business process testing
 */

// ============================================================================
// CLEAN SLATE - NO INITIAL DATA
// ============================================================================

// All data starts empty - will be populated through the application workflow

const DataContext = createContext(undefined);

export const DataProvider = ({ children }) => {
  // Core business entities - WILL BE LOADED FROM BACKEND
  const [products, setProducts] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [salesmen, setSalesmen] = useState([]);
  const [orders, setOrders] = useState([]);
  const [payments, setPayments] = useState([]);
  const [expenses, setExpenses] = useState([]);
  const [inventoryTransactions, setInventoryTransactions] = useState([]);

  // ENHANCEMENT: Employee Management System
  const [employees, setEmployees] = useState([]);
  const [salaryAssignments, setSalaryAssignments] = useState([]);
  const [salaryPayments, setSalaryPayments] = useState([]);

  // ENHANCEMENT: Commission Management System
  const [commissionStagesData, setCommissionStagesData] = useState([]);
  const [commissionAssignments, setCommissionAssignments] = useState([]);

  // Loading states for each data type
  const [loading, setLoading] = useState({
    products: false,
    customers: false,
    salesmen: false,
    orders: false,
    payments: false,
    expenses: false,
    inventoryTransactions: false,
    employees: false,
    salaryAssignments: false,
    salaryPayments: false
  });

  // Initial data loaded flag
  const [initialDataLoaded, setInitialDataLoaded] = useState(false);
  const [dataLoadingError, setDataLoadingError] = useState(null);

  // Dynamic suppliers and categories - Production ready with database integration
  const [suppliers, setSuppliers] = useState([]);
  const [categories, setCategories] = useState([]);

  // Auto-increment IDs for new entities (only used ones kept)
  const [nextOrderId, setNextOrderId] = useState(1);
  const [nextPaymentId, setNextPaymentId] = useState(1);
  const [nextExpenseId, setNextExpenseId] = useState(1);
  const [nextTransactionId, setNextTransactionId] = useState(1);
  const [nextSupplierId, setNextSupplierId] = useState(5);

  // ENHANCEMENT: Company Losses Calculation (needed for revenue adjustment)
  const companyLosses = useMemo(() => {
    return inventoryTransactions
      .filter(transaction =>
        transaction.type === 'loss' ||
        (transaction.type === 'out' && transaction.notes?.includes('Company Loss'))
      )
      .reduce((sum, transaction) => sum + (transaction.totalCost || 0), 0);
  }, [inventoryTransactions]);

  // Calculate original sales revenue (needed for both adjusted revenue and profit)
  const salesRevenue = useMemo(() => {
    // FIXED: Only include delivered orders in revenue calculation (exclude cancelled/rejected orders)
    return orders
      .filter(order => order.status === 'delivered' && order.status !== 'cancelled')
      .reduce((sum, order) => sum + order.totalAmount, 0);
  }, [orders]);

  // Computed values for reports and analytics (memoized to prevent infinite re-renders)
  const totalRevenue = useMemo(() => {
    // ENHANCEMENT: Adjust revenue calculation to account for company losses
    // Formula: Adjusted Revenue = Total Sales Revenue - Company Losses
    return salesRevenue - companyLosses;
  }, [salesRevenue, companyLosses]);

  const totalExpenses = useMemo(() => {
    return expenses.reduce((sum, expense) => sum + expense.amount, 0);
  }, [expenses]);

  const profit = useMemo(() => {
    // FIXED: Profit calculation should use original sales revenue, not adjusted revenue
    // Company losses are asset write-downs, not operational expenses
    // Formula: Profit = Total Sales Revenue - Total Expenses
    return salesRevenue - totalExpenses;
  }, [salesRevenue, totalExpenses]);

  const lowStockProducts = useMemo(() => {
    return products.filter(product => product.stock <= product.minStock);
  }, [products]);

  const salesmanPerformance = useMemo(() => {
    return salesmen.map(salesman => {
      // Only include delivered orders for salesman performance (exclude cancelled/rejected orders)
      const salesmanOrders = orders.filter(order =>
        order.salesmanId === salesman.id && order.status === 'delivered'
      );
      const totalSales = salesmanOrders.reduce((sum, order) => sum + order.totalAmount, 0);
      const commission = totalSales * salesman.commissionRate;

      return {
        ...salesman,
        currentMonthSales: totalSales,
        currentMonthCommission: commission,
        ordersCount: salesmanOrders.length,
        targetAchievement: (totalSales / salesman.monthlyTarget) * 100
      };
    });
  }, [salesmen, orders]);

  const getCustomerBalance = (customerId) => {
    const customer = customers.find(c => c.id === customerId);
    return customer ? customer.currentBalance : 0;
  };

  // Calculate total purchased amount for a customer (from delivered/completed orders)
  const getCustomerTotalPurchased = (customerId) => {
    return orders
      .filter(order =>
        order.customerId === customerId &&
        order.status !== 'cancelled' && // FIXED: Exclude cancelled orders
        (order.status === 'delivered' || order.status === 'completed')
      )
      .reduce((sum, order) => sum + (order.totalAmount || 0), 0);
  };

  // Calculate pending amount for a customer (unpaid/partially paid orders)
  const getCustomerPendingAmount = (customerId) => {
    return orders
      .filter(order =>
        order.customerId === customerId &&
        order.status !== 'cancelled' && // FIXED: Exclude cancelled orders
        (order.paymentStatus === 'pending' || order.paymentStatus === 'partial')
      )
      .reduce((sum, order) => sum + ((order.totalAmount || 0) - (order.paidAmount || 0)), 0);
  };

  // Calculate total orders count for a customer (excluding cancelled orders)
  const getCustomerTotalOrders = (customerId) => {
    return orders
      .filter(order =>
        order.customerId === customerId &&
        order.status !== 'cancelled' // FIXED: Exclude cancelled orders
      ).length;
  };

  // Get order history for a customer (including cancelled orders for history visibility)
  const getCustomerOrderHistory = (customerId, limit = 10) => {
    return orders
      .filter(order => order.customerId === customerId)
      .sort((a, b) => new Date(b.date) - new Date(a.date)) // Most recent first
      .slice(0, limit)
      .map(order => ({
        id: order.orderNumber,
        date: order.date,
        amount: order.totalAmount,
        status: order.status
      }));
  };

  // Additional computed functions for AdminDashboard
  const getTotalProfit = () => profit;
  const getPendingPayments = () => {
    // FIXED: Exclude cancelled orders from pending payments calculation
    return orders.reduce((sum, order) => {
      if (order.paymentStatus !== 'paid' && order.status !== 'cancelled') {
        return sum + (order.totalAmount - order.paidAmount);
      }
      return sum;
    }, 0);
  };

  const getInventoryValue = () => {
    return products.reduce((sum, product) => sum + (product.price * product.stock), 0);
  };

  const getRecentOrders = (limit = 5) => {
    return orders
      .sort((a, b) => new Date(b.date) - new Date(a.date))
      .slice(0, limit)
      .map(order => {
        const customer = customers.find(c => c.id === order.customerId);
        return {
          ...order,
          customer: customer || { name: 'Unknown Customer' }
        };
      });
  };

  // ============================================================================
  // DATA FETCHING FUNCTIONS - LOAD FROM BACKEND
  // ============================================================================

  const getAuthToken = () => {
    const token = localStorage.getItem('auth_token');
    if (!token) {
      console.warn('⚠️ No authentication token found');
      return null;
    }
    return token;
  };

  const fetchSuppliers = useCallback(async () => {
    try {
      const token = getAuthToken();
      if (!token) return;

      setLoading(prev => ({ ...prev, suppliers: true }));
      console.log('🏭 Fetching suppliers from backend...');

      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api'}/products/suppliers`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch suppliers: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ Suppliers fetched:', result);

      if (result.success && result.data) {
        // Transform backend data to frontend format
        const suppliersArray = result.data.suppliers || result.data;
        const transformedSuppliers = suppliersArray.map(supplier => ({
          id: supplier.supplier_id,
          name: supplier.name,
          contactPerson: supplier.contact_person,
          phone: supplier.phone,
          email: supplier.email,
          address: supplier.address,
          paymentTerms: supplier.payment_terms,
          isActive: supplier.is_active
        }));

        setSuppliers(transformedSuppliers);
        console.log('🏭 Suppliers state updated:', transformedSuppliers.length, 'suppliers');
      }
    } catch (error) {
      console.error('❌ Failed to fetch suppliers:', error);
    } finally {
      setLoading(prev => ({ ...prev, suppliers: false }));
    }
  }, []);

  const fetchProducts = useCallback(async () => {
    try {
      const token = getAuthToken();
      if (!token) return;

      setLoading(prev => ({ ...prev, products: true }));
      console.log('📦 Fetching products from backend...');

      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api'}/products`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch products: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ Products fetched:', result);

      if (result.success && result.data) {
        // Transform backend data to frontend format
        const productsArray = result.data.products || result.data;
        const transformedProducts = productsArray.map(product => {
          // Use supplier name from backend data directly to avoid dependency loop
          const supplierName = product.supplier?.name || 'Unknown Supplier';

          return {
            id: product.product_id,
            name: product.name,
            // Use category relationship data instead of old category field
            category: product.categoryInfo?.name || product.category || 'Uncategorized',
            categoryId: product.category_id,
            sku: product.sku,
            barcode: product.barcode,
            description: product.description,
            price: product.unit_price,
            unit_price: product.unit_price, // Add both fields for compatibility
            costPrice: product.cost_price,
            stock: product.current_stock,
            minStock: product.min_stock_level,
            maxStock: product.max_stock_level,
            unitOfMeasure: product.unit_of_measure,
            supplier: supplierName,
            supplierId: product.supplier_id,
            storageLocation: product.storage_location,
            batchNumber: product.batch_number,
            expirationDate: product.expiration_date?.split('T')[0],
            lastRestocked: product.last_restocked?.split('T')[0] || product.updatedAt?.split('T')[0] || new Date().toISOString().split('T')[0],
            isActive: product.is_active
          };
        });

        setProducts(transformedProducts);
        console.log('📦 Products state updated:', transformedProducts.length, 'products');
      }
    } catch (error) {
      console.error('❌ Failed to fetch products:', error);
    } finally {
      setLoading(prev => ({ ...prev, products: false }));
    }
  }, []); // Remove suppliers dependency to prevent infinite loop

  const fetchCustomers = useCallback(async () => {
    try {
      const token = getAuthToken();
      if (!token) return;

      setLoading(prev => ({ ...prev, customers: true }));
      console.log('👥 Fetching customers from backend...');

      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api'}/customers`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch customers: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ Customers fetched:', result);

      if (result.success && result.data) {
        // Transform backend data to frontend format
        const customersArray = result.data.customers || result.data;
        const transformedCustomers = customersArray.map(customer => ({
          id: customer.customer_id,
          name: customer.name,
          type: customer.type,
          contact: customer.contact,
          phone: customer.phone,
          email: customer.email,
          address: customer.address,
          currentBalance: customer.current_balance,
          status: customer.status,
          registrationDate: customer.createdAt?.split('T')[0] || new Date().toISOString().split('T')[0],
          lastOrderDate: customer.last_order_date,
          totalOrders: customer.total_orders || 0,
          totalSpent: customer.total_spent || 0,
          averageOrderValue: customer.total_orders > 0 ? (customer.total_spent / customer.total_orders) : 0
        }));

        setCustomers(transformedCustomers);
        console.log('👥 Customers state updated:', transformedCustomers.length, 'customers');
      }
    } catch (error) {
      console.error('❌ Failed to fetch customers:', error);
    } finally {
      setLoading(prev => ({ ...prev, customers: false }));
    }
  }, []);

  const fetchOrders = useCallback(async () => {
    try {
      const token = getAuthToken();
      if (!token) return;

      setLoading(prev => ({ ...prev, orders: true }));
      console.log('📋 Fetching orders from backend...');

      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api'}/orders`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch orders: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ Orders fetched:', result);

      if (result.success && result.data) {
        // Transform backend data to frontend format
        const ordersArray = result.data.orders || result.data;
        const transformedOrders = ordersArray.map(order => {
          // Transform order items to include product information
          const transformedItems = (order.items || []).map(item => ({
            id: item.order_item_id,
            productId: item.product_id,
            productName: item.product?.name || 'Unknown Product',
            quantity: item.quantity,
            unitPrice: item.unit_price,
            totalPrice: item.total_price,
            discountAmount: item.discount_amount || 0
          }));

          // Transform customer information
          const customer = order.customer ? {
            id: order.customer.customer_id,
            name: order.customer.name,
            type: order.customer.type
          } : null;

          // Transform salesman information
          const salesman = order.salesman ? {
            id: order.salesman.salesman_id,
            name: order.salesman.full_name
          } : null;



          return {
            id: order.order_id,
            orderNumber: order.order_number,
            customerId: order.customer_id,
            salesmanId: order.salesman_id,
            date: order.order_date?.split('T')[0] || new Date().toISOString().split('T')[0],
            dueDate: order.due_date?.split('T')[0],
            status: order.status,
            paymentStatus: order.payment_status,
            totalAmount: order.total_amount,
            paidAmount: order.paid_amount || 0,
            items: transformedItems, // Properly transformed order items
            customer: customer, // Include customer information
            salesman: salesman // Include salesman information
          };
        });

        setOrders(transformedOrders);
        console.log('📋 Orders state updated:', transformedOrders.length, 'orders');
      }
    } catch (error) {
      console.error('❌ Failed to fetch orders:', error);
    } finally {
      setLoading(prev => ({ ...prev, orders: false }));
    }
  }, []);

  const fetchPayments = useCallback(async () => {
    try {
      const token = getAuthToken();
      if (!token) return;

      setLoading(prev => ({ ...prev, payments: true }));
      console.log('💳 Fetching payments from backend...');

      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api'}/payments`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch payments: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ Payments fetched:', result);

      if (result.success && result.data) {
        // Transform backend data to frontend format
        const transformedPayments = result.data.map(payment => ({
          id: payment.payment_id,
          paymentNumber: payment.payment_number,
          customerId: payment.customer_id,
          orderId: payment.order_id,
          amount: payment.amount,
          paymentMethod: payment.payment_method,
          paymentDate: payment.payment_date?.split('T')[0] || new Date().toISOString().split('T')[0],
          status: payment.status,
          notes: payment.notes
        }));

        setPayments(transformedPayments);
        console.log('💳 Payments state updated:', transformedPayments.length, 'payments');
      }
    } catch (error) {
      console.error('❌ Failed to fetch payments:', error);
    } finally {
      setLoading(prev => ({ ...prev, payments: false }));
    }
  }, []);

  const fetchExpenses = useCallback(async () => {
    try {
      const token = getAuthToken();
      if (!token) return;

      setLoading(prev => ({ ...prev, expenses: true }));
      console.log('💰 Fetching expenses from backend...');

      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api'}/expenses`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch expenses: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ Expenses fetched:', result);

      if (result.success && result.data) {
        // Transform backend data to frontend format
        const transformedExpenses = result.data.map(expense => ({
          id: expense.expense_id,
          category: expense.category,
          amount: expense.amount,
          description: expense.description,
          expenseDate: expense.expense_date?.split('T')[0] || new Date().toISOString().split('T')[0],
          paymentMethod: expense.payment_method,
          receiptUrl: expense.receipt_url,
          status: expense.status
        }));

        setExpenses(transformedExpenses);
        console.log('💰 Expenses state updated:', transformedExpenses.length, 'expenses');
      }
    } catch (error) {
      console.error('❌ Failed to fetch expenses:', error);
    } finally {
      setLoading(prev => ({ ...prev, expenses: false }));
    }
  }, []);

  // Fetch salesmen from backend
  const fetchSalesmen = useCallback(async () => {
    try {
      const token = getAuthToken();
      if (!token) {
        console.warn('⚠️ No auth token, skipping salesmen fetch');
        return;
      }

      console.log('👤 Fetching salesmen from backend...');

      // Use the correct backend port for salesmen API
      const response = await fetch('http://localhost:3001/api/salesmen', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch salesmen: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ Salesmen fetched:', result.data?.salesmen?.length || 0);

      const salesmenArray = result.data?.salesmen || [];
      const transformedSalesmen = salesmenArray.map(salesman => ({
        id: salesman.salesman_id,
        name: salesman.full_name,
        full_name: salesman.full_name,
        userId: salesman.user_id,
        contactInfo: salesman.contact_info || {},
        commissionRate: salesman.commission_rate || 0,
        monthlyTarget: salesman.monthly_target || 0,
        isActive: salesman.is_active,
        user: salesman.user || null
      }));

      setSalesmen(transformedSalesmen);
    } catch (error) {
      console.error('❌ Failed to fetch salesmen:', error);
      // Keep existing salesmen on error
    }
  }, []);

  // Fetch categories from backend
  const fetchCategories = useCallback(async () => {
    try {
      const token = getAuthToken();
      if (!token) {
        console.warn('⚠️ No auth token, skipping categories fetch');
        return;
      }

      console.log('📂 Fetching categories from backend...');

      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3002/api'}/products/categories`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch categories: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ Categories fetched:', result.data?.categories?.length || 0);

      const categoriesArray = result.data?.categories || [];
      const transformedCategories = categoriesArray.map(category => ({
        id: category.category_id,
        name: category.name,
        description: category.description,
        isActive: category.is_active
      }));

      setCategories(transformedCategories);
    } catch (error) {
      console.error('❌ Failed to fetch categories:', error);
      // Keep existing categories on error
    }
  }, []);

  // ENHANCEMENT: Employee Management System - Fetch Functions
  const fetchEmployees = useCallback(async () => {
    try {
      const token = getAuthToken();
      if (!token) return;

      setLoading(prev => ({ ...prev, employees: true }));
      console.log('👥 Fetching employees from backend...');

      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api'}/employees`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch employees: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ Employees fetched:', result);

      if (result.success && result.data) {
        const employeesArray = result.data.employees || result.data;
        const transformedEmployees = employeesArray.map(employee => ({
          id: employee.employee_id,
          employeeNumber: employee.employee_number,
          name: employee.full_name,
          email: employee.email,
          phone: employee.phone,
          position: employee.position,
          department: employee.department,
          hireDate: employee.hire_date?.split('T')[0],
          status: employee.status,
          employeeType: employee.employee_type,
          address: employee.address || {},
          emergencyContact: employee.emergency_contact || {},
          bankDetails: employee.bank_details || {},
          profilePhoto: employee.profile_photo,
          managerId: employee.manager_id,
          notes: employee.notes,
          // Include current salary assignment if available
          currentSalaryAssignment: employee.salaryAssignments?.[0] || null,
          basicSalary: employee.salaryAssignments?.[0]?.base_salary || 0,
          totalSalary: employee.salaryAssignments?.[0] ?
            (parseFloat(employee.salaryAssignments[0].base_salary) +
             Object.values(employee.salaryAssignments[0].allowances || {}).reduce((sum, val) => sum + parseFloat(val || 0), 0) +
             Object.values(employee.salaryAssignments[0].bonuses || {}).reduce((sum, val) => sum + parseFloat(val || 0), 0) -
             Object.values(employee.salaryAssignments[0].deductions || {}).reduce((sum, val) => sum + parseFloat(val || 0), 0)) : 0
        }));

        setEmployees(transformedEmployees);
        console.log('👥 Employees state updated:', transformedEmployees.length, 'employees');
      }
    } catch (error) {
      console.error('❌ Failed to fetch employees:', error);
    } finally {
      setLoading(prev => ({ ...prev, employees: false }));
    }
  }, []);

  const fetchSalaryPayments = useCallback(async () => {
    try {
      const token = getAuthToken();
      if (!token) return;

      setLoading(prev => ({ ...prev, salaryPayments: true }));
      console.log('💳 Fetching salary payments from backend...');

      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api'}/employees/salary-payments`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch salary payments: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ Salary payments fetched:', result);

      if (result.success && result.data) {
        const paymentsArray = result.data.payments || result.data;
        const transformedPayments = paymentsArray.map(payment => ({
          id: payment.payment_id,
          paymentNumber: payment.payment_number,
          employeeId: payment.employee_id,
          employeeName: payment.employee?.full_name || 'Unknown Employee',
          assignmentId: payment.assignment_id,
          payPeriodStart: payment.pay_period_start?.split('T')[0],
          payPeriodEnd: payment.pay_period_end?.split('T')[0],
          paymentDate: payment.payment_date?.split('T')[0],
          grossSalary: parseFloat(payment.gross_salary),
          totalAllowances: parseFloat(payment.total_allowances),
          totalBonuses: parseFloat(payment.total_bonuses),
          totalDeductions: parseFloat(payment.total_deductions),
          netSalary: parseFloat(payment.net_salary),
          overtimeHours: parseFloat(payment.overtime_hours),
          overtimeAmount: parseFloat(payment.overtime_amount),
          workingDays: payment.working_days,
          presentDays: payment.present_days,
          absentDays: payment.absent_days,
          leaveDays: payment.leave_days,
          paymentMethod: payment.payment_method,
          status: payment.status,
          receiptGenerated: payment.receipt_generated,
          receiptSent: payment.receipt_sent,
          receiptUrl: payment.receipt_url,
          notes: payment.notes
        }));

        setSalaryPayments(transformedPayments);
        console.log('💳 Salary payments state updated:', transformedPayments.length, 'payments');
      }
    } catch (error) {
      console.error('❌ Failed to fetch salary payments:', error);
    } finally {
      setLoading(prev => ({ ...prev, salaryPayments: false }));
    }
  }, []);

  const fetchInventoryTransactions = useCallback(async () => {
    try {
      const token = getAuthToken();
      if (!token) return;

      setLoading(prev => ({ ...prev, inventoryTransactions: true }));
      console.log('📦 Fetching inventory transactions from backend...');

      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api'}/inventory/transactions`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch inventory transactions: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ Inventory transactions fetched:', result);

      if (result.success && result.data) {
        // Transform backend data to frontend format
        const transactionsArray = result.data.transactions || result.data;
        const transformedTransactions = transactionsArray.map(transaction => {
          // Find product name from products array
          let productName = 'Unknown Product';
          if (transaction.product_id) {
            const product = products.find(p => p.id === transaction.product_id);
            if (product) {
              productName = product.name;
            } else {
              // If product not found in local array, use backend product data if available
              productName = transaction.product?.name || `Product ID: ${transaction.product_id}`;
            }
          }

          // Find supplier name from suppliers array
          let supplierName = null;
          if (transaction.supplier_id) {
            const supplier = suppliers.find(s => s.id === transaction.supplier_id);
            if (supplier) {
              supplierName = supplier.name;
            } else {
              // If supplier not found in local array, use backend supplier data if available
              supplierName = transaction.supplier?.name || null;
            }
          }

          // Find customer name from customers array
          let customerName = null;
          if (transaction.customer_id) {
            const customer = customers.find(c => c.id === transaction.customer_id);
            if (customer) {
              customerName = customer.name;
            } else {
              // If customer not found in local array, use backend customer data if available
              customerName = transaction.customer?.name || null;
            }
          }

          return {
            id: transaction.transaction_id,
            type: transaction.type,
            productId: transaction.product_id,
            productName: productName,
            quantity: transaction.quantity,
            unitPrice: transaction.unit_price,
            totalCost: transaction.total_cost,
            supplier: supplierName || '-',
            customer: customerName || '-',
            date: transaction.transaction_date?.split('T')[0] || new Date().toISOString().split('T')[0],
            status: transaction.status,
            notes: transaction.notes,
            recordedBy: transaction.recorder?.email || transaction.recorded_by,
            invoiceNumber: transaction.invoice_number,
            referenceNumber: transaction.reference_number,
            // Additional comprehensive fields
            batchNumber: transaction.batch_number,
            expirationDate: transaction.expiration_date?.split('T')[0],
            storageLocation: transaction.storage_location
          };
        });

        setInventoryTransactions(transformedTransactions);
        console.log('📦 Inventory transactions state updated:', transformedTransactions.length, 'transactions');
      }
    } catch (error) {
      console.error('❌ Failed to fetch inventory transactions:', error);
    } finally {
      setLoading(prev => ({ ...prev, inventoryTransactions: false }));
    }
  }, []); // Remove dependencies to prevent infinite loop

  // Fetch commission stages from backend with auth check and auto-logout on 403
  const fetchCommissionStages = useCallback(async () => {
    const token = getAuthToken();
    console.log('🔑 Auth token for commission stages:', token);
    if (!token) {
      setDataLoadingError('You must be logged in to view commission stages.');
      setCommissionStagesData([]);
      return;
    }
    try {
      setLoading(prev => ({ ...prev, commissionStages: true }));
      console.log('🔄 Fetching commission stages from backend...');
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api'}/commissions/stages`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      if (response.status === 403) {
        console.error('❌ 403 Forbidden on commission stages. Logging out user.');
        localStorage.removeItem('auth_token');
        setTimeout(() => window.location.reload(), 500);
        setDataLoadingError('Session expired or access denied. Please log in again.');
        setCommissionStagesData([]);
        return;
      }
      if (!response.ok) {
        throw new Error(`Failed to fetch commission stages: ${response.status}`);
      }
      const result = await response.json();
      console.log('Commission stages API result:', result);
      if (result.success && result.data) {
        setCommissionStagesData(result.data);
      } else {
        setCommissionStagesData([]);
      }
    } catch (error) {
      setDataLoadingError('Failed to fetch commission stages. Please try again.');
      setCommissionStagesData([]);
      console.error('❌ Commission stages fetch failed:', error);
    } finally {
      setLoading(prev => ({ ...prev, commissionStages: false }));
    }
  }, []);

  // Fetch commission assignments from backend
  const fetchCommissionAssignments = useCallback(async () => {
    try {
      setLoading(prev => ({ ...prev, commissionAssignments: true }));
      console.log('🔄 Fetching commission assignments from backend...');

      const token = localStorage.getItem('auth_token'); // FIX: use correct token key
      const response = await fetch('http://localhost:3001/api/commissions/summary', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setCommissionAssignments(result.data);
          console.log(`✅ Loaded ${result.data.length} commission assignments`);
        } else {
          console.error('❌ Failed to fetch commission assignments:', result.message);
        }
      } else {
        console.error('❌ Commission assignments fetch failed:', response.status);
      }
    } catch (error) {
      console.error('❌ Error fetching commission assignments:', error);
    } finally {
      setLoading(prev => ({ ...prev, commissionAssignments: false }));
    }
  }, []);

  // Load all initial data from backend - PRODUCTION READY
  const loadInitialData = useCallback(async () => {
    console.log('🚀 Loading initial data from backend...');
    setDataLoadingError(null);
    setInitialDataLoaded(false);

    try {
      const token = getAuthToken();
      if (!token) {
        console.warn('⚠️ No auth token found, skipping initial data load');
        setInitialDataLoaded(true);
        return;
      }

      // Load data sequentially to handle dependencies properly
      console.log('📊 Step 1: Loading suppliers...');
      await fetchSuppliers();

      console.log('📊 Step 2: Loading customers...');
      await fetchCustomers();

      console.log('📊 Step 3: Loading products (depends on suppliers)...');
      await fetchProducts();

      console.log('📊 Step 4: Loading orders...');
      await fetchOrders();

      console.log('📊 Step 5: Loading inventory transactions (depends on products)...');
      await fetchInventoryTransactions();

      console.log('📊 Step 6: Loading payments...');
      await fetchPayments();

      console.log('📊 Step 7: Loading expenses...');
      await fetchExpenses();

      console.log('📊 Step 8: Loading salesmen...');
      await fetchSalesmen();

      console.log('📊 Step 9: Loading categories...');
      await fetchCategories();

      console.log('📊 Step 10: Loading employees...');
      await fetchEmployees();

      console.log('📊 Step 11: Loading salary payments...');
      await fetchSalaryPayments();

      console.log('📊 Step 12: Loading commission stages...');
      await fetchCommissionStages();

      console.log('📊 Step 13: Loading commission assignments...');
      await fetchCommissionAssignments();

      setInitialDataLoaded(true);
      console.log('✅ Initial data loading completed successfully');

    } catch (error) {
      console.error('❌ Failed to load initial data:', error);
      setDataLoadingError(error.message);
      // Still mark as loaded to prevent infinite loading
      setInitialDataLoaded(true);
    }
  }, []); // Remove dependencies to prevent infinite loop

  // Load data on component mount (FIXED - No dependencies to prevent infinite loop)
  useEffect(() => {
    const token = getAuthToken();
    if (token) {
      loadInitialData();
    } else {
      console.log('⚠️ No auth token found, skipping initial data load');
    }
  }, []); // Empty dependency array - run only once on mount

  // ============================================================================
  // REFRESH FUNCTIONS FOR COMPONENT-LEVEL DATA REFRESH
  // ============================================================================

  // Refresh all data without page reload
  const refreshAllData = async () => {
    console.log('🔄 Refreshing all data...');
    setDataLoadingError(null);

    try {
      const token = getAuthToken();
      if (!token) {
        console.warn('⚠️ No auth token found, cannot refresh data');
        return;
      }

      // Refresh all data sources
      await Promise.all([
        fetchSuppliers(),
        fetchCustomers(),
        fetchProducts(),
        fetchOrders(),
        fetchInventoryTransactions(),
        fetchPayments(),
        fetchExpenses(),
        fetchSalesmen(),
        fetchCategories(),
        fetchEmployees(),
        fetchSalaryPayments(),
        fetchCommissionStages(),
        fetchCommissionAssignments()
      ]);

      console.log('✅ All data refreshed successfully');
    } catch (error) {
      console.error('❌ Failed to refresh data:', error);
      setDataLoadingError(error.message);
      throw error;
    }
  };

  // Refresh specific data types
  const refreshProducts = async () => {
    console.log('🔄 Refreshing products...');
    try {
      await fetchProducts();
      console.log('✅ Products refreshed');
    } catch (error) {
      console.error('❌ Failed to refresh products:', error);
      throw error;
    }
  };

  const refreshOrders = async () => {
    console.log('🔄 Refreshing orders...');
    try {
      await fetchOrders();
      console.log('✅ Orders refreshed');
    } catch (error) {
      console.error('❌ Failed to refresh orders:', error);
      throw error;
    }
  };

  const refreshCustomers = async () => {
    console.log('🔄 Refreshing customers...');
    try {
      await fetchCustomers();
      console.log('✅ Customers refreshed');
    } catch (error) {
      console.error('❌ Failed to refresh customers:', error);
      throw error;
    }
  };

  const refreshInventory = async () => {
    console.log('🔄 Refreshing inventory...');
    try {
      await Promise.all([
        fetchProducts(),
        fetchInventoryTransactions(),
        fetchSuppliers(),
        fetchCategories()
      ]);
      console.log('✅ Inventory data refreshed');
    } catch (error) {
      console.error('❌ Failed to refresh inventory:', error);
      throw error;
    }
  };

  // ============================================================================
  // CRUD FUNCTIONS WITH BACKEND INTEGRATION
  // ============================================================================

  // Inventory Transaction Management
  const addInventoryTransaction = async (transactionData) => {
    try {
      const token = getAuthToken();
      if (!token) {
        console.warn('⚠️ No auth token, adding transaction to local state only');
        // Fallback to local state if no token
        const newTransaction = {
          id: nextTransactionId,
          date: new Date().toISOString().split('T')[0],
          ...transactionData,
          status: transactionData.status || 'completed'
        };
        setInventoryTransactions(prev => [...prev, newTransaction]);
        setNextTransactionId(prev => prev + 1);
        return newTransaction;
      }

      console.log('📦 Creating inventory transaction:', transactionData);

      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api'}/inventory/transactions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          type: transactionData.type,
          product_id: transactionData.productId,
          quantity: parseInt(transactionData.quantity),
          unit_price: parseFloat(transactionData.unitPrice) || 0,
          supplier_id: transactionData.supplierId || null,
          customer_id: transactionData.customerId || null,
          reference_number: transactionData.referenceNumber || null,
          invoice_number: transactionData.invoiceNumber || null,
          notes: transactionData.notes || null,
          batch_number: transactionData.batchNumber || null,
          expiration_date: transactionData.expirationDate || null,
          storage_location: transactionData.storageLocation || null
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to create inventory transaction: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ Inventory transaction created:', result);

      // Refresh inventory transactions data from backend
      await fetchInventoryTransactions();

      return result.data;
    } catch (error) {
      console.error('❌ Failed to create inventory transaction:', error);

      // Fallback to local state on error
      const newTransaction = {
        id: nextTransactionId,
        date: new Date().toISOString().split('T')[0],
        ...transactionData,
        status: transactionData.status || 'completed'
      };
      setInventoryTransactions(prev => [...prev, newTransaction]);
      setNextTransactionId(prev => prev + 1);
      return newTransaction;
    }
  };

  // Product Management with Backend Integration
  const addProduct = async (productData) => {
    try {
      // Get auth token
      const token = localStorage.getItem('auth_token');
      console.log('🔑 Auth token check:', token ? 'Found' : 'Not found');
      console.log('🔑 Token value:', token);
      if (!token) {
        throw new Error('No authentication token found. Please log in again.');
      }

      // Handle supplier creation/lookup
      let supplierId = null;
      if (productData.supplier && productData.supplier.trim() !== '') {
        console.log('🏭 Processing supplier:', productData.supplier);

        // Check if supplier is already a UUID (existing supplier)
        if (productData.supplier.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
          supplierId = productData.supplier;
          console.log('✅ Using existing supplier UUID:', supplierId);
        } else {
          // Create or find supplier by name
          const supplier = await findOrCreateSupplier(productData.supplier);
          if (supplier) {
            supplierId = supplier.id;
            console.log('✅ Supplier resolved:', supplier);
          }
        }
      }

      // Prepare product data for backend
      const backendProductData = {
        name: productData.name,
        category: productData.category,
        sku: productData.sku || `SKU-${Date.now()}`,
        barcode: productData.barcode || '',
        description: productData.description || '',
        unit_price: parseFloat(productData.price),
        cost_price: parseFloat(productData.costPrice || productData.price * 0.7),
        current_stock: parseInt(productData.stock),
        min_stock_level: parseInt(productData.minStock || 50),
        max_stock_level: parseInt(productData.maxStock || 1000),
        unit_of_measure: productData.unitOfMeasure || 'pieces',
        supplier_id: supplierId,

        // Additional product details
        storage_location: productData.storageLocation || '',
        batch_number: productData.batchNumber || '',
        expiration_date: productData.expirationDate || null,

        // Transaction details for inventory transaction
        invoice_number: productData.invoiceNumber || '',
        reference_number: productData.referenceNumber || '',
        notes: productData.notes || '',

        is_active: true
      };

      console.log('🚀 Adding product to backend:', backendProductData);

      // Send to backend
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api'}/products`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(backendProductData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to add product');
      }

      const result = await response.json();
      console.log('✅ Product added to backend:', result);

      // Update local state with backend response
      const newProduct = {
        id: result.data.product_id,
        name: result.data.name,
        category: result.data.category,
        sku: result.data.sku,
        price: result.data.unit_price,
        stock: result.data.current_stock,
        minStock: result.data.min_stock_level,
        supplier: productData.supplier || 'Unknown Supplier',
        lastRestocked: new Date().toISOString().split('T')[0]
      };

      // Refresh products data from backend to ensure synchronization
      await fetchProducts();

      // Create inventory transaction for product addition with proper foreign keys
      await addInventoryTransaction({
        type: 'in',
        productId: result.data.product_id,
        productName: result.data.name,
        quantity: result.data.current_stock,
        supplierId: supplierId, // Use the actual supplier ID
        customerId: null, // No customer for stock-in transactions
        unitPrice: result.data.unit_price,
        totalCost: result.data.unit_price * result.data.current_stock,
        invoiceNumber: productData.invoiceNumber || '',
        referenceNumber: productData.referenceNumber || '',
        notes: productData.notes || 'Initial stock entry'
      });

      return newProduct;
    } catch (error) {
      console.error('❌ Failed to add product:', error);
      throw error;
    }
  };

  // Update Product with Backend Integration
  const updateProduct = async (productId, updateData) => {
    try {
      const token = getAuthToken();
      if (!token) {
        throw new Error('No authentication token found. Please log in again.');
      }

      console.log('🔄 Updating product:', productId, updateData);

      // Prepare update data for backend
      const backendUpdateData = {
        name: updateData.name,
        description: updateData.description,
        category: updateData.category,
        sku: updateData.sku,
        barcode: updateData.barcode,
        unit_price: parseFloat(updateData.price),
        cost_price: parseFloat(updateData.costPrice),
        current_stock: parseInt(updateData.stock),
        min_stock_level: parseInt(updateData.minStock || 50),
        max_stock_level: parseInt(updateData.maxStock || 1000),
        storage_location: updateData.storageLocation || ''
      };

      // Send update to backend
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api'}/products/${productId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(backendUpdateData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update product');
      }

      const result = await response.json();
      console.log('✅ Product updated in backend:', result);

      // Refresh products data from backend to ensure synchronization
      await fetchProducts();

      // Create inventory transaction for significant changes
      const originalProduct = products.find(p => p.id === productId);
      if (originalProduct) {
        const stockChanged = originalProduct.stock !== updateData.stock;
        const priceChanged = originalProduct.price !== updateData.price;

        if (stockChanged || priceChanged) {
          const changeType = stockChanged && priceChanged ? 'Price and stock updated' :
                           stockChanged ? 'Stock updated' : 'Price updated';

          console.log(`📦 Product changes detected: ${changeType}`);
          console.log(`📦 Note: Backend will handle transaction creation automatically`);

          // Note: Inventory transactions are now handled by the backend
          // when using ApiDataContext. This prevents duplicate transaction creation.
        }
      }

      return result.data;
    } catch (error) {
      console.error('❌ Failed to update product:', error);
      throw error;
    }
  };

  const updateProductStock = async (productId, newStock, transactionType = 'adjustment', additionalData = {}) => {
    const product = products.find(p => p.id === productId);
    if (!product) return;

    const oldStock = product.stock;
    const stockDifference = newStock - oldStock;

    setProducts(prev => prev.map(p =>
      p.id === productId
        ? { ...p, stock: newStock, lastRestocked: new Date().toISOString().split('T')[0] }
        : p
    ));

    // Note: Inventory transactions for stock changes are now handled by the backend
    // through the ApiDataContext to prevent duplicate transaction creation.
    if (stockDifference !== 0) {
      console.log(`📦 Stock change detected: ${oldStock} → ${newStock} (${stockDifference > 0 ? '+' : ''}${stockDifference})`);
      console.log(`📦 Note: Use ApiDataContext.updateProductStock for backend transaction creation`);
    }
  };

  // Customer Management with Backend Integration
  const addCustomer = async (customerData) => {
    try {
      // Get auth token
      const token = localStorage.getItem('auth_token');
      console.log('🔑 Auth token check:', token ? 'Found' : 'Not found');
      console.log('🔑 Token value:', token);
      if (!token) {
        throw new Error('No authentication token found. Please log in again.');
      }

      // Prepare customer data for backend
      const backendCustomerData = {
        name: customerData.name,
        type: customerData.type || 'individual',
        contact: customerData.contact || '',
        phone: customerData.phone || '',
        email: customerData.email || '',
        address: customerData.address || '',
        credit_limit: parseFloat(customerData.creditLimit || 0),
        current_balance: 0.00,
        payment_terms: customerData.paymentTerms || 'cash',
        payment_method: customerData.paymentMethod || 'cash',
        status: 'active',
        salesman_id: customerData.salesmanId || null
      };

      console.log('🚀 Adding customer to backend:', backendCustomerData);

      // Send to backend
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api'}/customers`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(backendCustomerData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to add customer');
      }

      const result = await response.json();
      console.log('✅ Customer added to backend:', result);

      // Update local state with backend response
      const newCustomer = {
        id: result.data.customer_id,
        name: result.data.name,
        type: result.data.type,
        contact: result.data.contact,
        phone: result.data.phone,
        email: result.data.email,
        address: result.data.address,
        currentBalance: result.data.current_balance,
        status: result.data.status,
        registrationDate: new Date().toISOString().split('T')[0],
        lastOrderDate: null,
        totalOrders: 0,
        totalSpent: 0.00,
        averageOrderValue: 0.00
      };

      // Refresh customers data from backend to ensure synchronization
      await fetchCustomers();
      return newCustomer;
    } catch (error) {
      console.error('❌ Failed to add customer:', error);
      throw error;
    }
  };

  // Order Management
  const addOrder = (orderData) => {
    const orderNumber = `ORD-${new Date().getFullYear()}-${String(nextOrderId).padStart(3, '0')}`;
    const newOrder = {
      id: nextOrderId,
      orderNumber,
      ...orderData,
      status: orderData.status || 'pending',
      paymentStatus: orderData.paymentStatus || 'pending',
      paidAmount: orderData.paidAmount || 0.00
    };

    setOrders(prev => [...prev, newOrder]);
    setNextOrderId(prev => prev + 1);

    // Update customer statistics
    setCustomers(prev => prev.map(customer => {
      if (customer.id === orderData.customerId) {
        const newTotalOrders = customer.totalOrders + 1;
        const newTotalSpent = customer.totalSpent + orderData.totalAmount;
        return {
          ...customer,
          lastOrderDate: orderData.date,
          totalOrders: newTotalOrders,
          totalSpent: newTotalSpent,
          averageOrderValue: newTotalSpent / newTotalOrders
        };
      }
      return customer;
    }));

    // Update product stock if order is delivered
    if (orderData.status === 'delivered') {
      orderData.items.forEach(item => {
        updateProductStock(item.productId,
          products.find(p => p.id === item.productId)?.stock - item.quantity || 0
        );
      });
    }

    return newOrder;
  };

  // Update order payment status with backend integration
  const updateOrderPaymentStatus = async (orderId, paymentStatus, paidAmount) => {
    try {
      const token = getAuthToken();
      if (!token) {
        console.warn('⚠️ No auth token, updating payment status in local state only');
        // Fallback to local state update
        setOrders(prev => prev.map(order => {
          if (order.id === orderId) {
            return {
              ...order,
              paymentStatus,
              paidAmount: paidAmount !== undefined ? paidAmount : order.paidAmount
            };
          }
          return order;
        }));
        return;
      }

      console.log('💳 Updating order payment status:', { orderId, paymentStatus, paidAmount });

      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api'}/orders/${orderId}/payment-status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          payment_status: paymentStatus,
          paid_amount: paidAmount
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to update payment status: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ Payment status updated:', result);

      // Refresh orders data from backend to get updated data
      await fetchOrders();

      return result.data;
    } catch (error) {
      console.error('❌ Failed to update payment status:', error);

      // Fallback to local state update on error
      setOrders(prev => prev.map(order => {
        if (order.id === orderId) {
          return {
            ...order,
            paymentStatus,
            paidAmount: paidAmount !== undefined ? paidAmount : order.paidAmount
          };
        }
        return order;
      }));

      throw error; // Re-throw to let the UI handle the error
    }
  };

  // Update order status with backend integration
  const updateOrderStatus = async (orderId, status) => {
    try {
      const token = getAuthToken();
      if (!token) {
        console.warn('⚠️ No auth token, updating order status in local state only');
        // Fallback to local state update (backend handles inventory automatically)
        setOrders(prev => prev.map(order => {
          if (order.id === orderId) {
            return { ...order, status };
          }
          return order;
        }));
        return;
      }

      console.log('📋 Updating order status:', { orderId, status });

      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api'}/orders/${orderId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ status })
      });

      if (!response.ok) {
        throw new Error(`Failed to update order status: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ Order status updated:', result);

      // Refresh orders data from backend to get updated data
      await fetchOrders();

      // Also refresh products data as stock may have changed
      await fetchProducts();

      // ENHANCEMENT: Log payment reset for cancelled orders
      if (status === 'cancelled') {
        console.log('🚫 Order cancelled - payment information has been reset by backend');
      }

      return result.data;
    } catch (error) {
      console.error('❌ Failed to update order status:', error);

      // Fallback to local state update on error (backend handles inventory automatically)
      setOrders(prev => prev.map(order => {
        if (order.id === orderId) {
          return { ...order, status };
        }
        return order;
      }));

      throw error; // Re-throw to let the UI handle the error
    }
  };

  // Payment management functions
  const addPayment = (paymentData) => {
    const newPayment = {
      id: nextPaymentId,
      ...paymentData,
      status: 'completed'
    };

    setPayments(prev => [...prev, newPayment]);
    setNextPaymentId(prev => prev + 1);

    // Update order payment status
    setOrders(prev => prev.map(order => {
      if (order.id === paymentData.orderId) {
        const newPaidAmount = order.paidAmount + paymentData.amount;
        const newPaymentStatus = newPaidAmount >= order.totalAmount ? 'paid' : 'partial';

        return {
          ...order,
          paidAmount: newPaidAmount,
          paymentStatus: newPaymentStatus
        };
      }
      return order;
    }));

    return newPayment;
  };

  // Expense Management
  const addExpense = (expenseData) => {
    const newExpense = {
      id: nextExpenseId,
      ...expenseData,
      approvedBy: 'Admin'
    };
    setExpenses(prev => [...prev, newExpense]);
    setNextExpenseId(prev => prev + 1);
    return newExpense;
  };

  // Supplier Management with Backend Integration - CRASH-PROOF VERSION
  const addSupplier = async (supplierName) => {
    try {
      const token = getAuthToken();
      if (!token) {
        console.warn('⚠️ No auth token, adding supplier to local state only');
        // Fallback to local state if no token
        const supplierId = `supplier-${nextSupplierId}`;
        const newSupplier = {
          id: supplierId,
          name: supplierName.trim(),
          isActive: true
        };
        setSuppliers(prev => [...prev, newSupplier]);
        setNextSupplierId(prev => prev + 1);
        return newSupplier;
      }

      console.log('🏭 Creating supplier with crash protection:', supplierName);

      // Add timeout to prevent hanging requests
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      try {
        const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api'}/products/suppliers`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({
            name: supplierName.trim(),
            is_active: true
          }),
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        console.log('✅ Supplier creation response:', result);

        // Handle both new creation and existing supplier cases
        const supplierData = result.data;
        const newSupplier = {
          id: supplierData.supplier_id,
          name: supplierData.name,
          contactPerson: supplierData.contact_person,
          phone: supplierData.phone,
          email: supplierData.email,
          address: supplierData.address,
          paymentTerms: supplierData.payment_terms,
          isActive: supplierData.is_active
        };

        // Update local state immediately for better UX
        setSuppliers(prev => {
          const existingIndex = prev.findIndex(s => s.id === newSupplier.id);
          if (existingIndex >= 0) {
            // Update existing supplier
            const updated = [...prev];
            updated[existingIndex] = newSupplier;
            return updated;
          } else {
            // Add new supplier
            return [...prev, newSupplier];
          }
        });

        // Refresh suppliers data from backend in background
        fetchSuppliers().catch(error => {
          console.warn('⚠️ Background supplier refresh failed:', error);
        });

        const message = result.existing
          ? `✅ Supplier '${supplierData.name}' already exists and is ready to use!`
          : `✅ New supplier '${supplierData.name}' has been created successfully!`;

        console.log(message);
        return newSupplier;

      } catch (fetchError) {
        clearTimeout(timeoutId);
        throw fetchError;
      }

    } catch (error) {
      console.error('❌ Failed to create supplier:', error);

      // Enhanced fallback with better error handling
      if (error.name === 'AbortError') {
        console.warn('⚠️ Supplier creation timed out, using local fallback');
      } else if (error.message.includes('Failed to fetch')) {
        console.warn('⚠️ Network error during supplier creation, using local fallback');
      }

      // Fallback to local state on any error
      const supplierId = `supplier-${nextSupplierId}`;
      const newSupplier = {
        id: supplierId,
        name: supplierName.trim(),
        isActive: true,
        isLocal: true // Mark as local-only for debugging
      };

      setSuppliers(prev => [...prev, newSupplier]);
      setNextSupplierId(prev => prev + 1);

      console.log(`✅ New supplier '${supplierName.trim()}' has been created successfully! (Local fallback)`);
      return newSupplier;
    }
  };

  // Helper function to find or create supplier by name - CRASH-PROOF VERSION
  const findOrCreateSupplier = async (supplierName) => {
    try {
      if (!supplierName || supplierName.trim() === '') {
        console.warn('⚠️ Empty supplier name provided to findOrCreateSupplier');
        return null;
      }

      const trimmedName = supplierName.trim();
      console.log(`🔍 Finding or creating supplier: ${trimmedName}`);

      // First, try to find existing supplier (case-insensitive)
      const existingSupplier = suppliers.find(s =>
        s.name.toLowerCase() === trimmedName.toLowerCase()
      );

      if (existingSupplier) {
        console.log(`✅ Found existing supplier: ${existingSupplier.name} (ID: ${existingSupplier.id})`);
        return existingSupplier;
      }

      // If not found, create new supplier with error protection
      console.log(`🏭 Supplier not found, creating new: ${trimmedName}`);

      try {
        const newSupplier = await addSupplier(trimmedName);
        console.log(`✅ Successfully created/found supplier: ${newSupplier.name} (ID: ${newSupplier.id})`);
        return newSupplier;
      } catch (supplierError) {
        console.error('❌ Failed to create supplier in findOrCreateSupplier:', supplierError);

        // Return a temporary supplier object to prevent crashes
        const tempSupplier = {
          id: `temp-${Date.now()}`,
          name: trimmedName,
          isActive: true,
          isTemporary: true
        };

        console.log(`⚠️ Using temporary supplier: ${tempSupplier.name} (ID: ${tempSupplier.id})`);
        return tempSupplier;
      }

    } catch (error) {
      console.error('❌ Critical error in findOrCreateSupplier:', error);

      // Return null to prevent further crashes
      return null;
    }
  };

  // Category Management with Backend Integration
  const addCategory = async (categoryName) => {
    try {
      const token = getAuthToken();
      if (!token) {
        console.warn('⚠️ No auth token, adding category to local state only');
        // Fallback to local state if no token
        const categoryId = categoryName.toLowerCase().replace(/\s+/g, '-');
        const newCategory = {
          id: categoryId,
          name: categoryName.trim()
        };
        setCategories(prev => [...prev, newCategory]);
        return newCategory;
      }

      console.log('📂 Creating category:', categoryName);

      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3002/api'}/products/categories`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          name: categoryName.trim()
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to create category: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ Category created:', result);

      // Refresh categories from backend to ensure synchronization
      await fetchCategories();

      // Return the created category
      const newCategory = {
        id: result.data.category_id,
        name: result.data.name
      };

      return newCategory;
    } catch (error) {
      console.error('❌ Failed to create category:', error);

      // Fallback to local state on error
      const categoryId = categoryName.toLowerCase().replace(/\s+/g, '-');
      const newCategory = {
        id: categoryId,
        name: categoryName.trim()
      };
      setCategories(prev => [...prev, newCategory]);
      return newCategory;
    }
  };

  // ============================================================================
  // EMPLOYEE MANAGEMENT CRUD FUNCTIONS
  // ============================================================================

  // Add new employee
  const addEmployee = async (employeeData) => {
    try {
      const token = getAuthToken();
      if (!token) {
        throw new Error('No authentication token found. Please log in again.');
      }

      console.log('👤 Creating new employee:', employeeData);

      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api'}/employees`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(employeeData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create employee');
      }

      const result = await response.json();
      console.log('✅ Employee created:', result);

      // Refresh employees data from backend
      await fetchEmployees();

      return result.data;
    } catch (error) {
      console.error('❌ Failed to create employee:', error);
      throw error;
    }
  };

  // Assign salary to employee
  const assignSalary = async (salaryData) => {
    try {
      const token = getAuthToken();
      if (!token) {
        throw new Error('No authentication token found. Please log in again.');
      }

      console.log('💰 Assigning salary:', salaryData);

      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api'}/employees/salary-assignments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(salaryData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to assign salary');
      }

      const result = await response.json();
      console.log('✅ Salary assigned:', result);

      // Refresh employees and salary data
      await Promise.all([
        fetchEmployees(),
        fetchSalaryPayments()
      ]);

      return result.data;
    } catch (error) {
      console.error('❌ Failed to assign salary:', error);
      throw error;
    }
  };

  // Process salary payments
  const processSalaryPayments = async (paymentRequests) => {
    try {
      const token = getAuthToken();
      if (!token) {
        throw new Error('No authentication token found. Please log in again.');
      }

      console.log('💳 Processing salary payments:', paymentRequests.length, 'payments');

      const results = [];
      for (const paymentRequest of paymentRequests) {
        const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api'}/employees/salary-payments`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify(paymentRequest)
        });

        if (!response.ok) {
          const errorData = await response.json();
          console.error('❌ Failed to process payment for employee:', paymentRequest.employee_id, errorData);
          continue;
        }

        const result = await response.json();
        results.push(result.data);
      }

      console.log('✅ Processed', results.length, 'salary payments');

      // Refresh salary payments data
      await fetchSalaryPayments();

      return results;
    } catch (error) {
      console.error('❌ Failed to process salary payments:', error);
      throw error;
    }
  };

  // Refresh employee data
  const refreshEmployees = async () => {
    console.log('🔄 Refreshing employees...');
    try {
      await Promise.all([
        fetchEmployees(),
        fetchSalaryPayments()
      ]);
      console.log('✅ Employee data refreshed');
    } catch (error) {
      console.error('❌ Failed to refresh employee data:', error);
      throw error;
    }
  };

  const value = {
    // Core data
    products,
    setProducts,
    customers,
    setCustomers,
    salesmen,
    setSalesmen,
    orders,
    setOrders,
    payments,
    setPayments,
    expenses,
    setExpenses,

    // Computed values
    getTotalRevenue: () => totalRevenue,
    getTotalExpenses: () => totalExpenses,
    getProfit: () => profit,
    getTotalProfit,
    getPendingPayments,
    getInventoryValue,
    getRecentOrders,
    getLowStockProducts: () => lowStockProducts,
    getCustomerBalance,
    getCustomerTotalPurchased,
    getCustomerPendingAmount,
    getCustomerTotalOrders, // FIXED: Add total orders count function
    getCustomerOrderHistory, // FIXED: Add order history function
    getSalesmanPerformance: () => salesmanPerformance,
    // ENHANCEMENT: Company Losses
    getCompanyLosses: () => companyLosses,

    // Actions
    addPayment,
    addProduct,
    updateProduct,
    addCustomer,
    addOrder,
    updateOrderStatus,
    updateOrderPaymentStatus,
    updateProductStock,
    addExpense,
    addInventoryTransaction,
    addSupplier,
    addCategory,
    findOrCreateSupplier,
    inventoryTransactions,

    // Dynamic data
    suppliers,
    categories,

    // Data fetching functions
    fetchProducts,
    fetchSuppliers,
    fetchCustomers,
    fetchOrders,
    fetchPayments,
    fetchExpenses,
    fetchInventoryTransactions,
    fetchSalesmen,
    fetchCategories,
    fetchEmployees,
    fetchSalaryPayments,
    loadInitialData,

    // Refresh functions
    refreshAllData,
    refreshProducts,
    refreshOrders,
    refreshCustomers,
    refreshInventory,
    refreshEmployees,

    // ENHANCEMENT: Employee Management System
    employees,
    salaryPayments,
    addEmployee,
    assignSalary,
    processSalaryPayments,

    // Loading states
    loading,
    initialDataLoaded,
    dataLoadingError,

    // Legacy compatibility (for existing components)
    customersData: customers,
    expenseHistory: expenses,
    salesData: [
      { month: 'Oct', revenue: 42000 },
      { month: 'Nov', revenue: 45000 },
      { month: 'Dec', revenue: totalRevenue }
    ],
    inventoryData: [
      { category: 'Beverages', stock: products.filter(p => p.category === 'Beverages').reduce((sum, p) => sum + p.stock, 0) },
      { category: 'Snacks', stock: products.filter(p => p.category === 'Snacks').reduce((sum, p) => sum + p.stock, 0) },
      { category: 'Water', stock: products.filter(p => p.category === 'Water').reduce((sum, p) => sum + p.stock, 0) }
    ],
    expensesData: expenses,
    salesmenPerformance: salesmanPerformance,

    // Additional data for specific components
    invoicesData: orders.map(order => {
      const customer = customers.find(c => c.id === order.customerId);

      // FIXED: Apply cancelled order payment status logic for invoices
      const isCancelledOrder = order.status === 'cancelled';
      const actualPaymentStatus = isCancelledOrder ? 'cancelled' : order.paymentStatus;
      const invoiceStatus = actualPaymentStatus === 'paid' ? 'paid' :
                           actualPaymentStatus === 'partial' ? 'pending' :
                           actualPaymentStatus === 'cancelled' ? 'cancelled' : 'pending';

      return {
        id: `INV-${order.orderNumber.split('-')[2]}`,
        customer: customer ? customer.name : 'Unknown Customer',
        amount: order.totalAmount,
        date: order.date,
        dueDate: order.dueDate,
        status: invoiceStatus,
        items: order.items ? order.items.map(item => ({
          description: item.productName || 'Unknown Product',
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          total: item.totalPrice
        })) : []
      };
    }),

    customerOptions: [
      { value: '', label: 'All Customers' },
      ...customers.map(customer => ({ value: customer.id, label: customer.name }))
    ],

    statusOptions: [
      { value: '', label: 'All Statuses' },
      { value: 'pending', label: 'Pending' },
      { value: 'paid', label: 'Paid' },
      { value: 'overdue', label: 'Overdue' },
      { value: 'cancelled', label: 'Cancelled' }
    ],

    // ENHANCEMENT: Use real employees data with proper database integration
    employeesData: (() => {
      // Combine both employees and salesmen data sources
      const allEmployees = [];

      // Add manually created employees from employees table
      if (employees.length > 0) {
        employees.forEach(employee => {
          const currentSalaryAssignment = employee.currentSalaryAssignment;
          const latestPayment = salaryPayments.find(p => p.employeeId === employee.id);

          // Calculate salary components from database
          const basicSalary = currentSalaryAssignment?.base_salary ? parseFloat(currentSalaryAssignment.base_salary) : 0;
          const allowancesTotal = currentSalaryAssignment?.allowances ?
            Object.values(currentSalaryAssignment.allowances).reduce((sum, val) => sum + parseFloat(val || 0), 0) : 0;
          const bonusesTotal = currentSalaryAssignment?.bonuses ?
            Object.values(currentSalaryAssignment.bonuses).reduce((sum, val) => sum + parseFloat(val || 0), 0) : 0;
          const deductionsTotal = currentSalaryAssignment?.deductions ?
            Object.values(currentSalaryAssignment.deductions).reduce((sum, val) => sum + parseFloat(val || 0), 0) : 0;

          const grossSalary = basicSalary + allowancesTotal + bonusesTotal;
          const netSalary = grossSalary - deductionsTotal;

          allEmployees.push({
            id: employee.id,
            employeeNumber: employee.employeeNumber,
            name: employee.name,
            email: employee.email,
            phone: employee.phone,
            position: employee.position,
            department: employee.department,
            hireDate: employee.hireDate,
            status: employee.status,
            employeeType: employee.employeeType,
            basicSalary: basicSalary,
            commission: 0, // Employees don't have commission, only salesmen do
            totalSalary: netSalary,
            // Use actual bank details from database
            bankAccount: employee.bankDetails?.account_number || 'Not Set',
            bankName: employee.bankDetails?.bank_name || 'Not Set',
            paymentMethod: currentSalaryAssignment?.payment_method || 'bank_transfer',
            // Use actual attendance data from latest payment record
            attendance: {
              present: latestPayment?.presentDays || 0,
              absent: latestPayment?.absentDays || 0,
              leave: latestPayment?.leaveDays || 0,
              late: 0 // Not tracked in current schema
            },
            // Use actual deductions from salary assignment
            deductions: currentSalaryAssignment?.deductions || {
              tax: 0,
              insurance: 0,
              other: 0
            },
            address: employee.address,
            emergencyContact: employee.emergencyContact,
            notes: employee.notes,
            // Additional fields for salary management
            grossSalary: grossSalary,
            netSalary: netSalary,
            allowances: currentSalaryAssignment?.allowances || {},
            bonuses: currentSalaryAssignment?.bonuses || {},
            payFrequency: currentSalaryAssignment?.pay_frequency || 'monthly',
            overtimeRate: currentSalaryAssignment?.overtime_rate || 1.5
          });
        });
      }

      // Add salesmen as employees (for backward compatibility)
      salesmen.forEach(salesman => {
        // Calculate salesman salary data from database
        const basicSalary = salesman.baseSalary || salesman.base_salary || 0;
        const totalCommission = salesman.totalCommission || salesman.total_commission || 0;
        const monthlyCommission = totalCommission / 12;
        const totalSalary = basicSalary + monthlyCommission;

        allEmployees.push({
          id: `salesman_${salesman.id}`, // Prefix to distinguish from employees
          employeeNumber: `SALES-${salesman.id}`,
          name: salesman.name || 'Unknown Salesman',
          email: salesman.email || '',
          phone: salesman.phone || '',
          position: salesman.specialization || salesman.position || 'Sales Representative',
          department: 'Sales',
          hireDate: salesman.hireDate || '',
          status: salesman.status || 'active',
          employeeType: 'full_time',
          basicSalary: basicSalary,
          commission: monthlyCommission,
          totalSalary: totalSalary,
          // Use actual bank details if available, otherwise indicate not set
          bankAccount: salesman.bankDetails?.account_number || 'Not Set',
          bankName: salesman.bankDetails?.bank_name || 'Not Set',
          paymentMethod: 'bank_transfer',
          // For salesmen, we don't have attendance data in current schema
          attendance: {
            present: 0,
            absent: 0,
            leave: 0,
            late: 0
          },
          // Calculate deductions based on actual salary
          deductions: {
            tax: totalSalary * 0.1, // 10% tax rate
            insurance: 0,
            other: 0
          },
          address: salesman.address || {},
          emergencyContact: salesman.emergencyContact || {},
          notes: salesman.notes || '',
          // Additional fields
          grossSalary: totalSalary,
          netSalary: totalSalary * 0.9, // After 10% tax
          allowances: {},
          bonuses: { commission: monthlyCommission },
          payFrequency: 'monthly',
          overtimeRate: 1.5
        });
      });

      return allEmployees;
    })(),

    months: [
      { value: 1, label: 'January' }, { value: 2, label: 'February' }, { value: 3, label: 'March' },
      { value: 4, label: 'April' }, { value: 5, label: 'May' }, { value: 6, label: 'June' },
      { value: 7, label: 'July' }, { value: 8, label: 'August' }, { value: 9, label: 'September' },
      { value: 10, label: 'October' }, { value: 11, label: 'November' }, { value: 12, label: 'December' }
    ],

    years: [
      { value: 2024, label: '2024' },
      { value: 2023, label: '2023' },
      { value: 2022, label: '2022' },
      { value: 2021, label: '2021' }
    ],

    // ENHANCEMENT: Real commission stages from backend
    commissionStages: commissionStagesData || [
      { stage_number: 1, stage_name: 'Bronze', min_sales_amount: 0, max_sales_amount: 10000, commission_percentage: 0.025, bonus_amount: 100 },
      { stage_number: 2, stage_name: 'Silver', min_sales_amount: 10001, max_sales_amount: 25000, commission_percentage: 0.03, bonus_amount: 200 },
      { stage_number: 3, stage_name: 'Gold', min_sales_amount: 25001, max_sales_amount: null, commission_percentage: 0.05, bonus_amount: 500 }
    ],

    salesmenCommissionData: salesmen.map(salesman => {
      // DEFENSIVE PROGRAMMING: Handle undefined values with fallbacks
      const salesmanOrders = orders.filter(order => order.salesmanId === salesman.id) || [];
      const bottlesSold = salesmanOrders.reduce((sum, order) => {
        if (!order.items || !Array.isArray(order.items)) return sum;
        return sum + order.items.reduce((itemSum, item) => {
          // Check if product is beverage by productId (1-4 are beverages in our data)
          const productId = item.productId || 0;
          const quantity = item.quantity || 0;
          return (productId >= 1 && productId <= 4) ? itemSum + quantity : itemSum;
        }, 0);
      }, 0);

      const monthlyTarget = salesman.monthlyTarget || 4000; // Default $4000 target
      const totalCommission = salesman.totalCommission || 0;
      const monthlyCommission = totalCommission / 12;

      return {
        id: salesman.id || 0,
        name: salesman.name || 'Unknown Salesman',
        currentStage: bottlesSold >= 3500 ? 3 : bottlesSold >= 2000 ? 2 : 1,
        bottlesSold: bottlesSold,
        target: monthlyTarget / 2, // Bottle target (assuming average $2 per bottle)
        progress: Math.min((bottlesSold / (monthlyTarget / 2)) * 100, 100),
        commission: monthlyCommission,
        status: salesman.status || 'active',
        history: [
          { month: 'October', bottles: Math.floor(bottlesSold * 0.3), commission: monthlyCommission * 0.3 },
          { month: 'November', bottles: Math.floor(bottlesSold * 0.35), commission: monthlyCommission * 0.35 },
          { month: 'December', bottles: Math.floor(bottlesSold * 0.35), commission: monthlyCommission * 0.35 }
        ]
      };
    }),

    // Dynamic options for forms
    supplierOptions: [
      { value: '', label: 'Select supplier' },
      ...suppliers.map(supplier => ({ value: supplier.id, label: supplier.name }))
    ],
    categoryOptions: [
      { value: '', label: 'Select category' },
      ...categories.map(category => ({ value: category.name, label: category.name }))
    ],
    recentDeliveries: inventoryTransactions
      .filter(t => t.type === 'in')
      .sort((a, b) => new Date(b.date) - new Date(a.date))
      .slice(0, 10)
      .map(t => ({
        date: t.date,
        supplier: t.supplier,
        product: t.productName,
        quantity: t.quantity,
        totalCost: t.totalCost,
        status: t.status === 'completed' ? 'Received' : 'Pending'
      }))
  };

  return <DataContext.Provider value={value}>{children}</DataContext.Provider>;
};

export const useData = () => {
  const context = useContext(DataContext);
  if (context === undefined) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};
