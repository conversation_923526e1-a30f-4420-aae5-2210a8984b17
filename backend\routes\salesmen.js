const express = require('express');
const router = express.Router();
const {
  get<PERSON><PERSON><PERSON>,
  get<PERSON><PERSON><PERSON>,
  create<PERSON><PERSON><PERSON>,
  update<PERSON><PERSON><PERSON>,
  delete<PERSON><PERSON><PERSON>
} = require('../controllers/salesmanController');
const { validateUUID } = require('../middleware/validation');
const { authenticateToken, adminOnly, adminOrSalesman } = require('../middleware/auth');
const { body } = require('express-validator');

// All routes require authentication
router.use(authenticateToken);

// Validation middleware for salesman creation/update
const validateSalesman = [
  body('full_name')
    .notEmpty()
    .withMessage('Full name is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('Full name must be between 2 and 100 characters'),
  body('user_id')
    .optional()
    .isUUID()
    .withMessage('User ID must be a valid UUID'),
  body('commission_rate')
    .optional()
    .isFloat({ min: 0, max: 1 })
    .withMessage('Commission rate must be between 0 and 1'),
  body('monthly_target')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Monthly target must be a positive number'),
  body('contact_info')
    .optional()
    .isObject()
    .withMessage('Contact info must be an object')
];

// GET routes (accessible by admin and salesman)
router.get('/', adminOrSalesman, getSalesmen);
router.get('/:id', adminOrSalesman, validateUUID('id'), getSalesman);

// POST/PUT/DELETE routes (admin only)
router.post('/', adminOnly, validateSalesman, createSalesman);
router.put('/:id', adminOnly, validateUUID('id'), validateSalesman, updateSalesman);
router.delete('/:id', adminOnly, validateUUID('id'), deleteSalesman);

module.exports = router;
