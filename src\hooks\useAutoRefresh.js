import { useEffect, useRef, useState } from 'react';

/**
 * Custom hook for auto-refresh functionality
 * Provides intelligent auto-refresh with user interaction detection
 */
const useAutoRefresh = (refreshFunction, options = {}) => {
  const {
    interval = 3000, // 3 seconds default
    enabled = true,
    pauseOnInteraction = true,
    interactionTimeout = 5000 // 5 seconds after interaction
  } = options;

  const [isAutoRefreshEnabled, setIsAutoRefreshEnabled] = useState(enabled);
  const [isPaused, setIsPaused] = useState(false);
  const [lastRefresh, setLastRefresh] = useState(new Date());
  const intervalRef = useRef(null);
  const interactionTimeoutRef = useRef(null);
  const isUserInteractingRef = useRef(false);

  // Track user interactions
  const handleUserInteraction = () => {
    if (!pauseOnInteraction) return;

    isUserInteractingRef.current = true;
    setIsPaused(true);

    // Clear existing timeout
    if (interactionTimeoutRef.current) {
      clearTimeout(interactionTimeoutRef.current);
    }

    // Resume auto-refresh after interaction timeout
    interactionTimeoutRef.current = setTimeout(() => {
      isUserInteractingRef.current = false;
      setIsPaused(false);
    }, interactionTimeout);
  };

  // Setup interaction listeners
  useEffect(() => {
    if (!pauseOnInteraction) return;

    const events = ['mousedown', 'keydown', 'scroll', 'touchstart'];
    
    events.forEach(event => {
      document.addEventListener(event, handleUserInteraction, { passive: true });
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleUserInteraction);
      });
    };
  }, [pauseOnInteraction, interactionTimeout]);

  // Auto-refresh logic
  useEffect(() => {
    if (!isAutoRefreshEnabled || isPaused) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      return;
    }

    const performRefresh = async () => {
      try {
        console.log('🔄 Auto-refresh triggered...');
        await refreshFunction();
        setLastRefresh(new Date());
        console.log('✅ Auto-refresh completed');
      } catch (error) {
        console.error('❌ Auto-refresh failed:', error);
        // Don't disable auto-refresh on error, just log it
      }
    };

    // Start interval
    intervalRef.current = setInterval(performRefresh, interval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [isAutoRefreshEnabled, isPaused, interval, refreshFunction]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      if (interactionTimeoutRef.current) {
        clearTimeout(interactionTimeoutRef.current);
      }
    };
  }, []);

  // Manual refresh function
  const manualRefresh = async () => {
    try {
      console.log('🔄 Manual refresh triggered...');
      await refreshFunction();
      setLastRefresh(new Date());
      console.log('✅ Manual refresh completed');
    } catch (error) {
      console.error('❌ Manual refresh failed:', error);
      throw error; // Re-throw for error handling in components
    }
  };

  // Toggle auto-refresh
  const toggleAutoRefresh = () => {
    setIsAutoRefreshEnabled(prev => !prev);
  };

  return {
    isAutoRefreshEnabled,
    isPaused,
    lastRefresh,
    toggleAutoRefresh,
    manualRefresh,
    setAutoRefreshEnabled: setIsAutoRefreshEnabled
  };
};

export default useAutoRefresh;
