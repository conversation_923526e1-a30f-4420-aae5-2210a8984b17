const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const CommissionStage = sequelize.define('CommissionStage', {
  stage_id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  stage_number: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  stage_name: {
    type: DataTypes.STRING,
    allowNull: false
  },
  min_sales_amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0
  },
  max_sales_amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true // NULL means no upper limit
  },
  commission_percentage: {
    type: DataTypes.DECIMAL(5, 4),
    allowNull: false,
    validate: {
      min: 0,
      max: 1 // 0-100% represented as 0-1
    }
  },
  bonus_amount: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0
  },
  calculation_period: {
    type: DataTypes.ENUM('daily', 'weekly', 'monthly', 'quarterly', 'annually'),
    defaultValue: 'monthly'
  },
  eligible_categories: {
    type: DataTypes.JSONB,
    defaultValue: [] // Empty array means all categories eligible
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  effective_date: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  end_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  salesman_id: {
    type: DataTypes.UUID,
    allowNull: true, // Null means global stage
    references: {
      model: 'salesmen',
      key: 'salesman_id'
    }
  },
  created_by: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'user_id'
    }
  },
  updated_by: {
    type: DataTypes.UUID,
    references: {
      model: 'users',
      key: 'user_id'
    }
  },
  notes: {
    type: DataTypes.TEXT
  }
}, {
  tableName: 'commission_stages',
  indexes: [
    {
      unique: true,
      fields: ['stage_number', 'salesman_id']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['effective_date']
    },
    {
      fields: ['min_sales_amount', 'max_sales_amount']
    }
  ]
});

// Removed CommissionStageOverride definition and association

module.exports = CommissionStage;
