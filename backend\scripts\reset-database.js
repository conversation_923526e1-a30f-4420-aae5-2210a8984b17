const { sequelize } = require('../config/database');
const bcrypt = require('bcryptjs');
const chalk = require('chalk');

// Import all models to ensure they're registered
const {
  User,
  Customer,
  Supplier,
  Category,
  Product,
  Order,
  OrderItem,
  InventoryTransaction,
  Payment,
  Expense,
  Employee,
  SalaryPayment,
  Salesman,
  CommissionStage,
  CommissionAssignment,
  CommissionHistory,
  CommissionStageOverride
} = require('../models');

async function resetDatabase() {
  try {
    console.log(chalk.blue('🔄 Starting database reset...'));

    // Test database connection
    await sequelize.authenticate();
    console.log(chalk.green('✅ Database connection established'));

    // Get all table names in dependency order (children first, parents last)
    const tablesToClear = [
      'order_items',
      'orders',
      'inventory_transactions',
      'products',
      'categories',
      'customers',
      'suppliers',
      'payments',
      'expenses',
      'salary_payments',
      'employees',
      'commission_assignments',
      'commission_history',
      'commission_stage_overrides',
      'commission_stages',
      'salesmen',
      'users'
    ];

    console.log(chalk.yellow('🗑️  Clearing all data from tables...'));

    // Disable foreign key constraints temporarily
    await sequelize.query('PRAGMA foreign_keys = OFF;');
    console.log(chalk.gray('   Disabled foreign key constraints'));

    // Clear all tables in order
    for (const tableName of tablesToClear) {
      try {
        await sequelize.query(`DELETE FROM ${tableName};`);
        await sequelize.query(`DELETE FROM sqlite_sequence WHERE name='${tableName}';`);
        console.log(chalk.gray(`   ✓ Cleared ${tableName}`));
      } catch (error) {
        console.log(chalk.gray(`   ⚠ Skipped ${tableName} (table may not exist)`));
      }
    }

    // Re-enable foreign key constraints
    await sequelize.query('PRAGMA foreign_keys = ON;');
    console.log(chalk.gray('   Re-enabled foreign key constraints'));

    console.log(chalk.green('✅ All data cleared successfully'));

    // Create fresh admin user
    console.log(chalk.blue('👤 Creating fresh admin user...'));

    const adminPassword = 'admin123';
    // Let the model hook handle password hashing
    const adminUser = await User.create({
      email: '<EMAIL>',
      password_hash: adminPassword, // Will be hashed by model hook
      role: 'admin',
      is_active: true,
      email_verified: true
    });

    console.log(chalk.green('✅ Admin user created successfully'));
    console.log(chalk.cyan('📧 Email: <EMAIL>'));
    console.log(chalk.cyan('🔑 Password: admin123'));
    console.log(chalk.cyan('👑 Role: admin'));

    // Verify tables are empty
    console.log(chalk.blue('🔍 Verifying database state...'));

    const verificationQueries = [
      { name: 'Users', query: 'SELECT COUNT(*) as count FROM users' },
      { name: 'Customers', query: 'SELECT COUNT(*) as count FROM customers' },
      { name: 'Products', query: 'SELECT COUNT(*) as count FROM products' },
      { name: 'Orders', query: 'SELECT COUNT(*) as count FROM orders' },
      { name: 'Salesmen', query: 'SELECT COUNT(*) as count FROM salesmen' },
      { name: 'CommissionStages', query: 'SELECT COUNT(*) as count FROM commission_stages' },
      { name: 'CommissionStageOverrides', query: 'SELECT COUNT(*) as count FROM commission_stage_overrides' }
    ];

    for (const { name, query } of verificationQueries) {
      try {
        const [results] = await sequelize.query(query);
        const count = results[0].count;
        if (name === 'Users' && count === 1) {
          console.log(chalk.green(`   ✓ ${name}: ${count} record (admin user)`));
        } else if (count === 0) {
          console.log(chalk.green(`   ✓ ${name}: ${count} records (empty)`));
        } else {
          console.log(chalk.yellow(`   ⚠ ${name}: ${count} records (unexpected)`));
        }
      } catch (error) {
        console.log(chalk.gray(`   ⚠ ${name}: Could not verify (table may not exist)`));
      }
    }

    console.log(chalk.green('\n🎉 Database reset completed successfully!'));
    console.log(chalk.blue('\n📋 Summary:'));
    console.log(chalk.gray('   • All data cleared from all tables'));
    console.log(chalk.gray('   • Table schemas preserved'));
    console.log(chalk.gray('   • Fresh admin user created'));
    console.log(chalk.gray('   • Database ready for testing'));

    console.log(chalk.yellow('\n🔐 Login Credentials:'));
    console.log(chalk.cyan('   Email: <EMAIL>'));
    console.log(chalk.cyan('   Password: admin123'));

  } catch (error) {
    console.error(chalk.red('❌ Database reset failed:'), error);
    throw error;
  } finally {
    await sequelize.close();
    console.log(chalk.gray('🔌 Database connection closed'));
  }
}

// Run the reset if this script is executed directly
if (require.main === module) {
  resetDatabase()
    .then(() => {
      console.log(chalk.green('\n✅ Reset script completed successfully'));
      process.exit(0);
    })
    .catch((error) => {
      console.error(chalk.red('\n❌ Reset script failed:'), error);
      process.exit(1);
    });
}

module.exports = { resetDatabase };
