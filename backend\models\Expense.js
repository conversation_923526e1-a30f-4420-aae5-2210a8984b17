const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Expense = sequelize.define('Expense', {
  expense_id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  category: {
    type: DataTypes.STRING,
    allowNull: false
  },
  subcategory: {
    type: DataTypes.STRING
  },
  description: {
    type: DataTypes.STRING,
    allowNull: false
  },
  amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  expense_date: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  payment_method: {
    type: DataTypes.ENUM('cash', 'credit_card', 'bank_transfer', 'check'),
    defaultValue: 'cash'
  },
  receipt_number: {
    type: DataTypes.STRING
  },
  vendor: {
    type: DataTypes.STRING
  },
  is_recurring: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  recurring_frequency: {
    type: DataTypes.ENUM('daily', 'weekly', 'monthly', 'yearly')
  },
  status: {
    type: DataTypes.ENUM('pending', 'approved', 'paid', 'rejected'),
    defaultValue: 'approved'
  },
  notes: {
    type: DataTypes.TEXT
  },
  recorded_by: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'user_id'
    }
  }
}, {
  tableName: 'expenses',
  indexes: [
    {
      fields: ['category']
    },
    {
      fields: ['expense_date']
    },
    {
      fields: ['status']
    }
  ]
});

module.exports = Expense;
