import { useAuth } from '../contexts/AuthContext';

function AuthDebug() {
  const { user, isAuthenticated } = useAuth();
  
  const checkAuth = () => {
    const token = localStorage.getItem('auth_token');
    const allKeys = Object.keys(localStorage);
    
    console.log('🔍 Authentication Debug:');
    console.log('- User:', user);
    console.log('- Is Authenticated:', isAuthenticated);
    console.log('- Token in localStorage:', token);
    console.log('- All localStorage keys:', allKeys);
    
    // Show in UI as well
    alert(`
Authentication Debug:
- User: ${user ? user.email : 'Not logged in'}
- Is Authenticated: ${isAuthenticated}
- Token: ${token ? 'Found' : 'Not found'}
- All localStorage keys: ${allKeys.join(', ')}
    `);
  };
  
  return (
    <div className="p-4 bg-yellow-100 border border-yellow-400 rounded">
      <h3 className="font-bold text-yellow-800 mb-2">Authentication Debug</h3>
      <p className="text-sm text-yellow-700 mb-3">
        User: {user ? user.email : 'Not logged in'} | 
        Authenticated: {isAuthenticated ? 'Yes' : 'No'}
      </p>
      <button
        onClick={checkAuth}
        className="px-3 py-1 bg-yellow-600 text-white rounded text-sm hover:bg-yellow-700"
      >
        Check Auth Status
      </button>
    </div>
  );
}

export default AuthDebug;
