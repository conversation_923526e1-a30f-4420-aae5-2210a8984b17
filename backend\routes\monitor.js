const express = require('express');
const router = express.Router();
const {
  User,
  Salesman,
  Customer,
  Product,
  Order,
  OrderItem,
  Payment,
  Expense,
  InventoryTransaction,
  Supplier,
  Category
} = require('../models');
const chalk = require('chalk');
const moment = require('moment');

// ============================================================================
// PUBLIC MONITORING ENDPOINTS (NO AUTH REQUIRED)
// ============================================================================

// Test endpoint
router.get('/test', (req, res) => {
  console.log(chalk.green('📊 Monitor test endpoint called'));
  res.json({
    success: true,
    message: 'Monitor routes are working!',
    timestamp: new Date().toISOString()
  });
});

// Debug endpoint to check models
router.get('/debug/models', (req, res) => {
  console.log(chalk.blue('🔍 Debug models endpoint called'));

  const models = {
    users: User,
    customers: Customer,
    products: Product,
    orders: Order,
    order_items: OrderItem,
    payments: Payment,
    expenses: Expense,
    inventory_transactions: InventoryTransaction,
    salesmen: Salesman,
    suppliers: Supplier,
    categories: Category
  };

  const modelInfo = {};
  for (const [name, model] of Object.entries(models)) {
    modelInfo[name] = {
      exists: !!model,
      name: model?.name || 'undefined',
      tableName: model?.tableName || 'undefined'
    };
  }

  console.log('Model info:', modelInfo);
  res.json({
    success: true,
    models: modelInfo,
    timestamp: new Date().toISOString()
  });
});

// Specific test endpoint for categories
router.get('/test/categories', async (req, res) => {
  try {
    console.log(chalk.blue('🔍 Testing categories specifically...'));

    const count = await Category.count();
    const data = await Category.findAll({ limit: 5, raw: true });

    console.log(`📊 Categories count: ${count}`);
    console.log(`📊 Categories data:`, data);

    res.json({
      success: true,
      count,
      data,
      message: 'Categories test successful',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error(chalk.red('❌ Categories test error:'), error);
    res.status(500).json({
      success: false,
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Database statistics endpoint
router.get('/database/stats', async (req, res) => {
  try {
    console.log(chalk.blue(`\n📊 [${moment().format('YYYY-MM-DD HH:mm:ss.SSS')}] DATABASE STATS REQUEST`));

    const models = {
      users: User,
      customers: Customer,
      products: Product,
      orders: Order,
      order_items: OrderItem,
      payments: Payment,
      expenses: Expense,
      inventory_transactions: InventoryTransaction,
      salesmen: Salesman,
      suppliers: Supplier,
      categories: Category
    };

    const stats = {
      tables: {},
      health: { status: 'healthy', message: 'Database connection successful' },
      lastUpdated: {},
      dataIntegrity: {},
      timestamp: new Date().toISOString()
    };

    // Get record counts
    for (const [tableName, Model] of Object.entries(models)) {
      try {
        const count = await Model.count();
        stats.tables[tableName] = count;
        console.log(`✅ ${tableName}: ${count} records`);
      } catch (error) {
        console.error(`❌ Error counting ${tableName}:`, error.message);
        stats.tables[tableName] = 0;
      }
    }

    // Calculate total records
    stats.totalRecords = Object.values(stats.tables).reduce((sum, count) => sum + count, 0);

    console.log(chalk.green(`✅ Database stats generated successfully`));
    res.json({ success: true, data: stats });

  } catch (error) {
    console.error(chalk.red('❌ Database stats error:'), error);
    res.status(500).json({ success: false, message: error.message });
  }
});

// Direct categories data endpoint (using different path to avoid conflicts)
router.get('/categories-data', async (req, res) => {
  try {
    console.log(chalk.blue('🔍 Direct categories data endpoint called'));
    const { limit = 50 } = req.query;

    const data = await Category.findAll({
      limit: parseInt(limit),
      raw: true
    });

    console.log(`📊 Categories data fetched: ${data.length} records`);

    res.json({
      success: true,
      data: data,
      metadata: {
        table: 'categories',
        recordCount: data.length,
        columns: Object.keys(Category.rawAttributes)
      }
    });
  } catch (error) {
    console.error(chalk.red('❌ Direct categories error:'), error);
    res.status(500).json({ success: false, message: error.message });
  }
});

// Table data endpoint
router.get('/data/:table', async (req, res) => {
  try {
    const { table } = req.params;
    const { limit = 50 } = req.query;

    console.log(chalk.blue(`\n📋 [${moment().format('YYYY-MM-DD HH:mm:ss.SSS')}] TABLE DATA REQUEST:`));
    console.log(chalk.white(`Table: ${table.toUpperCase()}, Limit: ${limit}`));
    console.log(chalk.white(`Raw table param: "${table}"`));
    console.log(chalk.white(`Table type: ${typeof table}`));

    const models = {
      users: User,
      customers: Customer,
      products: Product,
      orders: Order,
      order_items: OrderItem,
      payments: Payment,
      expenses: Expense,
      inventory_transactions: InventoryTransaction,
      salesmen: Salesman,
      suppliers: Supplier,
      categories: Category
    };

    const Model = models[table];
    console.log(`🔍 Looking for model: ${table}`);
    console.log(`🔍 Available models:`, Object.keys(models));
    console.log(`🔍 Model found:`, !!Model);

    if (!Model) {
      console.log(`❌ Invalid table name: ${table}`);
      return res.status(400).json({
        success: false,
        message: 'Invalid table name',
        availableTables: Object.keys(models)
      });
    }

    // Fetch data with simple query (no ordering to avoid issues)
    console.log(`🔍 Fetching data from ${table} model...`);

    const data = await Model.findAll({
      limit: parseInt(limit),
      raw: true
    });

    console.log(`📊 Query completed, found ${data.length} records`);

    console.log(chalk.green(`✅ Table data retrieved: ${data.length} records`));

    const response = {
      success: true,
      data: data,
      metadata: {
        table,
        recordCount: data.length,
        columns: Object.keys(Model.rawAttributes)
      }
    };

    console.log(`📤 Sending response for ${table}:`, JSON.stringify(response, null, 2).substring(0, 200) + '...');
    res.json(response);

  } catch (error) {
    console.error(chalk.red('❌ Table data error:'), error);
    res.status(500).json({ success: false, message: error.message });
  }
});

// Health check endpoint
router.get('/health', async (req, res) => {
  try {
    // Test database connectivity
    await User.count();
    res.json({
      success: true,
      status: 'healthy',
      timestamp: new Date().toISOString(),
      message: 'Database monitoring service is operational'
    });
  } catch (error) {
    res.status(503).json({
      success: false,
      status: 'error',
      timestamp: new Date().toISOString(),
      message: 'Database connection failed',
      error: error.message
    });
  }
});

module.exports = router;
