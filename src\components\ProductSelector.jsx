import { useState, useRef, useEffect } from 'react';
import { ChevronDownIcon, PlusIcon, SearchIcon } from 'lucide-react';

const ProductSelector = ({ 
  products = [], 
  value = '', 
  onChange, 
  onNewProduct,
  placeholder = "Select or search for a product...",
  className = "",
  disabled = false 
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const dropdownRef = useRef(null);
  const inputRef = useRef(null);

  // Filter products based on search term
  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Check if current value matches an existing product
  const selectedProduct = products.find(p => p.name.toLowerCase() === value.toLowerCase());
  const isNewProduct = value && !selectedProduct;

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
        setSearchTerm('');
        setHighlightedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Handle keyboard navigation
  const handleKeyDown = (e) => {
    if (!isOpen) {
      if (e.key === 'Enter' || e.key === 'ArrowDown') {
        setIsOpen(true);
        setSearchTerm('');
        e.preventDefault();
      }
      return;
    }

    const totalOptions = filteredProducts.length + (isNewProduct ? 0 : 1); // +1 for "Add New Product"

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setHighlightedIndex(prev => 
          prev < totalOptions - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setHighlightedIndex(prev => 
          prev > 0 ? prev - 1 : totalOptions - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (highlightedIndex === -1) {
          // No selection, treat as new product if there's text
          if (searchTerm.trim()) {
            handleSelectNewProduct(searchTerm.trim());
          }
        } else if (highlightedIndex < filteredProducts.length) {
          // Select existing product
          handleSelectProduct(filteredProducts[highlightedIndex]);
        } else {
          // Select "Add New Product" option
          handleSelectNewProduct(searchTerm.trim());
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setSearchTerm('');
        setHighlightedIndex(-1);
        break;
    }
  };

  const handleSelectProduct = (product) => {
    onChange(product.name);
    setIsOpen(false);
    setSearchTerm('');
    setHighlightedIndex(-1);
    if (onNewProduct) {
      onNewProduct(false); // Indicate this is an existing product
    }
  };

  const handleSelectNewProduct = (productName) => {
    if (productName.trim()) {
      onChange(productName.trim());
      setIsOpen(false);
      setSearchTerm('');
      setHighlightedIndex(-1);
      if (onNewProduct) {
        onNewProduct(true); // Indicate this is a new product
      }
    }
  };

  const handleInputChange = (e) => {
    const newValue = e.target.value;
    onChange(newValue);
    setSearchTerm(newValue);
    setHighlightedIndex(-1);
    if (!isOpen) {
      setIsOpen(true);
    }
  };

  const handleInputClick = () => {
    if (!isOpen) {
      setIsOpen(true);
      setSearchTerm(value);
    }
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Input Field */}
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          value={isOpen ? searchTerm : value}
          onChange={handleInputChange}
          onClick={handleInputClick}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          disabled={disabled}
          className={`w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
            disabled ? 'bg-gray-100 cursor-not-allowed' : 'bg-white'
          } ${selectedProduct ? 'text-gray-900' : isNewProduct ? 'text-blue-600 font-medium' : 'text-gray-700'}`}
        />
        <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
          {isOpen ? (
            <SearchIcon size={16} className="text-gray-400" />
          ) : (
            <ChevronDownIcon size={16} className="text-gray-400" />
          )}
        </div>
      </div>

      {/* Status Indicator */}
      {value && (
        <div className="absolute -bottom-5 left-0 text-xs">
          {selectedProduct ? (
            <span className="text-green-600">✓ Existing product</span>
          ) : (
            <span className="text-blue-600">+ New product</span>
          )}
        </div>
      )}

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
          {/* Add New Product Option */}
          {searchTerm.trim() && !selectedProduct && (
            <div
              className={`px-3 py-2 cursor-pointer flex items-center ${
                highlightedIndex === filteredProducts.length
                  ? 'bg-blue-50 text-blue-700'
                  : 'text-gray-700 hover:bg-gray-50'
              }`}
              onClick={() => handleSelectNewProduct(searchTerm.trim())}
            >
              <PlusIcon size={16} className="mr-2 text-blue-500" />
              <span className="font-medium">Add New Product: "{searchTerm.trim()}"</span>
            </div>
          )}

          {/* Existing Products */}
          {filteredProducts.length > 0 ? (
            filteredProducts.map((product, index) => (
              <div
                key={product.id}
                className={`px-3 py-2 cursor-pointer ${
                  highlightedIndex === index
                    ? 'bg-blue-50 text-blue-700'
                    : 'text-gray-700 hover:bg-gray-50'
                }`}
                onClick={() => handleSelectProduct(product)}
              >
                <div className="flex justify-between items-center">
                  <span className="font-medium">{product.name}</span>
                  <span className="text-sm text-gray-500">
                    ${(product.price || 0).toFixed(2)}
                  </span>
                </div>
                {product.category && (
                  <div className="text-xs text-gray-400 mt-1">
                    {product.category} • Stock: {product.stock || 0}
                  </div>
                )}
              </div>
            ))
          ) : searchTerm && !isNewProduct ? (
            <div className="px-3 py-2 text-gray-500 text-sm">
              No products found matching "{searchTerm}"
            </div>
          ) : null}

          {/* No products message */}
          {products.length === 0 && (
            <div className="px-3 py-2 text-gray-500 text-sm">
              No products available. Start by adding a new product.
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ProductSelector;
