import { useState, Fragment } from 'react';
import { UsersIcon, PlusIcon, SearchIcon, ChevronLeftIcon, ChevronRightIcon, EyeIcon, EditIcon, PhoneIcon, MailIcon, MapPinIcon } from 'lucide-react';
import { useData } from '../contexts/DataContext';
import RefreshButton from '../components/RefreshButton';
import useAutoRefresh from '../hooks/useAutoRefresh';
import AutoRefreshControl from '../components/AutoRefreshControl';
import useDataRefresh from '../hooks/useDataRefresh';
const Customers = () => {
  const {
    customers,
    addCustomer,
    getCustomerTotalPurchased,
    getCustomerPendingAmount,
    getCustomerTotalOrders,
    getCustomerOrderHistory,
    refreshCustomers
  } = useData();

  // ENHANCEMENT: Auto-refresh after CRUD operations
  const { refreshAfterCustomerCreate } = useDataRefresh();
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const [showAddCustomer, setShowAddCustomer] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [formData, setFormData] = useState({
    name: '',
    type: '',
    contact: '',
    phone: '',
    email: '',
    address: ''
  });
  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleTypeFilter = (e) => {
    setTypeFilter(e.target.value);
  };

  const handleView = (id) => {
    setSelectedCustomer(id === selectedCustomer ? null : id);
  };

  // Filter customers based on search term and type filter
  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = searchTerm === '' ||
      customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.contact.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.phone.includes(searchTerm) ||
      customer.email.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesType = typeFilter === '' || customer.type === typeFilter;

    return matchesSearch && matchesType;
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      // Add customer using DataContext (now async)
      await addCustomer(formData);

      // ENHANCEMENT: Trigger automatic data refresh after customer creation
      await refreshAfterCustomerCreate();

      // Reset form and close modal
      setFormData({
        name: '',
        type: '',
        contact: '',
        phone: '',
        email: '',
        address: ''
      });
      setShowAddCustomer(false);

      // Show success message (you can add a toast notification here)
      alert('Customer added successfully!');
    } catch (error) {
      console.error('Error adding customer:', error);
      alert(`Error adding customer: ${error.message}`);
    }
  };

  // Use only real customer data from DataContext
  const getStatusBadgeClass = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'approved':
        return 'bg-blue-100 text-blue-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800'; // FIXED: Add cancelled status styling
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // ENHANCEMENT: Comprehensive refresh function for Customers
  const handleRefreshData = async () => {
    try {
      console.log('🔄 Refreshing Customers data...');
      // Refresh all relevant data for customers page
      await Promise.all([
        refreshCustomers()
      ]);
      console.log('✅ Customers data refreshed successfully');
    } catch (error) {
      console.error('❌ Failed to refresh Customers data:', error);
      throw error; // Re-throw to let RefreshButton handle the error
    }
  };

  // ENHANCEMENT: Auto-refresh functionality
  const {
    isAutoRefreshEnabled,
    isPaused,
    lastRefresh,
    toggleAutoRefresh,
    manualRefresh
  } = useAutoRefresh(handleRefreshData, {
    interval: 3000, // 3 seconds
    enabled: true,
    pauseOnInteraction: true,
    interactionTimeout: 5000 // 5 seconds
  });
  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Customers</h1>
          <p className="text-gray-600">Manage customer information and track purchase history</p>
        </div>
        <div className="flex items-center space-x-4">
          <AutoRefreshControl
            isAutoRefreshEnabled={isAutoRefreshEnabled}
            isPaused={isPaused}
            lastRefresh={lastRefresh}
            toggleAutoRefresh={toggleAutoRefresh}
            interval={3000}
            size="sm"
          />
          <RefreshButton
            onRefresh={manualRefresh}
            variant="outline"
            size="md"
            label="Refresh Now"
          />
          <button className="flex items-center bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700" onClick={() => setShowAddCustomer(!showAddCustomer)}>
            <PlusIcon size={16} className="mr-2" />
            Add Customer
          </button>
        </div>
      </div>
      {/* Search and Filter */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="flex-grow max-w-md">
            <div className="relative">
              <input type="text" placeholder="Search customers..." value={searchTerm} onChange={handleSearch} className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" />
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <SearchIcon size={18} className="text-gray-400" />
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <select
              value={typeFilter}
              onChange={handleTypeFilter}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Types</option>
              <option value="Convenience Store">Convenience Store</option>
              <option value="Supermarket">Supermarket</option>
              <option value="Wholesaler">Wholesaler</option>
              <option value="Restaurant">Restaurant</option>
              <option value="Other">Other</option>
            </select>
            {(searchTerm || typeFilter) && (
              <button
                onClick={() => {
                  setSearchTerm('');
                  setTypeFilter('');
                }}
                className="flex items-center bg-red-100 text-red-700 px-4 py-2 rounded-md hover:bg-red-200 transition-colors"
              >
                Clear Filters
              </button>
            )}
          </div>
        </div>
      </div>
      {/* Add Customer Form (conditionally rendered) */}
      {showAddCustomer && (
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="flex items-center mb-4">
            <UsersIcon size={20} className="text-blue-600 mr-2" />
            <h2 className="text-lg font-medium text-gray-800">Add New Customer</h2>
          </div>
          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">Customer Name</label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter customer name"
                  required
                />
              </div>
              <div>
                <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-1">Customer Type</label>
                <select
                  id="type"
                  name="type"
                  value={formData.type}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                >
                  <option value="">Select type</option>
                  <option value="Convenience Store">Convenience Store</option>
                  <option value="Supermarket">Supermarket</option>
                  <option value="Wholesaler">Wholesaler</option>
                  <option value="Restaurant">Restaurant</option>
                  <option value="Other">Other</option>
                </select>
              </div>
              <div>
                <label htmlFor="contact" className="block text-sm font-medium text-gray-700 mb-1">Contact Person</label>
                <input
                  type="text"
                  id="contact"
                  name="contact"
                  value={formData.contact}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter contact person name"
                  required
                />
              </div>
              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter phone number"
                  required
                />
              </div>
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter email address"
                />
              </div>
            </div>
            <div className="mb-6">
              <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">Address</label>
              <textarea
                id="address"
                name="address"
                value={formData.address}
                onChange={handleInputChange}
                rows={2}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter complete address"
                required
              ></textarea>
            </div>
            <div className="flex justify-end">
              <button type="button" className="px-4 py-2 mr-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50" onClick={() => setShowAddCustomer(false)}>
                Cancel
              </button>
              <button type="submit" className="flex items-center bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700">
                <PlusIcon size={16} className="mr-2" />
                Add Customer
              </button>
            </div>
          </form>
        </div>
      )}
      {/* Customers Table */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact Person</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Purchased</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pending Amount</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Order</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredCustomers.length > 0 ? filteredCustomers.map(customer => (
                <Fragment key={customer.id}>
                  <tr>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{customer.name}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{customer.type}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{customer.contact}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      ${getCustomerTotalPurchased(customer.id).toFixed(2)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <span className={getCustomerPendingAmount(customer.id) > 0 ? 'text-red-600 font-medium' : 'text-gray-900'}>
                        ${getCustomerPendingAmount(customer.id).toFixed(2)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {customer.lastOrder || 'No orders yet'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button className="text-blue-600 hover:text-blue-900" onClick={() => handleView(customer.id)}><EyeIcon size={18} /></button>
                        <button className="text-green-600 hover:text-green-900"><EditIcon size={18} /></button>
                      </div>
                    </td>
                  </tr>
                  {selectedCustomer === customer.id && (
                    <tr>
                      <td colSpan={7} className="px-6 py-4 bg-gray-50">
                        <div className="border rounded-md overflow-hidden">
                          <div className="bg-gray-100 px-4 py-2 font-medium flex justify-between items-center">
                            <span>Customer Details</span>
                            <button className="text-gray-500 hover:text-gray-700" onClick={() => setSelectedCustomer(null)}>
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                              </svg>
                            </button>
                          </div>
                          <div className="p-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                              <div>
                                <p className="text-sm text-gray-500 mb-1">Contact Information</p>
                                <div className="flex items-center mb-2"><PhoneIcon size={16} className="text-gray-400 mr-2" /><span>{customer.phone}</span></div>
                                <div className="flex items-center mb-2"><MailIcon size={16} className="text-gray-400 mr-2" /><span>{customer.email}</span></div>
                                <div className="flex items-start"><MapPinIcon size={16} className="text-gray-400 mr-2 mt-1" /><span>{customer.address}</span></div>
                              </div>
                              <div>
                                <p className="text-sm text-gray-500 mb-1">Purchase Information</p>
                                <div className="flex justify-between mb-1"><span>Total Purchased:</span><span className="font-medium">${getCustomerTotalPurchased(customer.id).toFixed(2)}</span></div>
                                <div className="flex justify-between mb-1"><span>Pending Amount:</span><span className={getCustomerPendingAmount(customer.id) > 0 ? 'text-red-600 font-medium' : 'font-medium'}>${getCustomerPendingAmount(customer.id).toFixed(2)}</span></div>
                                <div className="flex justify-between"><span>Total Orders:</span><span className="font-medium">{getCustomerTotalOrders(customer.id)}</span></div>
                              </div>
                            </div>
                            <div>
                              <p className="text-sm text-gray-500 mb-2">Recent Orders</p>
                              {(() => {
                                // FIXED: Use dynamic order history from DataContext
                                const orderHistory = getCustomerOrderHistory(customer.id, 10);
                                return orderHistory && orderHistory.length > 0 ? (
                                  <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                      <tr>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order ID</th>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                      </tr>
                                    </thead>
                                    <tbody className="divide-y divide-gray-200">
                                      {orderHistory.map((order, index) => (
                                        <tr key={index}>
                                          <td className="px-4 py-2 whitespace-nowrap text-sm font-medium text-blue-600">{order.id}</td>
                                          <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{order.date}</td>
                                          <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">${order.amount.toFixed(2)}</td>
                                          <td className="px-4 py-2 whitespace-nowrap text-sm">
                                            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(order.status)}`}>
                                              {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                                            </span>
                                          </td>
                                        </tr>
                                      ))}
                                    </tbody>
                                  </table>
                                ) : (
                                  <p className="text-gray-500 text-sm">No orders yet</p>
                                );
                              })()}
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                  )}
                </Fragment>
              )) : (
                <tr>
                  <td colSpan="7" className="px-6 py-12 text-center text-gray-500">
                    <UsersIcon size={48} className="mx-auto mb-4 text-gray-300" />
                    {customers.length === 0 ? (
                      <>
                        <h3 className="text-lg font-medium text-gray-700 mb-2">No Customers Found</h3>
                        <p className="text-gray-500 mb-4">Start by adding your first customer to the system.</p>
                        <button
                          onClick={() => setShowAddCustomer(true)}
                          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                        >
                          <PlusIcon size={16} className="mr-2" />
                          Add First Customer
                        </button>
                      </>
                    ) : (
                      <>
                        <h3 className="text-lg font-medium text-gray-700 mb-2">No Matching Customers</h3>
                        <p className="text-gray-500 mb-4">
                          No customers match your current search criteria.
                          {searchTerm && <><br />Search term: "{searchTerm}"</>}
                          {typeFilter && <><br />Type filter: "{typeFilter}"</>}
                        </p>
                        <button
                          onClick={() => {
                            setSearchTerm('');
                            setTypeFilter('');
                          }}
                          className="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
                        >
                          Clear Filters
                        </button>
                      </>
                    )}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
        {/* Pagination */}
        <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                {filteredCustomers.length > 0 ? (
                  <>
                    Showing <span className="font-medium">1</span> to{' '}
                    <span className="font-medium">{filteredCustomers.length}</span> of{' '}
                    <span className="font-medium">{filteredCustomers.length}</span> customers
                    {(searchTerm || typeFilter) && (
                      <span className="text-gray-500"> (filtered from {customers.length} total)</span>
                    )}
                  </>
                ) : (
                  <>No customers to display</>
                )}
              </p>
            </div>
            {customers.length > 10 && (
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                  <button className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50" disabled={currentPage === 1} onClick={() => setCurrentPage(currentPage - 1)}>
                    <span className="sr-only">Previous</span>
                    <ChevronLeftIcon size={16} />
                  </button>
                  <button className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-blue-600 hover:bg-gray-50">1</button>
                  <button className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50" onClick={() => setCurrentPage(currentPage + 1)}>
                    <span className="sr-only">Next</span>
                    <ChevronRightIcon size={16} />
                  </button>
                </nav>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
export default Customers;
