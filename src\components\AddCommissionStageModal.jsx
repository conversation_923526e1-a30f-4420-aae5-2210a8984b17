import { useState } from 'react';

function AddCommissionStageModal({ isOpen, onClose, onSubmit, loading = false, existingStages = [] }) {
  const [form, setForm] = useState({
    stageName: '',
    minSales: '',
    maxSales: '',
    commission: '',
    bonus: ''
  });
  const [error, setError] = useState('');

  const handleChange = (e) => {
    const { name, value } = e.target;
    setForm(f => ({ ...f, [name]: value }));
    setError('');
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!form.stageName.trim() || !form.minSales || !form.commission) {
      setError('Please fill all required fields.');
      return;
    }
    const min = parseFloat(form.minSales);
    const max = form.maxSales ? parseFloat(form.maxSales) : null;
    if (max !== null && max <= min) {
      setError('Max Sales must be greater than Min Sales.');
      return;
    }
    // Calculate next stage_number for global stages only
    let relevantStages = existingStages.filter(s => !s.salesman_id);
    let nextStageNumber = 1;
    if (relevantStages.length > 0) {
      nextStageNumber = Math.max(...relevantStages.map(s => s.stage_number || 0)) + 1;
    }
    onSubmit({
      stage_number: nextStageNumber,
      stage_name: form.stageName.trim(),
      min_sales_amount: min,
      max_sales_amount: max,
      commission_percentage: parseFloat(form.commission) / 100,
      bonus_amount: form.bonus ? parseFloat(form.bonus) : 0,
      salesman_id: null
    });
  };

  if (!isOpen) return null;
  return (
    <div className="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md p-6">
        <h2 className="text-lg font-bold mb-4">Add Commission Stage</h2>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block mb-1">Stage Name *</label>
            <input name="stageName" value={form.stageName} onChange={handleChange} className="w-full border rounded px-2 py-1" />
          </div>
          <div className="flex space-x-2">
            <div className="flex-1">
              <label className="block mb-1">Min Sales *</label>
              <input name="minSales" type="number" value={form.minSales} onChange={handleChange} className="w-full border rounded px-2 py-1" />
            </div>
            <div className="flex-1">
              <label className="block mb-1">Max Sales</label>
              <input name="maxSales" type="number" value={form.maxSales} onChange={handleChange} className="w-full border rounded px-2 py-1" />
            </div>
          </div>
          <div>
            <label className="block mb-1">Commission % *</label>
            <input name="commission" type="number" value={form.commission} onChange={handleChange} className="w-full border rounded px-2 py-1" />
          </div>
          <div>
            <label className="block mb-1">Bonus</label>
            <input name="bonus" type="number" value={form.bonus} onChange={handleChange} className="w-full border rounded px-2 py-1" />
          </div>
          {error && <div className="text-red-600 text-sm">{error}</div>}
          <div className="flex justify-end space-x-2 mt-4">
            <button type="button" onClick={onClose} className="px-4 py-2 bg-gray-200 rounded">Cancel</button>
            <button type="submit" disabled={loading} className="px-4 py-2 bg-blue-600 text-white rounded">Add</button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default AddCommissionStageModal;
