import { useState, useEffect, useRef } from 'react';

function CommissionStageModal({ isOpen, onClose, stages = [], overrides = [], salesmen = [], loading = false, onDataChange }) {
  const [edit, setEdit] = useState({}); // { [salesmanId_stageId]: value }
  const [original, setOriginal] = useState({}); // Track original values for dirty check
  const [error, setError] = useState('');
  const [loadingEdit, setLoadingEdit] = useState(false);
  const prevIsOpen = useRef(false);

  useEffect(() => {
    // Only reset when modal is first opened
    if (isOpen && !prevIsOpen.current) {
      const orig = {};
      salesmen.forEach(salesman => {
        stages.filter(s => !s.salesman_id).forEach(stage => {
          const key = `${salesman.salesman_id}_${stage.stage_id}`;
          // Use override if exists, else global
          const override = overrides.find(o => o.salesman_id === salesman.salesman_id && o.stage_id === stage.stage_id);
          orig[key] = override ? parseFloat(override.commission_percentage) * 100 : parseFloat(stage.commission_percentage) * 100;
        });
      });
      setOriginal(orig);
      setEdit({});
    }
    prevIsOpen.current = isOpen;
  }, [isOpen]);

  if (!isOpen) return null;

  // Only global stages
  const globalStages = stages.filter(s => !s.salesman_id).sort((a, b) => a.stage_number - b.stage_number);

  // Handler for commission % change
  const handleCommissionChange = (salesmanId, stageId, value) => {
    setEdit(e => ({ ...e, [`${salesmanId}_${stageId}`]: value }));
    setError('');
  };

  // Handler for save
  const handleSave = async (salesmanId, stage) => {
    const key = `${salesmanId}_${stage.stage_id}`;
    const commission = parseFloat(edit[key]);
    if (isNaN(commission) || commission < 0 || commission > 100) {
      setError('Commission % must be between 0 and 100.');
      return;
    }
    setLoadingEdit(true);
    try {
      const token = localStorage.getItem('auth_token');
      const res = await fetch('http://localhost:3001/api/commissions/overrides', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          salesman_id: salesmanId,
          stage_id: stage.stage_id,
          commission_percentage: commission / 100
        })
      });
      const data = await res.json();
      if (!data.success) throw new Error(data.message || 'Failed to update commission percentage');
      setOriginal(o => ({ ...o, [key]: commission })); // Update original value after save
      setEdit(e => ({ ...e, [key]: undefined }));
      if (onDataChange) onDataChange(); // Refetch data after save
    } catch (e) {
      setError(e.message || 'Failed to update commission percentage.');
    } finally {
      setLoadingEdit(false);
    }
  };

  // Helper to get the original value for dirty check
  const getOriginalValue = (salesmanId, stage) => {
    const key = `${salesmanId}_${stage.stage_id}`;
    if (original[key] !== undefined) return original[key];
    // Use override if exists, else global
    const override = overrides.find(o => o.salesman_id === salesmanId && o.stage_id === stage.stage_id);
    return override ? parseFloat(override.commission_percentage) * 100 : parseFloat(stage.commission_percentage) * 100;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl p-6">
        <h2 className="text-lg font-bold mb-4">Manage Commission Percentages</h2>
        {error && <div className="text-red-600 text-sm mb-2">{error}</div>}
        <table className="min-w-full border mb-4">
          <thead>
            <tr>
              <th className="border px-2 py-1">Salesman</th>
              {globalStages.map(stage => (
                <th key={stage.stage_id} className="border px-2 py-1">{stage.stage_name} <br/>({stage.min_sales_amount} - {stage.max_sales_amount ?? '∞'})</th>
              ))}
            </tr>
          </thead>
          <tbody>
            {salesmen.map(salesman => (
              <tr key={salesman.salesman_id}>
                <td className="border px-2 py-1 font-semibold">{salesman.full_name}</td>
                {globalStages.map(stage => {
                  const key = `${salesman.salesman_id}_${stage.stage_id}`;
                  const value = edit[key] !== undefined ? edit[key] : getOriginalValue(salesman.salesman_id, stage);
                  const dirty = value !== getOriginalValue(salesman.salesman_id, stage);
                  return (
                    <td key={stage.stage_id} className="border px-2 py-1">
                      <div className="flex items-center space-x-2">
                        <input
                          type="number"
                          min={0}
                          max={100}
                          value={value}
                          onChange={e => handleCommissionChange(salesman.salesman_id, stage.stage_id, e.target.value)}
                          className="w-20 border rounded px-2 py-1"
                          disabled={loadingEdit || loading}
                        />
                        {dirty && (
                          <button
                            className="px-2 py-1 bg-blue-600 text-white rounded text-xs"
                            onClick={() => handleSave(salesman.salesman_id, stage)}
                            disabled={loadingEdit || loading}
                          >Save</button>
                        )}
                      </div>
                    </td>
                  );
                })}
              </tr>
            ))}
          </tbody>
        </table>
        <div className="flex justify-end mt-4">
          <button onClick={onClose} className="px-4 py-2 bg-gray-200 rounded">Close</button>
        </div>
      </div>
    </div>
  );
}

export default CommissionStageModal;
