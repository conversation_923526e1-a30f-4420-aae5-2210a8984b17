import { useState, useEffect } from 'react';
import {
  RefreshCwIcon,
  DatabaseIcon,
  EyeIcon,
  ActivityIcon,
  UsersIcon,
  ShoppingCartIcon,
  PackageIcon,
  CreditCardIcon,
  TrendingUpIcon,
  AlertTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  SearchIcon,
  FilterIcon,
  InfoIcon,
  ServerIcon,
  HardDriveIcon,
  ZapIcon,
  UserCheckIcon,
  ShoppingBagIcon,
  ArchiveIcon,
  DollarSignIcon,
  FileTextIcon,
  TruckIcon,
  BarChart3Icon,
  TrashIcon,
  ShieldIcon,
  DownloadIcon,
  UserIcon,
  BuildingIcon,
  TagIcon
} from 'lucide-react';
import ResetDataModal from './ResetDataModal';

function DatabaseMonitor() {
  console.log('🚀 DatabaseMonitor function called');

  const [stats, setStats] = useState(null);
  const [selectedTable, setSelectedTable] = useState('');
  const [tableData, setTableData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterColumn, setFilterColumn] = useState('');
  const [activityFeed, setActivityFeed] = useState([]);
  const [dbHealth, setDbHealth] = useState(null);

  // Enhanced state for new features
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(50);
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState('DESC');
  const [showResetModal, setShowResetModal] = useState(false);
  const [resetTarget, setResetTarget] = useState('');
  const [confirmationText, setConfirmationText] = useState('');
  const [resetLoading, setResetLoading] = useState(false);
  const [lastRefresh, setLastRefresh] = useState(null);

  const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api';
  console.log('🔗 API_BASE_URL:', API_BASE_URL);

  // Enhanced table metadata with descriptions and icons
  const tableMetadata = {
    users: {
      icon: <UsersIcon size={20} />,
      name: 'Users',
      description: 'System users including admins and salesmen with authentication data',
      color: 'bg-blue-500',
      keyFields: ['email', 'role', 'email_verified'],
      relationships: ['salesmen (user_id)']
    },
    customers: {
      icon: <UserCheckIcon size={20} />,
      name: 'Customers',
      description: 'Customer records with contact information and purchase history',
      color: 'bg-green-500',
      keyFields: ['name', 'email', 'phone'],
      relationships: ['orders (customer_id)', 'payments (customer_id)']
    },
    products: {
      icon: <PackageIcon size={20} />,
      name: 'Products',
      description: 'Product catalog with pricing, inventory levels, and supplier information',
      color: 'bg-purple-500',
      keyFields: ['name', 'price', 'stock', 'category'],
      relationships: ['order_items (product_id)', 'inventory_transactions (product_id)']
    },
    orders: {
      icon: <ShoppingCartIcon size={20} />,
      name: 'Orders',
      description: 'Sales orders with customer details, status, and order totals',
      color: 'bg-orange-500',
      keyFields: ['order_number', 'status', 'total_amount'],
      relationships: ['order_items (order_id)', 'payments (order_id)']
    },
    order_items: {
      icon: <ShoppingBagIcon size={20} />,
      name: 'Order Items',
      description: 'Individual line items within orders with product and quantity details',
      color: 'bg-indigo-500',
      keyFields: ['product_id', 'quantity', 'unit_price'],
      relationships: ['orders (order_id)', 'products (product_id)']
    },
    payments: {
      icon: <CreditCardIcon size={20} />,
      name: 'Payments',
      description: 'Payment records tracking customer payments and order settlements',
      color: 'bg-emerald-500',
      keyFields: ['payment_number', 'amount', 'payment_method'],
      relationships: ['customers (customer_id)', 'orders (order_id)']
    },
    expenses: {
      icon: <DollarSignIcon size={20} />,
      name: 'Expenses',
      description: 'Business expense tracking for operational costs and expenditures',
      color: 'bg-red-500',
      keyFields: ['category', 'amount', 'expense_date'],
      relationships: []
    },
    inventory_transactions: {
      icon: <ArchiveIcon size={20} />,
      name: 'Inventory Transactions',
      description: 'All inventory movements including stock in, out, and adjustments',
      color: 'bg-yellow-500',
      keyFields: ['type', 'quantity', 'transaction_date'],
      relationships: ['products (product_id)']
    },
    salesmen: {
      icon: <TrendingUpIcon size={20} />,
      name: 'Salesmen',
      description: 'Salesman profiles with performance metrics and commission data',
      color: 'bg-cyan-500',
      keyFields: ['full_name', 'commission_rate', 'total_sales'],
      relationships: ['users (user_id)', 'orders (salesman_id)']
    },
    suppliers: {
      icon: <TruckIcon size={20} />,
      name: 'Suppliers',
      description: 'Supplier information for product sourcing and procurement',
      color: 'bg-pink-500',
      keyFields: ['name', 'contact_info', 'is_active'],
      relationships: ['products (supplier_id)']
    },
    categories: {
      icon: <TagIcon size={20} />,
      name: 'Categories',
      description: 'Product categories for organizing and classifying inventory items',
      color: 'bg-violet-500',
      keyFields: ['name', 'description', 'is_active'],
      relationships: ['products (category_id)']
    }
  };

  // Utility functions for data formatting
  const formatValue = (value, column) => {
    if (value === null || value === undefined) return <span className="text-gray-400">NULL</span>;

    // Date formatting
    if (column.includes('date') || column.includes('_at') || column.includes('expires')) {
      return new Date(value).toLocaleString();
    }

    // Currency formatting
    if (column.includes('price') || column.includes('amount') || column.includes('cost') || column.includes('total')) {
      return `$${parseFloat(value).toFixed(2)}`;
    }

    // Boolean formatting
    if (typeof value === 'boolean') {
      return (
        <span className={`px-2 py-1 rounded-full text-xs ${value ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
          {value ? 'Yes' : 'No'}
        </span>
      );
    }

    // JSON formatting
    if (typeof value === 'object') {
      return <pre className="text-xs bg-gray-100 p-1 rounded">{JSON.stringify(value, null, 2)}</pre>;
    }

    // String truncation for long values
    const stringValue = String(value);
    if (stringValue.length > 50) {
      return (
        <span title={stringValue}>
          {stringValue.substring(0, 50)}...
        </span>
      );
    }

    return stringValue;
  };

  const getColumnType = (column, value) => {
    if (column.includes('id')) return 'ID';
    if (column.includes('date') || column.includes('_at')) return 'DateTime';
    if (column.includes('price') || column.includes('amount') || column.includes('cost')) return 'Currency';
    if (typeof value === 'boolean') return 'Boolean';
    if (typeof value === 'number') return 'Number';
    if (column.includes('email')) return 'Email';
    if (column.includes('phone')) return 'Phone';
    return 'Text';
  };

  const calculatePercentage = (count, total) => {
    if (total === 0) return 0;
    return ((count / total) * 100).toFixed(1);
  };

  // Fetch database statistics (try monitor endpoint first, then enhanced)
  const fetchStats = async () => {
    try {
      console.log('📊 Fetching database stats...');
      setLoading(true);

      // Try monitor endpoint first (no auth required)
      try {
        const response = await fetch(`${API_BASE_URL}/monitor/database/stats`);
        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            console.log('✅ Got monitor database stats:', data);
            setStats(data.data);
            setLastRefresh(new Date());
            return;
          }
        }
      } catch (monitorError) {
        console.warn('⚠️ Monitor endpoint failed, trying enhanced endpoint:', monitorError.message);
      }

      // Fallback to enhanced endpoint with auth
      const token = localStorage.getItem('auth_token') ||
                   localStorage.getItem('token');
      if (!token) {
        console.warn('⚠️ No auth token found, falling back to basic stats');
        await fetchBasicStats();
        return;
      }

      console.log('🔑 Using auth token for enhanced stats');

      const response = await fetch(`${API_BASE_URL}/admin/database/stats`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('Response status:', response.status);

      if (!response.ok) {
        if (response.status === 401) {
          console.warn('⚠️ Authentication failed, falling back to basic stats');
          await fetchBasicStats();
          return;
        }
        if (response.status === 403) {
          console.warn('⚠️ Access denied (admin required), falling back to basic stats');
          await fetchBasicStats();
          return;
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ Enhanced database stats received:', data);

      if (data.success) {
        setStats(data.data);
        setLastRefresh(new Date());
        console.log('✅ Enhanced stats updated successfully');
      } else {
        console.error('❌ API returned error:', data.message);
        await fetchBasicStats();
      }
    } catch (error) {
      console.error('❌ Failed to fetch enhanced database stats:', error);
      // Fallback to basic stats if enhanced endpoint fails
      await fetchBasicStats();
    } finally {
      setLoading(false);
    }
  };

  // Fallback function for basic stats
  const fetchBasicStats = async () => {
    try {
      console.log('📊 Falling back to basic stats...');

      // Try the monitor stats endpoint first (no auth required)
      try {
        const response = await fetch(`${API_BASE_URL}/monitor/database/stats`);
        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            console.log('✅ Got monitor database stats:', data);
            setStats(data.data);
            setLastRefresh(new Date());
            return;
          }
        }
      } catch (monitorError) {
        console.warn('⚠️ Monitor stats endpoint failed:', monitorError.message);
      }

      // Try to get basic data without authentication first
      const basicStats = {
        tables: {
          users: 0,
          customers: 0,
          products: 0,
          orders: 0,
          payments: 0,
          expenses: 0,
          inventory_transactions: 0,
          salesmen: 0,
          suppliers: 0,
          order_items: 0,
          categories: 0
        },
        health: {
          status: 'limited',
          message: 'Limited access - enhanced features require admin authentication'
        },
        lastUpdated: {},
        dataIntegrity: {
          message: 'Data integrity checks require admin access'
        },
        timestamp: new Date().toISOString(),
        totalRecords: 0
      };

      const token = localStorage.getItem('token');
      if (token) {
        try {
          // Try to get user count if we have a token
          const response = await fetch(`${API_BASE_URL}/admin/users`, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });

          if (response.ok) {
            const result = await response.json();
            basicStats.tables.users = result.data?.length || 0;
            basicStats.totalRecords = result.data?.length || 0;
            basicStats.health.status = 'partial';
            basicStats.health.message = 'Partial data available - some features require enhanced access';
            console.log('✅ Got user count for basic stats');
          }
        } catch (userError) {
          console.warn('⚠️ Could not fetch user count:', userError.message);
        }
      }

      setStats(basicStats);
      setLastRefresh(new Date());
      console.log('✅ Basic stats loaded as fallback');

    } catch (error) {
      console.error('❌ Error fetching basic stats:', error);
      // Set minimal stats even if everything fails
      const minimalStats = {
        tables: {
          users: 0,
          customers: 0,
          products: 0,
          orders: 0,
          payments: 0,
          expenses: 0,
          inventory_transactions: 0,
          salesmen: 0,
          suppliers: 0,
          order_items: 0,
          categories: 0
        },
        health: {
          status: 'error',
          message: 'Unable to connect to database monitoring service'
        },
        lastUpdated: {},
        dataIntegrity: {},
        timestamp: new Date().toISOString(),
        totalRecords: 0
      };
      setStats(minimalStats);
      setLastRefresh(new Date());
    }
  };

  // Fetch table data (try monitor endpoint first, then enhanced)
  const fetchTableData = async (table) => {
    try {
      setLoading(true);
      console.log(`📋 Fetching data for table: ${table}`);

      // Try monitor endpoint first (no auth required)
      try {
        // Special handling for categories due to route conflict
        const endpoint = table === 'categories'
          ? `${API_BASE_URL}/monitor/categories-data?limit=50`
          : `${API_BASE_URL}/monitor/data/${table}?limit=50`;

        const response = await fetch(endpoint);
        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            setTableData(data.data || []);
            console.log(`✅ Monitor table data loaded for ${table}: ${data.data?.length || 0} records`);
            return;
          }
        }
      } catch (monitorError) {
        console.warn(`⚠️ Monitor endpoint failed for ${table}, trying enhanced endpoint:`, monitorError.message);
      }

      // Fallback to enhanced endpoint with auth
      const token = localStorage.getItem('auth_token') ||
                   localStorage.getItem('token');
      if (!token) {
        console.warn('⚠️ No auth token found, using fallback');
        await fetchBasicTableData(table);
        return;
      }

      const offset = (currentPage - 1) * pageSize;
      const params = new URLSearchParams({
        limit: pageSize.toString(),
        offset: offset.toString(),
        search: searchTerm,
        sortBy,
        sortOrder
      });

      const response = await fetch(`${API_BASE_URL}/admin/data/${table}?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log(`✅ Enhanced table data received for ${table}:`, data);

      if (data.success) {
        setTableData(data.data.records || []);
      }
    } catch (error) {
      console.error(`❌ Failed to fetch enhanced table data for ${table}:`, error);
      // Fallback to basic data fetch if needed
      await fetchBasicTableData(table);
    } finally {
      setLoading(false);
    }
  };

  // Fallback function for basic table data
  const fetchBasicTableData = async (table) => {
    try {
      console.log(`📋 Falling back to simple table data for: ${table}`);

      // Try the monitor table data endpoint (no auth required)
      // Special handling for categories due to route conflict
      const endpoint = table === 'categories'
        ? `${API_BASE_URL}/monitor/categories-data?limit=50`
        : `${API_BASE_URL}/monitor/data/${table}?limit=50`;

      const response = await fetch(endpoint);
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setTableData(data.data || []);
          console.log(`✅ Monitor table data loaded for ${table}: ${data.data?.length || 0} records`);
          return;
        }
      }

      // If that fails, set empty data
      console.warn(`⚠️ Could not fetch table data for ${table}`);
      setTableData([]);

    } catch (error) {
      console.error(`❌ Error fetching simple table data for ${table}:`, error);
      setTableData([]);
    }
  };

  // Reset functionality with safety measures
  const handleResetTable = async (confirmationText) => {
    try {
      setResetLoading(true);
      console.log(`🗑️ Resetting table: ${resetTarget}`);

      // Get token from localStorage (using the correct key from AuthContext)
      const token = localStorage.getItem('auth_token') ||
                   localStorage.getItem('token') ||
                   localStorage.getItem('authToken') ||
                   localStorage.getItem('accessToken');

      console.log('🔍 Token retrieval debug:');
      console.log('- auth_token:', localStorage.getItem('auth_token'));
      console.log('- token:', localStorage.getItem('token'));
      console.log('- Final token used:', token);

      if (!token) {
        throw new Error('No authentication token found. Please log in as an admin to use reset functionality.');
      }

      console.log('🔑 Using token for reset operation');

      const endpoint = resetTarget === 'all'
        ? `${API_BASE_URL}/admin/reset/all`
        : `${API_BASE_URL}/admin/reset/${resetTarget}`;

      console.log('🚀 Sending reset request to:', endpoint);
      console.log('📦 Request body:', { confirmationText });

      // Create fetch request with timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        console.warn('⏰ Reset request timeout, aborting...');
        controller.abort();
      }, 60000); // 60 second timeout

      const response = await fetch(endpoint, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ confirmationText }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      console.log('🔍 Reset response status:', response.status);
      console.log('🔍 Reset response headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
        console.error('❌ Reset request failed:', {
          status: response.status,
          statusText: response.statusText,
          errorData
        });

        if (response.status === 401) {
          throw new Error('Authentication failed. Please log in again as an admin.');
        } else if (response.status === 403) {
          throw new Error('Access denied. Admin privileges required for reset operations.');
        } else {
          throw new Error(errorData.message || `HTTP error! status: ${response.status} - ${response.statusText}`);
        }
      }

      const result = await response.json();
      console.log('✅ Reset completed:', result);

      // Show success message
      alert(`✅ Successfully reset ${resetTarget === 'all' ? 'all data' : `${resetTarget} table`}. ${result.recordsDeleted || result.totalDeleted || 0} records deleted.`);

      // Refresh data
      await fetchStats();
      if (selectedTable && selectedTable !== resetTarget) {
        await fetchTableData(selectedTable);
      } else {
        setSelectedTable('');
        setTableData([]);
      }

      // Close modal
      setShowResetModal(false);
      setResetTarget('');

    } catch (error) {
      console.error('❌ Reset failed:', error);

      let errorMessage = 'Unknown error occurred';

      if (error.name === 'AbortError') {
        errorMessage = 'Reset operation timed out. The server may be processing the request. Please check the data and try again if needed.';
      } else if (error.message.includes('NetworkError') || error.message.includes('fetch')) {
        errorMessage = 'Network connection failed. Please check if the server is running and try again.';
      } else if (error.message.includes('Authentication failed')) {
        errorMessage = 'Authentication failed. Please log in again as an admin.';
      } else if (error.message.includes('Access denied')) {
        errorMessage = 'Access denied. Admin privileges required for reset operations.';
      } else {
        errorMessage = error.message;
      }

      alert(`❌ Failed to reset data: ${errorMessage}`);
    } finally {
      setResetLoading(false);
    }
  };

  // Test authentication token
  const testAuthToken = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      console.log('🔍 Testing authentication token...');
      console.log('Token:', token);

      if (!token) {
        console.error('❌ No token found in localStorage');
        return false;
      }

      // Test token with a simple admin endpoint
      const response = await fetch(`${API_BASE_URL}/admin/users`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('🔍 Token test response status:', response.status);

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Token is valid, got data:', data);
        return true;
      } else {
        const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
        console.error('❌ Token test failed:', response.status, errorData);
        return false;
      }
    } catch (error) {
      console.error('❌ Token test error:', error);
      return false;
    }
  };

  const openResetModal = (target) => {
    setResetTarget(target);
    setShowResetModal(true);
  };

  // Export functionality
  const handleExportTable = async (table, format = 'csv') => {
    try {
      console.log(`📥 Exporting ${table} as ${format}`);

      // For now, export the current table data as CSV/JSON
      if (!tableData.length) {
        alert('No data available to export. Please select a table first.');
        return;
      }

      if (format === 'csv') {
        // Convert table data to CSV
        const headers = Object.keys(tableData[0]);
        const csvContent = [
          headers.join(','),
          ...tableData.map(row =>
            headers.map(header => {
              const value = row[header];
              // Escape commas and quotes in CSV
              if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
                return `"${value.replace(/"/g, '""')}"`;
              }
              return value;
            }).join(',')
          )
        ].join('\n');

        // Download CSV
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${table}_export_${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        console.log(`✅ ${table} exported as CSV successfully`);
        return;
      }

      if (format === 'json') {
        // Download JSON
        const jsonContent = JSON.stringify(tableData, null, 2);
        const blob = new Blob([jsonContent], { type: 'application/json' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${table}_export_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        console.log(`✅ ${table} exported as JSON successfully`);
        return;
      }

      // Fallback to authenticated export for other formats
      const token = localStorage.getItem('auth_token') ||
                   localStorage.getItem('token') ||
                   localStorage.getItem('authToken') ||
                   localStorage.getItem('accessToken');

      if (!token) {
        throw new Error('Authentication required for server-side export. Using client-side export instead.');
      }

      const response = await fetch(`${API_BASE_URL}/admin/data/${table}?export=${format}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`Export failed: ${response.status}`);
      }

      // Trigger download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${table}_export_${new Date().toISOString().split('T')[0]}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      console.log(`✅ ${table} exported successfully`);
    } catch (error) {
      console.error(`❌ Export failed:`, error);
      alert(`❌ Failed to export ${table}: ${error.message}`);
    }
  };

  // Filter table data based on search and filter criteria
  const getFilteredData = () => {
    if (!tableData.length) return [];

    let filtered = [...tableData];

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(row => {
        return Object.values(row).some(value =>
          String(value).toLowerCase().includes(searchTerm.toLowerCase())
        );
      });
    }

    // Apply column filter
    if (filterColumn && searchTerm) {
      filtered = filtered.filter(row => {
        const value = row[filterColumn];
        return String(value).toLowerCase().includes(searchTerm.toLowerCase());
      });
    }

    return filtered;
  };

  // Auto-refresh functionality (DISABLED TO PREVENT INFINITE LOOPS)
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        fetchStats();
        if (selectedTable) {
          fetchTableData(selectedTable);
        }
      }, 30000); // Refresh every 30 seconds instead of 3 seconds

      return () => clearInterval(interval);
    }
  }, [autoRefresh, selectedTable]);

  // Initial load
  useEffect(() => {
    console.log('DatabaseMonitor component mounted');
    fetchStats();
  }, []);

  // Handle table selection
  const handleTableSelect = (table) => {
    setSelectedTable(table);
    fetchTableData(table);
  };

  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleString();
  };

  // Enhanced table data renderer with formatting and tooltips
  const renderTableData = () => {
    const filteredData = getFilteredData();
    if (!filteredData.length) return <p className="text-gray-500">No data available</p>;

    const firstItem = filteredData[0];
    const columns = Object.keys(firstItem);

    return (
      <div className="space-y-4">
        {/* Search and Filter Controls */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <SearchIcon size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search all columns..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
          <div className="sm:w-48">
            <select
              value={filterColumn}
              onChange={(e) => setFilterColumn(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Columns</option>
              {columns.map(column => (
                <option key={column} value={column}>{column}</option>
              ))}
            </select>
          </div>
        </div>

        {/* Results Summary */}
        <div className="text-sm text-gray-600">
          Showing {filteredData.length} of {tableData.length} records
          {searchTerm && ` matching "${searchTerm}"`}
        </div>

        {/* Enhanced Table */}
        <div className="overflow-x-auto border rounded-lg">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {columns.map((column) => (
                  <th
                    key={column}
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider group"
                  >
                    <div className="flex items-center space-x-1">
                      <span>{column.replace(/_/g, ' ')}</span>
                      <InfoIcon
                        size={14}
                        className="text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity"
                        title={`Type: ${getColumnType(column, firstItem[column])}`}
                      />
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredData.map((row, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  {columns.map((column) => (
                    <td key={column} className="px-6 py-4 text-sm text-gray-900">
                      <div className="flex items-center space-x-2">
                        <span>{formatValue(row[column], column)}</span>
                        {tableMetadata[selectedTable]?.keyFields.includes(column) && (
                          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                            Key
                          </span>
                        )}
                      </div>
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  // Debug: Add console log to see if component is rendering
  console.log('DatabaseMonitor rendering, stats:', stats, 'loading:', loading);

  return (
    <div className="space-y-6">
      {/* Enhanced Debug info */}
      <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">
        <p><strong>Debug Info:</strong></p>
        <p>Component Status: {loading ? 'Loading...' : 'Ready'}</p>
        <p>Stats Available: {stats ? 'Yes' : 'No'}</p>
        <p>API URL: {API_BASE_URL}</p>
        <p>Auth Token (auth_token): {localStorage.getItem('auth_token') ? 'Present' : 'Missing'}</p>
        <p>Auth Token (token): {localStorage.getItem('token') ? 'Present' : 'Missing'}</p>
        <p>Last Refresh: {lastRefresh ? lastRefresh.toLocaleTimeString() : 'Never'}</p>
        <p>Selected Table: {selectedTable || 'None'}</p>
        <p>Table Data Records: {tableData.length}</p>
        {stats?.health && <p>Health Status: {stats.health.status} - {stats.health.message}</p>}
        <button
          onClick={testAuthToken}
          className="mt-2 px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600"
        >
          Test Auth Token
        </button>
      </div>

      {/* Header */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <DatabaseIcon size={28} className="text-blue-600 mr-3" />
            <div>
              <h1 className="text-3xl font-bold text-gray-800">Database Administration Dashboard</h1>
              <div className="flex items-center mt-1">
                <p className="text-gray-600">Real-time monitoring and data insights for ZIDAN Enterprises</p>
                {stats?.health && (
                  <div className="ml-4 flex items-center">
                    <div className={`w-2 h-2 rounded-full mr-2 ${
                      stats.health.status === 'healthy' ? 'bg-green-500' :
                      stats.health.status === 'partial' ? 'bg-yellow-500' :
                      stats.health.status === 'limited' ? 'bg-orange-500' :
                      'bg-red-500'
                    }`}></div>
                    <span className={`text-xs font-medium ${
                      stats.health.status === 'healthy' ? 'text-green-700' :
                      stats.health.status === 'partial' ? 'text-yellow-700' :
                      stats.health.status === 'limited' ? 'text-orange-700' :
                      'text-red-700'
                    }`}>
                      {stats.health.status === 'healthy' ? 'Full Access' :
                       stats.health.status === 'partial' ? 'Partial Access' :
                       stats.health.status === 'limited' ? 'Limited Access' :
                       'Connection Error'}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
                className="mr-2"
              />
              <ActivityIcon size={16} className="mr-1" />
              Auto-refresh (3s)
            </label>
            <button
              onClick={fetchStats}
              disabled={loading}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              <RefreshCwIcon size={16} className={`mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </button>
          </div>
        </div>
      </div>

      {stats && (
        <>
          {/* Key Metrics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-blue-100">
                  <HardDriveIcon size={24} className="text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Records</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalRecords || 0}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-green-100">
                  <CheckCircleIcon size={24} className="text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Active Tables</p>
                  <p className="text-2xl font-bold text-gray-900">{Object.keys(stats.tables).length}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-purple-100">
                  <UsersIcon size={24} className="text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">System Users</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.tables.users || 0}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-orange-100">
                  <ServerIcon size={24} className="text-orange-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Database Status</p>
                  <p className="text-lg font-bold text-green-600">Online</p>
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Table Statistics */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="text-xl font-semibold text-gray-800">Table Overview</h3>
                <p className="text-sm text-gray-600">
                  Last updated: {lastRefresh ? lastRefresh.toLocaleString() : formatTimestamp(stats.timestamp)}
                </p>
              </div>
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => openResetModal('all')}
                  className="flex items-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                  title="Reset all data (Nuclear option)"
                >
                  <ShieldIcon size={16} className="mr-2" />
                  Nuclear Reset
                </button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {Object.entries(stats.tables).map(([table, count]) => {
                const metadata = tableMetadata[table];
                const percentage = calculatePercentage(count, stats.totalRecords);

                return (
                  <div
                    key={table}
                    className={`p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
                      selectedTable === table
                        ? 'border-blue-500 bg-blue-50 shadow-md'
                        : 'border-gray-200 hover:border-gray-300 hover:shadow-sm'
                    }`}
                    onClick={() => handleTableSelect(table)}
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center">
                        <div className={`p-2 rounded-lg ${metadata?.color || 'bg-gray-500'} text-white mr-3`}>
                          {metadata?.icon || <DatabaseIcon size={16} />}
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-800">{metadata?.name || table}</h4>
                          <p className="text-2xl font-bold text-gray-900">{count}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-gray-600">{percentage}%</p>
                        <div className="w-16 bg-gray-200 rounded-full h-2 mt-1">
                          <div
                            className={`h-2 rounded-full ${metadata?.color || 'bg-gray-500'}`}
                            style={{ width: `${Math.min(percentage, 100)}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>

                    <p className="text-xs text-gray-600 mb-2">{metadata?.description}</p>

                    {metadata?.relationships.length > 0 && (
                      <div className="mt-2">
                        <p className="text-xs font-medium text-gray-700">Related to:</p>
                        <p className="text-xs text-gray-600">{metadata.relationships.join(', ')}</p>
                      </div>
                    )}

                    {count === 0 && (
                      <div className="flex items-center mt-2">
                        <AlertTriangleIcon size={14} className="text-yellow-500 mr-1" />
                        <span className="text-xs text-yellow-600">Empty table</span>
                      </div>
                    )}

                    {/* Action Buttons */}
                    <div className="flex items-center justify-between mt-3 pt-3 border-t border-gray-200">
                      <div className="flex space-x-2">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleExportTable(table, 'csv');
                          }}
                          className="flex items-center px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200"
                          title="Export as CSV"
                        >
                          <DownloadIcon size={12} className="mr-1" />
                          CSV
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleExportTable(table, 'json');
                          }}
                          className="flex items-center px-2 py-1 text-xs bg-green-100 text-green-700 rounded hover:bg-green-200"
                          title="Export as JSON"
                        >
                          <DownloadIcon size={12} className="mr-1" />
                          JSON
                        </button>
                      </div>

                      {count > 0 && (
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            openResetModal(table);
                          }}
                          className="flex items-center px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200"
                          title={`Reset ${table} table`}
                        >
                          <TrashIcon size={12} className="mr-1" />
                          Reset
                        </button>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Enhanced Table Data Viewer */}
          {selectedTable && (
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center">
                  <div className={`p-2 rounded-lg ${tableMetadata[selectedTable]?.color || 'bg-gray-500'} text-white mr-3`}>
                    {tableMetadata[selectedTable]?.icon || <DatabaseIcon size={20} />}
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-800">
                      {tableMetadata[selectedTable]?.name || selectedTable} Data
                    </h3>
                    <p className="text-sm text-gray-600">
                      {tableMetadata[selectedTable]?.description}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-600">
                    Showing latest {tableData.length} records
                  </p>
                  {tableMetadata[selectedTable]?.keyFields && (
                    <p className="text-xs text-gray-500">
                      Key fields: {tableMetadata[selectedTable].keyFields.join(', ')}
                    </p>
                  )}
                </div>
              </div>

              {/* Table Relationships */}
              {tableMetadata[selectedTable]?.relationships.length > 0 && (
                <div className="mb-4 p-3 bg-blue-50 rounded-lg">
                  <h4 className="text-sm font-medium text-blue-800 mb-1">Table Relationships</h4>
                  <p className="text-sm text-blue-700">
                    This table is related to: {tableMetadata[selectedTable].relationships.join(', ')}
                  </p>
                </div>
              )}

              {renderTableData()}
            </div>
          )}
        </>
      )}

      {loading && !stats && (
        <div className="bg-white rounded-lg shadow-md p-12">
          <div className="flex flex-col items-center justify-center">
            <RefreshCwIcon size={32} className="animate-spin text-blue-600 mb-4" />
            <h3 className="text-lg font-semibold text-gray-800 mb-2">Loading Database Statistics</h3>
            <p className="text-gray-600">Fetching real-time data from the database...</p>
          </div>
        </div>
      )}

      {!loading && !stats && (
        <div className="bg-white rounded-lg shadow-md p-12">
          <div className="flex flex-col items-center justify-center">
            <AlertTriangleIcon size={32} className="text-red-500 mb-4" />
            <h3 className="text-lg font-semibold text-gray-800 mb-2">Unable to Load Database</h3>
            <p className="text-gray-600 mb-4">Failed to connect to the database monitoring service.</p>
            <button
              onClick={fetchStats}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Try Again
            </button>
          </div>
        </div>
      )}

      {/* Reset Data Modal */}
      <ResetDataModal
        isOpen={showResetModal}
        onClose={() => setShowResetModal(false)}
        resetTarget={resetTarget}
        onConfirm={handleResetTable}
        loading={resetLoading}
        recordCount={resetTarget && stats?.tables ? stats.tables[resetTarget] || 0 : 0}
      />
    </div>
  );
}

export default DatabaseMonitor;
