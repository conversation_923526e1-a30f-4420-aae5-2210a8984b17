const { Sequelize } = require('sequelize');
require('dotenv').config();

// Production-ready database configuration
const sequelize = new Sequelize({
  dialect: 'sqlite',
  storage: './database.sqlite',
  logging: process.env.ENABLE_SQL_LOGGING === 'true' ? console.log : false,

  // Enhanced connection pool for production
  pool: {
    max: 20,          // Maximum number of connections
    min: 5,           // Minimum number of connections
    acquire: 60000,   // Maximum time to get connection (60 seconds)
    idle: 30000,      // Maximum time connection can be idle (30 seconds)
    evict: 1000,      // Time interval to run eviction (1 second)
    handleDisconnects: true
  },

  // Production-ready options
  define: {
    timestamps: true,
    underscored: true,
    freezeTableName: true,
    paranoid: true,   // Enable soft deletes
    charset: 'utf8',
    collate: 'utf8_general_ci'
  },

  // Enhanced query options
  query: {
    raw: false,
    nest: false
  },

  // SQLite-specific options
  dialectOptions: {
    // Disable foreign key constraints during development if needed
    // foreignKeys: false
  },

  // Retry configuration
  retry: {
    max: 3,
    match: [
      /SQLITE_BUSY/,
      /SQLITE_LOCKED/,
      /database is locked/
    ]
  },

  // Benchmark queries in development
  benchmark: process.env.NODE_ENV === 'development'
});

// Test database connection with retry logic
const testConnection = async (retries = 3) => {
  for (let i = 0; i < retries; i++) {
    try {
      console.log(`🔄 Attempting database connection (attempt ${i + 1}/${retries})...`);
      await sequelize.authenticate();
      console.log('✅ Database connection established successfully.');
      return;
    } catch (error) {
      console.error(`❌ Database connection attempt ${i + 1} failed:`, error.message);

      if (i === retries - 1) {
        console.error('❌ All database connection attempts failed. Exiting...');
        process.exit(1);
      }

      // Wait before retry
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
};

module.exports = { sequelize, testConnection };
