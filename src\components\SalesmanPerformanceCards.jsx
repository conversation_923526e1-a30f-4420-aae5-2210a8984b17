import React, { useEffect, useState } from 'react';
import { formatCurrency } from '../utils/formatters';

const SalesmanPerformanceCards = () => {
  const [salesmenMetrics, setSalesmenMetrics] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchSalesmenMetrics = async () => {
      try {
        const response = await fetch('/api/commissions/summary');
        const data = await response.json();
        setSalesmenMetrics(data.data || []);
      } catch (error) {
        console.error('Failed to fetch salesman metrics:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchSalesmenMetrics();
  }, []);

  if (loading) {
    return <div className="flex justify-center p-8">Loading salesman metrics...</div>;
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
      {salesmenMetrics.map((salesman) => (
        <div 
          key={salesman.id} 
          className="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow"
        >
          <div className="flex items-center mb-4">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
              <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
            <div className="ml-4">
              <h3 className="text-xl font-semibold text-gray-800">{salesman.name}</h3>
              <p className="text-sm text-gray-500">Sales Representative</p>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="bg-blue-50 rounded-lg p-4">
              <p className="text-sm text-gray-600">Monthly Orders</p>              <p className="text-2xl font-bold text-blue-600">{salesman.totalOrders || 0}</p>
            </div>
            
            <div className="bg-green-50 rounded-lg p-4">
              <p className="text-sm text-gray-600">Total Sales</p>
              <p className="text-2xl font-bold text-green-600">
                {formatCurrency(salesman.totalSales || 0)}
              </p>
            </div>

            <div className="bg-purple-50 rounded-lg p-4 col-span-2">
              <p className="text-sm text-gray-600">Commission Earned</p>
              <p className="text-2xl font-bold text-purple-600">
                {formatCurrency(salesman.totalCommission || 0)}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                {salesman.month}
              </p>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default SalesmanPerformanceCards;