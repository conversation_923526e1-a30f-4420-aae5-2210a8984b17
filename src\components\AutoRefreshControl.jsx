import React from 'react';
import { PlayIcon, PauseIcon, ActivityIcon, ClockIcon } from 'lucide-react';

/**
 * Auto-refresh control component
 * Provides consistent UI for auto-refresh functionality across all pages
 */
const AutoRefreshControl = ({
  isAutoRefreshEnabled,
  isPaused,
  lastRefresh,
  toggleAutoRefresh,
  interval = 3000,
  className = '',
  showLastRefresh = true,
  size = 'md'
}) => {
  // Size classes
  const sizeClasses = {
    sm: 'text-xs px-2 py-1',
    md: 'text-sm px-3 py-2',
    lg: 'text-base px-4 py-3'
  };

  // Icon sizes
  const iconSizes = {
    sm: 12,
    md: 14,
    lg: 16
  };

  const formatLastRefresh = (date) => {
    const now = new Date();
    const diff = Math.floor((now - date) / 1000);
    
    if (diff < 60) return `${diff}s ago`;
    if (diff < 3600) return `${Math.floor(diff / 60)}m ago`;
    return date.toLocaleTimeString();
  };

  const getStatusColor = () => {
    if (!isAutoRefreshEnabled) return 'text-gray-500';
    if (isPaused) return 'text-yellow-600';
    return 'text-green-600';
  };

  const getStatusText = () => {
    if (!isAutoRefreshEnabled) return 'Disabled';
    if (isPaused) return 'Paused';
    return 'Active';
  };

  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      {/* Auto-refresh toggle */}
      <button
        onClick={toggleAutoRefresh}
        className={`
          inline-flex items-center space-x-2 rounded-md font-medium
          transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
          ${sizeClasses[size]}
          ${isAutoRefreshEnabled 
            ? 'bg-green-100 text-green-800 hover:bg-green-200' 
            : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
          }
        `}
        title={`${isAutoRefreshEnabled ? 'Disable' : 'Enable'} auto-refresh`}
      >
        {isAutoRefreshEnabled ? (
          <PauseIcon size={iconSizes[size]} />
        ) : (
          <PlayIcon size={iconSizes[size]} />
        )}
        <span>Auto-refresh</span>
      </button>

      {/* Status indicator */}
      <div className="flex items-center space-x-1">
        <ActivityIcon 
          size={iconSizes[size]} 
          className={`${getStatusColor()} ${isAutoRefreshEnabled && !isPaused ? 'animate-pulse' : ''}`} 
        />
        <span className={`${sizeClasses[size]} font-medium ${getStatusColor()}`}>
          {getStatusText()}
        </span>
      </div>

      {/* Interval display */}
      <div className="flex items-center space-x-1 text-gray-500">
        <ClockIcon size={iconSizes[size]} />
        <span className={sizeClasses[size]}>
          {interval / 1000}s
        </span>
      </div>

      {/* Last refresh time */}
      {showLastRefresh && lastRefresh && (
        <div className={`text-gray-500 ${sizeClasses[size]}`}>
          Last: {formatLastRefresh(lastRefresh)}
        </div>
      )}
    </div>
  );
};

export default AutoRefreshControl;
