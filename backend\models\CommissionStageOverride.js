const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const CommissionStageOverride = sequelize.define('CommissionStageOverride', {
  override_id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  stage_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'commission_stages',
      key: 'stage_id'
    }
  },
  salesman_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'salesmen',
      key: 'salesman_id'
    }
  },
  commission_percentage: {
    type: DataTypes.DECIMAL(5, 4),
    allowNull: false,
    validate: {
      min: 0,
      max: 1
    },
    comment: 'Commission percentage as decimal (0.05 = 5%)'
  },
  effective_date: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  end_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_by: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'user_id'
    }
  },
  updated_by: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'users',
      key: 'user_id'
    }
  }
}, {
  tableName: 'commission_stage_overrides',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      unique: true,
      fields: ['stage_id', 'salesman_id'],
      name: 'unique_stage_salesman_override'
    },
    {
      fields: ['salesman_id'],
      name: 'idx_override_salesman'
    },
    {
      fields: ['stage_id'],
      name: 'idx_override_stage'
    },
    {
      fields: ['is_active'],
      name: 'idx_override_active'
    }
  ]
});

// Define associations
CommissionStageOverride.associate = (models) => {
  // Override belongs to a commission stage
  CommissionStageOverride.belongsTo(models.CommissionStage, {
    foreignKey: 'stage_id',
    as: 'stage'
  });

  // Override belongs to a salesman
  CommissionStageOverride.belongsTo(models.Salesman, {
    foreignKey: 'salesman_id',
    as: 'salesman'
  });

  // Override created by user
  CommissionStageOverride.belongsTo(models.User, {
    foreignKey: 'created_by',
    as: 'creator'
  });

  // Override updated by user
  CommissionStageOverride.belongsTo(models.User, {
    foreignKey: 'updated_by',
    as: 'updater'
  });
};

module.exports = CommissionStageOverride;
