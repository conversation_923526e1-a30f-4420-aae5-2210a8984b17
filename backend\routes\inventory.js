const express = require('express');
const router = express.Router();
const {
  getTransactions,
  createTransaction,
  updateStock
} = require('../controllers/inventoryController');
const { validateUUID } = require('../middleware/validation');
const { authenticateToken, adminOnly, adminOrSalesman } = require('../middleware/auth');
const { body } = require('express-validator');

// All routes require authentication
router.use(authenticateToken);

// Validation for inventory transactions
const validateTransaction = [
  body('type')
    .isIn(['in', 'out', 'adjustment', 'transfer'])
    .withMessage('Invalid transaction type'),
  body('product_id')
    .isUUID()
    .withMessage('Valid product ID is required'),
  body('quantity')
    .isInt({ min: 1 })
    .withMessage('Quantity must be a positive integer'),
  body('unit_price')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Unit price must be a positive number')
];

// Validation for stock updates
const validateStockUpdate = [
  body('new_stock')
    .isInt({ min: 0 })
    .withMessage('New stock must be a non-negative integer'),
  body('transaction_type')
    .optional()
    .isIn(['adjustment', 'sale', 'purchase', 'transfer'])
    .withMessage('Invalid transaction type')
];

// GET routes (accessible by admin and salesman)
router.get('/transactions', adminOrSalesman, getTransactions);

// POST routes (admin only for manual transactions)
router.post('/transactions', adminOnly, validateTransaction, createTransaction);

// PUT routes (admin only for stock adjustments)
router.put('/products/:productId/stock', adminOnly, validateUUID('productId'), validateStockUpdate, updateStock);

module.exports = router;
