import { useState, Fragment } from 'react';
import { DollarSignIcon, UserIcon, FilterIcon, ChevronLeftIcon, ChevronRightIcon, DownloadIcon, PrinterIcon, ClockIcon, PlusIcon, UserPlusIcon, CreditCardIcon } from 'lucide-react';
import { useData } from '../contexts/DataContext';
import RefreshButton from '../components/RefreshButton';
import useAutoRefresh from '../hooks/useAutoRefresh';
import AutoRefreshControl from '../components/AutoRefreshControl';
import useDataRefresh from '../hooks/useDataRefresh';
import AddEmployeeModal from '../components/AddEmployeeModal';
import SalaryAssignmentModal from '../components/SalaryAssignmentModal';
import PaymentProcessingModal from '../components/PaymentProcessingModal';

const FinancesSalaries = () => {
  console.log('🔍 FinancesSalaries component is rendering...');

  const {
    employeesData,
    months,
    years,
    refreshAllData,
    refreshOrders,
    refreshCustomers,
    // ENHANCEMENT: Employee Management System
    employees,
    salaryPayments,
    addEmployee,
    assignSalary,
    processSalaryPayments,
    refreshEmployees
  } = useData();

  console.log('💰 Salary data:', { employeesData, months, years });

  const [filters, setFilters] = useState({
    month: new Date().getMonth() + 1,
    year: new Date().getFullYear(),
    employee: '',
    status: ''
  });
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [processingPayment, setProcessingPayment] = useState(false);

  // ENHANCEMENT: Employee Management Modal States
  const [showAddEmployeeModal, setShowAddEmployeeModal] = useState(false);
  const [showSalaryAssignmentModal, setShowSalaryAssignmentModal] = useState(false);
  const [selectedEmployeeForSalary, setSelectedEmployeeForSalary] = useState(null);
  const [loading, setLoading] = useState(false);

  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({ ...prev, [name]: value }));
  };

  // ENHANCEMENT: Comprehensive refresh function for Finances Salaries
  const handleRefreshData = async () => {
    try {
      console.log('🔄 Refreshing Finances Salaries data...');
      // Refresh all relevant data for salaries page including employee management
      await Promise.all([
        refreshOrders(),
        refreshCustomers(),
        refreshEmployees()
      ]);
      console.log('✅ Finances Salaries data refreshed successfully');
    } catch (error) {
      console.error('❌ Failed to refresh Finances Salaries data:', error);
      throw error; // Re-throw to let RefreshButton handle the error
    }
  };

  // ENHANCEMENT: Auto-refresh functionality
  const {
    isAutoRefreshEnabled,
    isPaused,
    lastRefresh,
    toggleAutoRefresh,
    manualRefresh
  } = useAutoRefresh(handleRefreshData, {
    interval: 3000, // 3 seconds
    enabled: true,
    pauseOnInteraction: true,
    interactionTimeout: 5000 // 5 seconds
  });

  // ENHANCEMENT: Auto-refresh after CRUD operations
  const { refreshAfterPaymentCreate } = useDataRefresh();

  const handleView = (id) => {
    setSelectedEmployee(id === selectedEmployee ? null : id);
  };

  const handleProcessPayment = () => {
    setProcessingPayment(true);
    setTimeout(() => {
      setProcessingPayment(false);
      setShowPaymentModal(false);
    }, 2000);
  };

  // ENHANCEMENT: Employee Management Handler Functions
  const handleAddEmployee = async (employeeData) => {
    try {
      setLoading(true);
      console.log('👤 Adding new employee:', employeeData);

      await addEmployee(employeeData);

      setShowAddEmployeeModal(false);
      console.log('✅ Employee added successfully');
    } catch (error) {
      console.error('❌ Failed to add employee:', error);
      alert('Failed to add employee: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleAssignSalary = async (salaryData) => {
    try {
      setLoading(true);
      console.log('💰 Assigning salary:', salaryData);

      await assignSalary(salaryData);

      setShowSalaryAssignmentModal(false);
      setSelectedEmployeeForSalary(null);
      console.log('✅ Salary assigned successfully');
    } catch (error) {
      console.error('❌ Failed to assign salary:', error);
      alert('Failed to assign salary: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleProcessSalaryPayments = async (paymentRequests) => {
    try {
      setLoading(true);
      console.log('💳 Processing salary payments:', paymentRequests.length, 'payments');

      await processSalaryPayments(paymentRequests);

      setShowPaymentModal(false);
      console.log('✅ Salary payments processed successfully');
    } catch (error) {
      console.error('❌ Failed to process salary payments:', error);
      alert('Failed to process salary payments: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleOpenSalaryAssignment = (employee) => {
    setSelectedEmployeeForSalary(employee);
    setShowSalaryAssignmentModal(true);
  };

  // Use fallback data if context data is not available
  const currentEmployeesData = employeesData || [];
  const currentMonths = months || [
    { value: 1, label: 'January' },
    { value: 2, label: 'February' },
    { value: 3, label: 'March' },
    { value: 4, label: 'April' },
    { value: 5, label: 'May' },
    { value: 6, label: 'June' },
    { value: 7, label: 'July' },
    { value: 8, label: 'August' },
    { value: 9, label: 'September' },
    { value: 10, label: 'October' },
    { value: 11, label: 'November' },
    { value: 12, label: 'December' }
  ];
  const currentYears = years || [
    { value: 2023, label: '2023' },
    { value: 2022, label: '2022' },
    { value: 2021, label: '2021' }
  ];

  const getStatusBadgeClass = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'paid':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Employee Salaries</h1>
          <p className="text-gray-600">Manage and process employee salary payments</p>
        </div>
        <div className="flex items-center space-x-4">
          <AutoRefreshControl
            isAutoRefreshEnabled={isAutoRefreshEnabled}
            isPaused={isPaused}
            lastRefresh={lastRefresh}
            toggleAutoRefresh={toggleAutoRefresh}
            interval={3000}
            size="sm"
          />
          <RefreshButton
            onRefresh={manualRefresh}
            variant="outline"
            size="md"
            label="Refresh Now"
          />
          <button
            className="flex items-center bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
            onClick={() => setShowAddEmployeeModal(true)}
          >
            <UserPlusIcon size={16} className="mr-2" />
            Add New Employee
          </button>
          <button className="flex items-center bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200">
            <PrinterIcon size={16} className="mr-2" />
            Print Payslips
          </button>
          <button className="flex items-center bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700" onClick={() => setShowPaymentModal(true)}>
            <CreditCardIcon size={16} className="mr-2" />
            Process All Payments
          </button>
        </div>
      </div>
      {/* Filters */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="flex items-center mb-4">
          <FilterIcon size={20} className="text-blue-600 mr-2" />
          <h2 className="text-lg font-medium text-gray-800">Filters</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <label htmlFor="month" className="block text-sm font-medium text-gray-700 mb-1">Month</label>
            <select id="month" name="month" value={filters.month} onChange={handleFilterChange} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              {currentMonths.map(month => (
                <option key={month.value} value={month.value}>{month.label}</option>
              ))}
            </select>
          </div>
          <div>
            <label htmlFor="year" className="block text-sm font-medium text-gray-700 mb-1">Year</label>
            <select id="year" name="year" value={filters.year} onChange={handleFilterChange} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              {currentYears.map(year => (
                <option key={year.value} value={year.value}>{year.label}</option>
              ))}
            </select>
          </div>
          <div>
            <label htmlFor="employee" className="block text-sm font-medium text-gray-700 mb-1">Employee</label>
            <select id="employee" name="employee" value={filters.employee} onChange={handleFilterChange} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="">All Employees</option>
              {currentEmployeesData.map(employee => (
                <option key={employee.id} value={employee.id}>{employee.name}</option>
              ))}
            </select>
          </div>
          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <select id="status" name="status" value={filters.status} onChange={handleFilterChange} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="">All Statuses</option>
              <option value="pending">Pending</option>
              <option value="paid">Paid</option>
            </select>
          </div>
        </div>
        <div className="flex justify-end mt-4">
          <button className="px-4 py-2 mr-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50" onClick={() => setFilters({ month: new Date().getMonth() + 1, year: new Date().getFullYear(), employee: '', status: '' })}>
            Clear Filters
          </button>
          <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Apply Filters</button>
        </div>
      </div>
      {/* Salary Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-lg font-medium text-gray-800">Total Salary</h3>
            <DollarSignIcon size={20} className="text-green-600" />
          </div>
          <p className="text-3xl font-bold text-gray-900">${currentEmployeesData.reduce((sum, emp) => sum + emp.totalSalary, 0).toFixed(2)}</p>
          <p className="text-sm text-gray-600 mt-2">For {currentMonths.find(m => m.value === filters.month)?.label} {filters.year}</p>
        </div>
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-lg font-medium text-gray-800">Basic Salary</h3>
            <DollarSignIcon size={20} className="text-blue-600" />
          </div>
          <p className="text-3xl font-bold text-gray-900">${currentEmployeesData.reduce((sum, emp) => sum + emp.basicSalary, 0).toFixed(2)}</p>
          <p className="text-sm text-gray-600 mt-2">For {currentEmployeesData.length} employees</p>
        </div>
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-lg font-medium text-gray-800">Commission</h3>
            <DollarSignIcon size={20} className="text-purple-600" />
          </div>
          <p className="text-3xl font-bold text-gray-900">${currentEmployeesData.reduce((sum, emp) => sum + emp.commission, 0).toFixed(2)}</p>
          <p className="text-sm text-gray-600 mt-2">Based on sales performance</p>
        </div>
      </div>
      {/* Salaries Table */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <h2 className="text-lg font-medium text-gray-800">Employee Salaries</h2>
          <button className="flex items-center text-blue-600 hover:text-blue-800">
            <DownloadIcon size={16} className="mr-1" />
            <span className="text-sm">Export Excel</span>
          </button>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Position</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Basic Salary</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Commission</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {currentEmployeesData.map(employee => (
                <Fragment key={employee.id}>
                  <tr>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                            <UserIcon size={20} className="text-gray-500" />
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{employee.name}</div>
                          <div className="text-sm text-gray-500">ID: EMP-{employee.id.toString().padStart(3, '0')}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{employee.position || 'N/A'}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${(employee.basicSalary || 0).toFixed(2)}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${(employee.commission || 0).toFixed(2)}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${(employee.totalSalary || 0).toFixed(2)}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(employee.status)}`}>{employee.status.charAt(0).toUpperCase() + employee.status.slice(1)}</span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button className="text-blue-600 hover:text-blue-900" onClick={() => handleView(employee.id)}>
                          View Details
                        </button>
                        <button
                          className="text-purple-600 hover:text-purple-900"
                          onClick={() => handleOpenSalaryAssignment(employee)}
                        >
                          Assign Salary
                        </button>
                        {employee.status === 'pending' && (
                          <button className="text-green-600 hover:text-green-900">Pay Now</button>
                        )}
                      </div>
                    </td>
                  </tr>
                  {selectedEmployee === employee.id && (
                    <tr>
                      <td colSpan={7} className="px-6 py-4 bg-gray-50">
                        <div className="border rounded-md overflow-hidden">
                          <div className="bg-gray-100 px-4 py-2 font-medium">Salary Details</div>
                          <div className="p-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                              <h4 className="text-sm font-medium text-gray-700 mb-2">Payment Information</h4>
                              <p className="text-sm mb-1"><span className="font-medium">Bank:</span> {employee.bankName || 'N/A'}</p>
                              <p className="text-sm mb-1"><span className="font-medium">Account:</span> {employee.bankAccount || 'N/A'}</p>
                              <p className="text-sm"><span className="font-medium">Method:</span> {employee.paymentMethod || 'N/A'}</p>
                            </div>
                            <div>
                              <h4 className="text-sm font-medium text-gray-700 mb-2">Attendance</h4>
                              <div className="flex space-x-4">
                                <div className="text-center">
                                  <span className="block text-sm font-medium text-green-600">{employee.attendance?.present || 0}</span>
                                  <span className="text-xs text-gray-500">Present</span>
                                </div>
                                <div className="text-center">
                                  <span className="block text-sm font-medium text-red-600">{employee.attendance?.absent || 0}</span>
                                  <span className="text-xs text-gray-500">Absent</span>
                                </div>
                                <div className="text-center">
                                  <span className="block text-sm font-medium text-blue-600">{employee.attendance?.leave || 0}</span>
                                  <span className="text-xs text-gray-500">Leave</span>
                                </div>
                                <div className="text-center">
                                  <span className="block text-sm font-medium text-yellow-600">{employee.attendance?.late || 0}</span>
                                  <span className="text-xs text-gray-500">Late</span>
                                </div>
                              </div>
                            </div>
                            <div>
                              <h4 className="text-sm font-medium text-gray-700 mb-2">Deductions</h4>
                              <p className="text-sm mb-1"><span className="font-medium">Tax:</span> ${(employee.deductions?.tax || 0).toFixed(2)}</p>
                              <p className="text-sm mb-1"><span className="font-medium">Insurance:</span> ${(employee.deductions?.insurance || 0).toFixed(2)}</p>
                              <p className="text-sm"><span className="font-medium">Other:</span> ${(employee.deductions?.other || 0).toFixed(2)}</p>
                            </div>
                          </div>
                          <div className="bg-gray-50 p-4 border-t border-gray-200">
                            <div className="flex justify-between items-center">
                              <div>
                                <p className="text-sm font-medium">Net Salary:</p>
                                <p className="text-lg font-bold text-green-600">${((employee.totalSalary || 0) - ((employee.deductions?.tax || 0) + (employee.deductions?.insurance || 0) + (employee.deductions?.other || 0))).toFixed(2)}</p>
                              </div>
                              <div className="flex space-x-2">
                                <button className="px-3 py-1 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 flex items-center">
                                  <PrinterIcon size={14} className="mr-1" />
                                  Print
                                </button>
                                {employee.status === 'pending' && (
                                  <button className="px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center">
                                    <DollarSignIcon size={14} className="mr-1" />
                                    Process Payment
                                  </button>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                  )}
                </Fragment>
              ))}
            </tbody>
          </table>
        </div>
        {/* Pagination */}
        <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing <span className="font-medium">1</span> to <span className="font-medium">5</span> of <span className="font-medium">8</span> employees
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50" disabled={currentPage === 1} onClick={() => setCurrentPage(currentPage - 1)}>
                  <span className="sr-only">Previous</span>
                  <ChevronLeftIcon size={16} />
                </button>
                <button className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-blue-600 hover:bg-gray-50">1</button>
                <button className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50" onClick={() => setCurrentPage(currentPage + 1)}>
                  <span className="sr-only">Next</span>
                  <ChevronRightIcon size={16} />
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
      {/* ENHANCEMENT: Employee Management Modals */}

      {/* Add Employee Modal */}
      <AddEmployeeModal
        isOpen={showAddEmployeeModal}
        onClose={() => setShowAddEmployeeModal(false)}
        onSubmit={handleAddEmployee}
        loading={loading}
      />

      {/* Salary Assignment Modal */}
      <SalaryAssignmentModal
        isOpen={showSalaryAssignmentModal}
        onClose={() => {
          setShowSalaryAssignmentModal(false);
          setSelectedEmployeeForSalary(null);
        }}
        onSubmit={handleAssignSalary}
        employee={selectedEmployeeForSalary}
        loading={loading}
      />

      {/* Payment Processing Modal */}
      <PaymentProcessingModal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        employees={currentEmployeesData}
        onProcessPayments={handleProcessSalaryPayments}
        loading={loading}
      />
    </div>
  );
};

export default FinancesSalaries;
