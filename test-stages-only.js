// Test commission stages endpoint only
import fetch from 'node-fetch';

const API_BASE_URL = 'http://localhost:3001/api';

async function testStagesOnly() {
  try {
    console.log('🔐 Authenticating...');
    
    // Step 1: Authenticate as admin
    const authResponse = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Admin123!'
      })
    });

    const authResult = await authResponse.json();
    
    if (!authResult.success) {
      console.error('❌ Authentication failed:', authResult.message);
      return;
    }

    const token = authResult.data.token;
    console.log('✅ Authentication successful');

    // Step 2: Test commission stages endpoint
    console.log('\n📊 Testing commission stages endpoint...');
    
    const stagesResponse = await fetch(`${API_BASE_URL}/commissions/stages`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    console.log('Stages response status:', stagesResponse.status);
    
    const stagesResult = await stagesResponse.json();
    console.log('Stages result:', JSON.stringify(stagesResult, null, 2));

    if (stagesResult.success && stagesResult.data) {
      console.log(`\n📊 Found ${stagesResult.data.length} commission stages:`);
      stagesResult.data.forEach(stage => {
        console.log(`   - ${stage.stage_name} (Stage ${stage.stage_number}): ${(stage.commission_percentage * 100).toFixed(1)}% commission, $${stage.bonus_amount} bonus`);
      });
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testStagesOnly();
