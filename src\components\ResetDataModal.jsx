import React, { useState } from 'react';
import {
  AlertTriangleIcon,
  TrashIcon,
  XIcon,
  LoaderIcon,
  ShieldIcon,
  DatabaseIcon
} from 'lucide-react';

function ResetDataModal({
  isOpen,
  onClose,
  resetTarget,
  onConfirm,
  loading,
  recordCount = 0
}) {
  const [confirmationText, setConfirmationText] = useState('');
  const [step, setStep] = useState(1); // 1: Warning, 2: Confirmation, 3: Final Warning

  const isNuclearReset = resetTarget === 'all';
  const requiredText = isNuclearReset ? 'DELETE ALL DATA' : 'DELETE';
  const isConfirmationValid = confirmationText === requiredText;

  const handleNext = () => {
    if (step < 3) {
      setStep(step + 1);
    }
  };

  const handleConfirm = () => {
    if (isConfirmationValid) {
      onConfirm(confirmationText);
      setConfirmationText('');
      setStep(1);
    }
  };

  const handleClose = () => {
    setConfirmationText('');
    setStep(1);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <div className={`p-2 rounded-lg ${isNuclearReset ? 'bg-red-500' : 'bg-orange-500'} text-white mr-3`}>
              {isNuclearReset ? <ShieldIcon size={20} /> : <TrashIcon size={20} />}
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {isNuclearReset ? 'Nuclear Reset' : `Reset ${resetTarget} Table`}
              </h3>
              <p className="text-sm text-gray-600">Step {step} of 3</p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600"
            disabled={loading}
          >
            <XIcon size={20} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {step === 1 && (
            <div className="space-y-4">
              <div className="flex items-center p-4 bg-red-50 rounded-lg">
                <AlertTriangleIcon size={24} className="text-red-500 mr-3" />
                <div>
                  <h4 className="font-medium text-red-800">Permanent Data Deletion</h4>
                  <p className="text-sm text-red-700">
                    This action cannot be undone and will permanently delete data.
                  </p>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium text-gray-900">What will be deleted:</h4>
                {isNuclearReset ? (
                  <ul className="text-sm text-gray-700 space-y-1">
                    <li>• All customer records and order history</li>
                    <li>• All product catalog and inventory data</li>
                    <li>• All sales orders and payment records</li>
                    <li>• All financial transactions and expenses</li>
                    <li>• All salesman and supplier information</li>
                    <li className="font-medium text-red-600">• Everything except user accounts</li>
                  </ul>
                ) : (
                  <div className="text-sm text-gray-700">
                    <p>• <strong>{recordCount}</strong> records from the <strong>{resetTarget}</strong> table</p>
                    <p>• All related data that depends on this table</p>
                  </div>
                )}
              </div>

              <div className="p-4 bg-yellow-50 rounded-lg">
                <h4 className="font-medium text-yellow-800">⚠️ Important Notes:</h4>
                <ul className="text-sm text-yellow-700 mt-2 space-y-1">
                  <li>• No backup will be created automatically</li>
                  <li>• Related data in other tables may also be affected</li>
                  <li>• This operation cannot be reversed</li>
                  {!isNuclearReset && <li>• Foreign key constraints will be handled properly</li>}
                </ul>
              </div>
            </div>
          )}

          {step === 2 && (
            <div className="space-y-4">
              <div className="text-center">
                <DatabaseIcon size={48} className="mx-auto text-red-500 mb-4" />
                <h4 className="text-lg font-medium text-gray-900 mb-2">
                  Are you absolutely sure?
                </h4>
                <p className="text-sm text-gray-600">
                  {isNuclearReset
                    ? 'This will permanently delete ALL business data from the system.'
                    : `This will permanently delete all ${recordCount} records from the ${resetTarget} table.`
                  }
                </p>
              </div>

              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <h4 className="font-medium text-red-800 mb-2">Data Recovery</h4>
                <p className="text-sm text-red-700">
                  Once deleted, this data cannot be recovered unless you have a separate backup.
                  Consider exporting the data first if you might need it later.
                </p>
              </div>
            </div>
          )}

          {step === 3 && (
            <div className="space-y-4">
              <div className="text-center">
                <div className="p-4 bg-red-100 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                  <AlertTriangleIcon size={32} className="text-red-600" />
                </div>
                <h4 className="text-lg font-medium text-gray-900 mb-2">
                  Final Confirmation Required
                </h4>
                <p className="text-sm text-gray-600 mb-4">
                  Type <strong className="text-red-600">{requiredText}</strong> to confirm deletion:
                </p>
              </div>

              <div>
                <input
                  type="text"
                  value={confirmationText}
                  onChange={(e) => setConfirmationText(e.target.value)}
                  placeholder={`Type "${requiredText}" here`}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500 text-center font-mono"
                  disabled={loading}
                />
              </div>

              {confirmationText && !isConfirmationValid && (
                <p className="text-sm text-red-600 text-center">
                  Please type exactly: <strong>{requiredText}</strong>
                </p>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-between p-6 border-t border-gray-200">
          <button
            onClick={step === 1 ? handleClose : () => setStep(step - 1)}
            className="px-4 py-2 text-gray-600 hover:text-gray-800"
            disabled={loading}
          >
            {step === 1 ? 'Cancel' : 'Back'}
          </button>

          <div className="flex space-x-3">
            {step < 3 ? (
              <button
                onClick={handleNext}
                className="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700"
                disabled={loading}
              >
                Continue
              </button>
            ) : (
              <button
                onClick={handleConfirm}
                disabled={!isConfirmationValid || loading}
                className={`px-4 py-2 rounded-md flex items-center ${
                  isConfirmationValid && !loading
                    ? 'bg-red-600 text-white hover:bg-red-700'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                {loading ? (
                  <>
                    <LoaderIcon size={16} className="animate-spin mr-2" />
                    Deleting...
                  </>
                ) : (
                  <>
                    <TrashIcon size={16} className="mr-2" />
                    {isNuclearReset ? 'Delete All Data' : 'Delete Table Data'}
                  </>
                )}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default ResetDataModal;
