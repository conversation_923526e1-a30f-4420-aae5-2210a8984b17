import { useState } from 'react';
import { XIcon, DollarSignIcon, CalendarIcon, PlusIcon, MinusIcon } from 'lucide-react';

const SalaryAssignmentModal = ({ isOpen, onClose, onSubmit, employee, loading = false }) => {
  const [formData, setFormData] = useState({
    base_salary: '',
    allowances: {
      housing: '',
      transport: '',
      medical: '',
      food: '',
      other: ''
    },
    bonuses: {
      performance: '',
      annual: '',
      project: '',
      other: ''
    },
    deductions: {
      tax: '',
      insurance: '',
      pension: '',
      loan: '',
      other: ''
    },
    currency: 'USD',
    pay_frequency: 'monthly',
    effective_date: new Date().toISOString().split('T')[0],
    overtime_rate: '1.5',
    notes: ''
  });

  const [errors, setErrors] = useState({});

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    
    if (name.includes('.')) {
      const [section, field] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [section]: {
          ...prev[section],
          [field]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.base_salary || parseFloat(formData.base_salary) <= 0) {
      newErrors.base_salary = 'Base salary must be greater than 0';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    // Convert string values to numbers for allowances, bonuses, and deductions
    const processedData = {
      ...formData,
      // CRITICAL FIX: Use the correct employee ID field
      employee_id: employee?.id || employee?.employee_id || employee?.employeeId,
      base_salary: parseFloat(formData.base_salary),
      allowances: Object.fromEntries(
        Object.entries(formData.allowances).map(([key, value]) => [key, parseFloat(value) || 0])
      ),
      bonuses: Object.fromEntries(
        Object.entries(formData.bonuses).map(([key, value]) => [key, parseFloat(value) || 0])
      ),
      deductions: Object.fromEntries(
        Object.entries(formData.deductions).map(([key, value]) => [key, parseFloat(value) || 0])
      ),
      overtime_rate: parseFloat(formData.overtime_rate)
    };

    // Debug logging to help identify the issue
    console.log('🔍 SalaryAssignmentModal Debug:', {
      employee: employee,
      employeeId: employee?.id,
      employeeUUID: employee?.employee_id,
      processedEmployeeId: processedData.employee_id,
      processedData: processedData
    });
    
    onSubmit(processedData);
  };

  const handleClose = () => {
    setFormData({
      base_salary: '',
      allowances: {
        housing: '',
        transport: '',
        medical: '',
        food: '',
        other: ''
      },
      bonuses: {
        performance: '',
        annual: '',
        project: '',
        other: ''
      },
      deductions: {
        tax: '',
        insurance: '',
        pension: '',
        loan: '',
        other: ''
      },
      currency: 'USD',
      pay_frequency: 'monthly',
      effective_date: new Date().toISOString().split('T')[0],
      overtime_rate: '1.5',
      notes: ''
    });
    setErrors({});
    onClose();
  };

  const calculateTotalSalary = () => {
    const base = parseFloat(formData.base_salary) || 0;
    const totalAllowances = Object.values(formData.allowances).reduce((sum, val) => sum + (parseFloat(val) || 0), 0);
    const totalBonuses = Object.values(formData.bonuses).reduce((sum, val) => sum + (parseFloat(val) || 0), 0);
    const totalDeductions = Object.values(formData.deductions).reduce((sum, val) => sum + (parseFloat(val) || 0), 0);
    
    return base + totalAllowances + totalBonuses - totalDeductions;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center">
            <DollarSignIcon size={20} className="mr-2 text-green-600" />
            Assign Salary - {employee?.name || 'Employee'}
          </h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-500 transition-colors"
          >
            <XIcon size={24} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6">
          {/* Basic Salary Information */}
          <div className="mb-8">
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <DollarSignIcon size={18} className="mr-2 text-green-600" />
              Basic Salary Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Base Salary *
                </label>
                <div className="relative">
                  <DollarSignIcon size={16} className="absolute left-3 top-3 text-gray-400" />
                  <input
                    type="number"
                    name="base_salary"
                    value={formData.base_salary}
                    onChange={handleInputChange}
                    className={`w-full pl-10 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 ${
                      errors.base_salary ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="0.00"
                    step="0.01"
                    min="0"
                  />
                </div>
                {errors.base_salary && (
                  <p className="text-red-500 text-xs mt-1">{errors.base_salary}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Pay Frequency
                </label>
                <select
                  name="pay_frequency"
                  value={formData.pay_frequency}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                >
                  <option value="weekly">Weekly</option>
                  <option value="bi_weekly">Bi-Weekly</option>
                  <option value="monthly">Monthly</option>
                  <option value="quarterly">Quarterly</option>
                  <option value="annually">Annually</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Effective Date
                </label>
                <div className="relative">
                  <CalendarIcon size={16} className="absolute left-3 top-3 text-gray-400" />
                  <input
                    type="date"
                    name="effective_date"
                    value={formData.effective_date}
                    onChange={handleInputChange}
                    className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Allowances */}
          <div className="mb-8">
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <PlusIcon size={18} className="mr-2 text-blue-600" />
              Allowances
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {Object.entries(formData.allowances).map(([key, value]) => (
                <div key={key}>
                  <label className="block text-sm font-medium text-gray-700 mb-1 capitalize">
                    {key} Allowance
                  </label>
                  <div className="relative">
                    <DollarSignIcon size={16} className="absolute left-3 top-3 text-gray-400" />
                    <input
                      type="number"
                      name={`allowances.${key}`}
                      value={value}
                      onChange={handleInputChange}
                      className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="0.00"
                      step="0.01"
                      min="0"
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Deductions */}
          <div className="mb-8">
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <MinusIcon size={18} className="mr-2 text-red-600" />
              Deductions
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {Object.entries(formData.deductions).map(([key, value]) => (
                <div key={key}>
                  <label className="block text-sm font-medium text-gray-700 mb-1 capitalize">
                    {key} Deduction
                  </label>
                  <div className="relative">
                    <DollarSignIcon size={16} className="absolute left-3 top-3 text-gray-400" />
                    <input
                      type="number"
                      name={`deductions.${key}`}
                      value={value}
                      onChange={handleInputChange}
                      className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                      placeholder="0.00"
                      step="0.01"
                      min="0"
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Salary Summary */}
          <div className="mb-8 p-4 bg-gray-50 rounded-lg">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Salary Summary</h3>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Base Salary:</span>
                <p className="font-medium">${(parseFloat(formData.base_salary) || 0).toFixed(2)}</p>
              </div>
              <div>
                <span className="text-gray-600">Total Allowances:</span>
                <p className="font-medium text-blue-600">
                  +${Object.values(formData.allowances).reduce((sum, val) => sum + (parseFloat(val) || 0), 0).toFixed(2)}
                </p>
              </div>
              <div>
                <span className="text-gray-600">Total Deductions:</span>
                <p className="font-medium text-red-600">
                  -${Object.values(formData.deductions).reduce((sum, val) => sum + (parseFloat(val) || 0), 0).toFixed(2)}
                </p>
              </div>
              <div>
                <span className="text-gray-600">Net Salary:</span>
                <p className="font-bold text-green-600 text-lg">${calculateTotalSalary().toFixed(2)}</p>
              </div>
            </div>
          </div>

          {/* Notes */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Notes
            </label>
            <textarea
              name="notes"
              value={formData.notes}
              onChange={handleInputChange}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
              placeholder="Additional notes about this salary assignment..."
            />
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:bg-green-400 flex items-center"
            >
              {loading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Assigning...
                </>
              ) : (
                'Assign Salary'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SalaryAssignmentModal;
