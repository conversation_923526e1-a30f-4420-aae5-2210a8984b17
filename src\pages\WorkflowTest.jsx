import { useState } from 'react';
import { useData } from '../contexts/DataContext';

const WorkflowTest = () => {
  const {
    products,
    customers,
    orders,
    addProduct,
    addCustomer,
    addOrder,
    updateOrderStatus
  } = useData();

  const [testResults, setTestResults] = useState([]);

  const addTestResult = (test, success, message) => {
    setTestResults(prev => [...prev, { test, success, message, timestamp: new Date().toLocaleTimeString() }]);
  };

  const runWorkflowTest = async () => {
    setTestResults([]);

    try {
      // Step 1: Add a product
      const product = await addProduct({
        name: 'Test Coca-Cola 500ml',
        category: 'Beverages',
        price: 1.50,
        stock: 100,
        supplier: 'Test Supplier'
      });
      addTestResult('Add Product', true, `Added product: ${product.name} (ID: ${product.id})`);

      // Step 2: Add a customer
      const customer = await addCustomer({
        name: 'Test Store',
        type: 'Convenience Store',
        contact: '<PERSON>',
        phone: '+****************',
        email: '<EMAIL>',
        address: '123 Test Street',
        creditLimit: 5000.00,
        paymentTerms: 'Net 30',
        paymentMethod: 'Credit'
      });
      addTestResult('Add Customer', true, `Added customer: ${customer.name} (ID: ${customer.id})`);

      // Step 3: Create an order
      const order = addOrder({
        customerId: customer.id,
        salesmanId: 1, // Default salesman
        date: new Date().toISOString().split('T')[0],
        items: [{
          productId: product.id,
          productName: product.name,
          quantity: 10,
          unitPrice: product.price,
          totalPrice: product.price * 10
        }],
        totalAmount: product.price * 10,
        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 30 days from now
      });
      addTestResult('Add Order', true, `Created order: ${order.orderNumber} (ID: ${order.id})`);

      // Step 4: Update order status
      updateOrderStatus(order.id, 'delivered');
      addTestResult('Update Order Status', true, `Updated order ${order.orderNumber} to delivered`);

      addTestResult('Complete Workflow', true, 'All workflow steps completed successfully!');

    } catch (error) {
      addTestResult('Workflow Error', false, `Error: ${error.message}`);
    }
  };

  const clearAllData = () => {
    // This would require additional functions in DataContext to clear data
    setTestResults([]);
    addTestResult('Clear Data', true, 'Test results cleared (Note: Data clearing not implemented yet)');
  };

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Clean Slate Workflow Testing</h1>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Test Controls */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-4">Test Controls</h2>

          <div className="space-y-4">
            <button
              onClick={runWorkflowTest}
              className="w-full bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            >
              Run Complete Workflow Test
            </button>

            <button
              onClick={clearAllData}
              className="w-full bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
            >
              Clear Test Results
            </button>
          </div>

          <div className="mt-6">
            <h3 className="font-medium mb-2">Current Data Summary:</h3>
            <div className="text-sm text-gray-600 space-y-1">
              <p>Products: {products.length}</p>
              <p>Customers: {customers.length}</p>
              <p>Orders: {orders.length}</p>
            </div>
          </div>
        </div>

        {/* Test Results */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-4">Test Results</h2>

          <div className="space-y-2 max-h-96 overflow-y-auto">
            {testResults.length === 0 ? (
              <p className="text-gray-500 italic">No tests run yet. Click "Run Complete Workflow Test" to start.</p>
            ) : (
              testResults.map((result, index) => (
                <div
                  key={index}
                  className={`p-3 rounded border-l-4 ${
                    result.success
                      ? 'bg-green-50 border-green-400 text-green-800'
                      : 'bg-red-50 border-red-400 text-red-800'
                  }`}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="font-medium">{result.test}</p>
                      <p className="text-sm">{result.message}</p>
                    </div>
                    <span className="text-xs opacity-75">{result.timestamp}</span>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      {/* Workflow Steps Guide */}
      <div className="mt-8 bg-blue-50 p-6 rounded-lg">
        <h2 className="text-lg font-semibold mb-4">Complete Business Workflow Testing Guide</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-white p-4 rounded border">
            <h3 className="font-medium text-blue-800 mb-2">Step 1: Setup (Admin)</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Add products via Inventory In</li>
              <li>• Add customers via Customers page</li>
              <li>• Verify data appears correctly</li>
            </ul>
          </div>

          <div className="bg-white p-4 rounded border">
            <h3 className="font-medium text-green-800 mb-2">Step 2: Sales (Salesman)</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Login as salesman</li>
              <li>• Create orders via Sales Orders</li>
              <li>• Select from added customers</li>
            </ul>
          </div>

          <div className="bg-white p-4 rounded border">
            <h3 className="font-medium text-orange-800 mb-2">Step 3: Fulfillment (Admin)</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• View orders as admin</li>
              <li>• Mark orders as delivered</li>
              <li>• Update inventory via Inventory Out</li>
            </ul>
          </div>

          <div className="bg-white p-4 rounded border">
            <h3 className="font-medium text-purple-800 mb-2">Step 4: Verification</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Check inventory levels</li>
              <li>• Verify order statuses</li>
              <li>• Confirm data flow</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WorkflowTest;
