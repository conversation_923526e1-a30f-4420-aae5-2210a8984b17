const { InventoryTransaction, Product, Supplier, Customer, User, Order, Salesman } = require('../models');
const { Op } = require('sequelize');

// Get all inventory transactions with robust error handling
const getTransactions = async (req, res, next) => {
  try {
    console.log('📦 Getting inventory transactions...');

    const {
      page = 1,
      limit = 50,
      type,
      product_id,
      date_from,
      date_to,
      status
    } = req.query;

    const offset = (page - 1) * limit;
    const where = {};

    // Filter by type
    if (type) {
      where.type = type;
    }

    // Filter by product
    if (product_id) {
      where.product_id = product_id;
    }

    // Filter by status
    if (status) {
      where.status = status;
    }

    // Date range filter
    if (date_from || date_to) {
      where.transaction_date = {};
      if (date_from) where.transaction_date[Op.gte] = new Date(date_from);
      if (date_to) where.transaction_date[Op.lte] = new Date(date_to);
    }

    console.log('📦 Query filters:', where);

    // Use a more robust query with error handling for associations
    let transactions = [];
    let count = 0;

    try {
      const result = await InventoryTransaction.findAndCountAll({
        where,
        include: [
          {
            model: Product,
            as: 'product',
            attributes: ['product_id', 'name', 'unit_price'],
            required: false // Allow transactions without products
          },
          {
            model: Supplier,
            as: 'supplier',
            attributes: ['supplier_id', 'name'],
            required: false
          },
          {
            model: Customer,
            as: 'customer',
            attributes: ['customer_id', 'name'],
            required: false
          },
          {
            model: User,
            as: 'recorder',
            attributes: ['user_id', 'email'],
            required: false // Allow transactions without user info
          },
          {
            model: Order,
            as: 'order',
            attributes: ['order_id', 'order_number', 'salesman_id'],
            required: false, // Allow transactions without orders
            include: [
              {
                model: Salesman,
                as: 'salesman',
                attributes: ['salesman_id', 'full_name'],
                required: false // Allow orders without salesmen (direct sales)
              }
            ]
          }
        ],
        limit: parseInt(limit),
        offset: parseInt(offset),
        order: [['transaction_date', 'DESC']]
      });

      transactions = result.rows;
      count = result.count;

    } catch (associationError) {
      console.warn('⚠️ Association query failed, falling back to simple query:', associationError.message);

      // Fallback to simple query without associations
      const result = await InventoryTransaction.findAndCountAll({
        where,
        limit: parseInt(limit),
        offset: parseInt(offset),
        order: [['transaction_date', 'DESC']]
      });

      transactions = result.rows;
      count = result.count;
    }

    console.log(`✅ Found ${count} inventory transactions`);

    res.json({
      success: true,
      data: {
        transactions,
        pagination: {
          current_page: parseInt(page),
          total_pages: Math.ceil(count / limit),
          total_items: count,
          items_per_page: parseInt(limit)
        }
      }
    });
  } catch (error) {
    console.error('❌ Error getting inventory transactions:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch inventory transactions',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
};

// Create inventory transaction with comprehensive error handling
const createTransaction = async (req, res, next) => {
  let dbTransaction;

  try {
    console.log('📦 Creating inventory transaction...');
    console.log('📦 Request body:', JSON.stringify(req.body, null, 2));

    dbTransaction = await require('../models').sequelize.transaction({
      isolationLevel: null // Remove isolation level for SQLite compatibility
    });

    const {
      type,
      product_id,
      quantity,
      unit_price,
      supplier_id,
      customer_id,
      reference_number,
      invoice_number,
      notes,
      batch_number,
      expiration_date,
      storage_location
    } = req.body;

    console.log('📦 Extracted fields:', { type, product_id, quantity, unit_price, supplier_id, customer_id });

    // Validate required fields
    if (!type || !product_id || !quantity) {
      await dbTransaction.rollback();
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: type, product_id, and quantity are required'
      });
    }

    // Validate transaction type
    if (!['in', 'out', 'adjustment', 'transfer'].includes(type)) {
      await dbTransaction.rollback();
      return res.status(400).json({
        success: false,
        message: 'Invalid transaction type. Must be: in, out, adjustment, or transfer'
      });
    }

    // Validate quantity
    if (quantity <= 0) {
      await dbTransaction.rollback();
      return res.status(400).json({
        success: false,
        message: 'Quantity must be greater than 0'
      });
    }

    // Validate product exists
    const product = await Product.findByPk(product_id, { transaction: dbTransaction });
    if (!product) {
      await dbTransaction.rollback();
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Validate supplier exists if supplier_id is provided
    if (supplier_id) {
      const supplier = await Supplier.findByPk(supplier_id, { transaction: dbTransaction });
      if (!supplier) {
        await dbTransaction.rollback();
        return res.status(404).json({
          success: false,
          message: 'Supplier not found'
        });
      }
    }

    // Validate customer exists if customer_id is provided
    if (customer_id) {
      const customer = await Customer.findByPk(customer_id, { transaction: dbTransaction });
      if (!customer) {
        await dbTransaction.rollback();
        return res.status(404).json({
          success: false,
          message: 'Customer not found'
        });
      }
    }



    // Calculate new stock level
    let newStock = product.current_stock || 0;
    if (type === 'in') {
      newStock += quantity;
    } else if (type === 'out') {
      newStock -= quantity;
      if (newStock < 0) {
        await dbTransaction.rollback();
        return res.status(400).json({
          success: false,
          message: `Insufficient stock. Available: ${product.current_stock}, Requested: ${quantity}`
        });
      }
    }

    console.log(`📦 Stock calculation: ${product.current_stock} → ${newStock}`);

    // Ensure we have a valid user ID
    const recordedBy = req.user?.user_id;
    if (!recordedBy) {
      await dbTransaction.rollback();
      return res.status(401).json({
        success: false,
        message: 'User authentication required for inventory transactions'
      });
    }

    console.log(`📦 Creating transaction for user: ${recordedBy}`);

    // Create transaction record
    const transaction = await InventoryTransaction.create({
      type,
      product_id,
      quantity,
      unit_price: unit_price || product.unit_price,
      total_cost: (unit_price || product.unit_price) * quantity,
      supplier_id: supplier_id || null,
      customer_id: customer_id || null,
      reference_number,
      invoice_number,
      notes,
      batch_number,
      expiration_date: expiration_date ? new Date(expiration_date) : null,
      storage_location,
      recorded_by: recordedBy,
      transaction_date: new Date(),
      status: 'completed'
    }, { transaction: dbTransaction });

    // Update product stock
    await product.update({
      current_stock: newStock,
      last_restocked: type === 'in' ? new Date() : product.last_restocked
    }, { transaction: dbTransaction });

    await dbTransaction.commit();
    console.log('✅ Inventory transaction created successfully');

    // Get created transaction with relations (with error handling)
    let createdTransaction = transaction;
    try {
      createdTransaction = await InventoryTransaction.findByPk(transaction.transaction_id, {
        include: [
          {
            model: Product,
            as: 'product',
            attributes: ['product_id', 'name', 'unit_price'],
            required: false
          },
          {
            model: Supplier,
            as: 'supplier',
            attributes: ['supplier_id', 'name'],
            required: false
          },
          {
            model: Customer,
            as: 'customer',
            attributes: ['customer_id', 'name'],
            required: false
          }
        ]
      });
    } catch (includeError) {
      console.warn('⚠️ Could not fetch transaction with relations:', includeError.message);
      // Use the basic transaction data
    }

    res.status(201).json({
      success: true,
      message: 'Inventory transaction created successfully',
      data: {
        transaction: createdTransaction,
        product: {
          id: product.product_id,
          name: product.name,
          old_stock: product.current_stock,
          new_stock: newStock
        }
      }
    });
  } catch (error) {
    console.error('❌ Error creating inventory transaction:', error);

    if (dbTransaction) {
      try {
        await dbTransaction.rollback();
      } catch (rollbackError) {
        console.error('❌ Error rolling back transaction:', rollbackError);
      }
    }

    res.status(500).json({
      success: false,
      message: 'Failed to create inventory transaction',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
};

// Update product stock
const updateStock = async (req, res, next) => {
  const dbTransaction = await require('../models').sequelize.transaction({
    isolationLevel: null // Remove isolation level for SQLite compatibility
  });

  try {
    const { productId } = req.params;
    const {
      new_stock,
      transaction_type = 'adjustment',
      notes,
      supplier_id,
      customer_id
    } = req.body;

    const product = await Product.findByPk(productId);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    const oldStock = product.current_stock;
    const stockDifference = new_stock - oldStock;

    if (stockDifference !== 0) {
      // Use frontend-provided transaction type if valid, otherwise determine from stock difference
      let transactionType = transaction_type;
      if (!['in', 'out', 'adjustment', 'transfer'].includes(transaction_type)) {
        transactionType = stockDifference > 0 ? 'in' : 'out';
      }

      const transactionNotes = notes || `Stock ${transaction_type}: ${oldStock} → ${new_stock}`;

      console.log(`📦 Creating ${transactionType} transaction: ${Math.abs(stockDifference)} units`);
      console.log(`📦 Additional data:`, { supplier_id, customer_id, notes });

      await InventoryTransaction.create({
        type: transactionType,
        product_id: productId,
        quantity: Math.abs(stockDifference),
        unit_price: req.body.unitPrice || product.unit_price,
        total_cost: Math.abs(stockDifference) * (req.body.unitPrice || product.unit_price),
        supplier_id: stockDifference > 0 ? supplier_id : null,
        customer_id: stockDifference < 0 ? customer_id : null,
        reference_number: req.body.referenceNumber || null,
        invoice_number: req.body.invoiceNumber || null,
        batch_number: req.body.batchNumber || null,
        expiration_date: req.body.expirationDate ? new Date(req.body.expirationDate) : null,
        storage_location: req.body.storageLocation || null,
        notes: transactionNotes,
        recorded_by: req.user.user_id,
        transaction_date: new Date(),
        status: 'completed'
      }, { transaction: dbTransaction });

      console.log(`✅ Transaction created: ${transactionType} ${Math.abs(stockDifference)} units`);
    }

    // Update product stock
    await product.update({
      current_stock: new_stock,
      last_restocked: stockDifference > 0 ? new Date() : product.last_restocked
    }, { transaction: dbTransaction });

    await dbTransaction.commit();

    res.json({
      success: true,
      message: 'Stock updated successfully',
      data: {
        product: {
          product_id: product.product_id,
          name: product.name,
          old_stock: oldStock,
          new_stock: new_stock,
          difference: stockDifference
        }
      }
    });
  } catch (error) {
    await dbTransaction.rollback();
    next(error);
  }
};

module.exports = {
  getTransactions,
  createTransaction,
  updateStock
};
