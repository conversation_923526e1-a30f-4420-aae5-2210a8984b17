'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('commission_stage_overrides', {
      override_id: {
        type: Sequelize.UUID,
        allowNull: false,
        primaryKey: true
      },
      stage_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'commission_stages',
          key: 'stage_id'
        },
        onDelete: 'CASCADE'
      },
      salesman_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'salesmen',
          key: 'salesman_id'
        },
        onDelete: 'CASCADE'
      },
      commission_percentage: {
        type: Sequelize.DECIMAL(5, 4),
        allowNull: false
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });
    await queryInterface.addConstraint('commission_stage_overrides', {
      fields: ['stage_id', 'salesman_id'],
      type: 'unique',
      name: 'unique_stage_salesman_override'
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('commission_stage_overrides');
  }
}; 