const { User } = require('./models');
const { testConnection } = require('./config/database');

async function createTestUser() {
  try {
    console.log('🔄 Connecting to database...');
    await testConnection();
    
    console.log('🔄 Creating test user...');
    
    // Delete existing test user if exists
    await User.destroy({ where: { email: '<EMAIL>' } });
    
    // Create new test user
    const user = await User.create({
      email: '<EMAIL>',
      password_hash: 'Admin123!', // Will be hashed by the model hook
      role: 'admin',
      email_verified: true, // Skip email verification for test user
      is_active: true
    });
    
    console.log('✅ Test user created successfully!');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: Admin123!');
    console.log('👤 Role: admin');
    console.log('✅ Email verified: true');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Failed to create test user:', error);
    process.exit(1);
  }
}

createTestUser();
