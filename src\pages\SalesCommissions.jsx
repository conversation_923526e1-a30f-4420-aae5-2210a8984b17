import React, { useState, useEffect, useCallback } from 'react';
import { PlusIcon, SettingsIcon, TrendingUpIcon } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { useData } from '../contexts/DataContext';
import CommissionMatrix from '../components/CommissionMatrix';
import CommissionStageModal from '../components/CommissionStageModal';
import RefreshButton from '../components/RefreshButton';
import AddCommissionStageModal from '../components/AddCommissionStageModal';
import isEqual from 'lodash.isequal';
import SalesmanPerformanceCards from '../components/SalesmanPerformanceCards';

function SalesCommissions() {
  const { user } = useAuth();
  const { salesmen, refreshAllData } = useData();

  // Local state for commission data
  const [commissionStages, setCommissionStages] = useState([]);
  const [commissionOverrides, setCommissionOverrides] = useState([]);
  const [loading, setLoading] = useState({
    stages: false,
    overrides: false,
    updating: false
  });
  const [error, setError] = useState(null);
  const [showManageStagesModal, setShowManageStagesModal] = useState(false);
  const [showAddStageModal, setShowAddStageModal] = useState(false);

  // Fetch commission stages
  const fetchCommissionStages = useCallback(async () => {
    try {
      setLoading(prev => ({ ...prev, stages: true }));
      setError(null);

      const token = localStorage.getItem('auth_token');
      const response = await fetch(`http://localhost:3001/api/commissions/stages?cb=${Date.now()}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        cache: 'no-store' // Force bypass cache
      });

      if (response.status === 304) {
        return;
      }
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          let newStages = [];
          if (result.data && Array.isArray(result.data.stages)) {
            newStages = result.data.stages;
          } else if (Array.isArray(result.data)) {
            newStages = result.data;
          }
          // Only update if changed
          if (!isEqual(newStages, commissionStages)) {
            setCommissionStages(newStages);
          }
        } else {
          setError(result.message || 'Failed to fetch commission stages');
        }
      } else {
        setError('Failed to fetch commission stages');
      }
    } catch (error) {
      console.error('Error fetching commission stages:', error);
      setError('Failed to fetch commission stages');
    } finally {
      setLoading(prev => ({ ...prev, stages: false }));
    }
  }, [commissionStages]);

  // Fetch commission overrides
  const fetchCommissionOverrides = useCallback(async (forceRefresh = false) => {
    try {
      setLoading(prev => ({ ...prev, overrides: true }));

      const token = localStorage.getItem('auth_token');
      // Add cache busting parameter for force refresh
      const url = forceRefresh
        ? `http://localhost:3001/api/commissions/overrides?_t=${Date.now()}`
        : 'http://localhost:3001/api/commissions/overrides';

      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const result = await response.json();
        console.log('📊 Commission overrides API response:', result);

        if (result.success) {
          const newOverrides = result.data || [];
          console.log('📊 Setting commission overrides:', newOverrides);

          // Always update if force refresh, otherwise check for changes
          if (forceRefresh || !isEqual(newOverrides, commissionOverrides)) {
            setCommissionOverrides(newOverrides);
            console.log('✅ Commission overrides updated in state');
          } else {
            console.log('📊 Commission overrides unchanged');
          }
        } else {
          console.error('❌ Commission overrides API returned error:', result);
        }
      } else {
        console.error('❌ Commission overrides API failed:', response.status, response.statusText);
      }
    } catch (error) {
      console.error('Error fetching commission overrides:', error);
    } finally {
      setLoading(prev => ({ ...prev, overrides: false }));
    }
  }, [commissionOverrides]);

  // Load initial data on component mount
  useEffect(() => {
    fetchCommissionStages();
    fetchCommissionOverrides();
  }, [fetchCommissionStages, fetchCommissionOverrides]);

  // Manual refresh function
  const manualRefresh = useCallback(async () => {
    await Promise.all([
      fetchCommissionStages(),
      fetchCommissionOverrides(),
      refreshAllData()
    ]);
  }, [fetchCommissionStages, fetchCommissionOverrides, refreshAllData]);

  // Handle commission override updates
  const handleUpdateOverride = useCallback(async (overrideData) => {
    try {
      setLoading(prev => ({ ...prev, updating: true }));
      console.log('💾 Saving commission override:', overrideData);

      const token = localStorage.getItem('auth_token');
      const response = await fetch('http://localhost:3001/api/commissions/overrides', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(overrideData)
      });

      const result = await response.json();
      console.log('💾 Override save response:', result);

      if (result.success) {
        console.log('✅ Override saved successfully, refreshing data...');

        // Force refresh overrides with cache busting
        await fetchCommissionOverrides(true);
        console.log('🔄 Commission overrides refreshed after save');

      } else {
        throw new Error(result.message || 'Failed to update commission override');
      }
    } catch (error) {
      console.error('Error updating commission override:', error);
      throw error; // Re-throw to let the component handle the error
    } finally {
      setLoading(prev => ({ ...prev, updating: false }));
    }
  }, [fetchCommissionOverrides]);

  // Handle global stage management
  const handleUpdateCommissionStages = useCallback(async (data) => {
    try {
      setLoading(prev => ({ ...prev, updating: true }));

      const token = localStorage.getItem('auth_token');
      const response = await fetch('http://localhost:3001/api/commissions/stages', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(data)
      });

      const result = await response.json();

      if (result.success) {
        setShowManageStagesModal(false);
        // Refresh data to show updated stages
        await fetchCommissionStages();
        await manualRefresh();
      } else {
        console.error('Failed to update commission stages:', result.message);
        alert(`Failed to update commission stages: ${result.message}`);
      }
    } catch (error) {
      console.error('Error updating commission stages:', error);
      alert('Failed to update commission stages. Please try again.');
    } finally {
      setLoading(prev => ({ ...prev, updating: false }));
    }
  }, [fetchCommissionStages, manualRefresh]);

  if (error) {
    return (
      <div className="space-y-6">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <h2 className="font-bold">Error Loading Commission Page</h2>
          <p>{error}</p>
          <button
            onClick={() => {
              setError(null);
              fetchCommissionStages();
              fetchCommissionOverrides();
            }}
            className="mt-2 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Professional Header with Analytics */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg shadow-lg text-white p-6">
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-3xl font-bold mb-2">Sales Commissions</h1>
            <p className="text-blue-100 text-lg">Global commission structure with per-salesman overrides</p>
          </div>
          <div className="flex items-center space-x-3">
            <RefreshButton
              onRefresh={manualRefresh}
              variant="outline"
              size="md"
              label="Refresh"
              className="border-white text-white hover:bg-white hover:text-blue-600"
            />
            {user?.role === 'admin' && (
              <>
                <button
                  onClick={() => setShowAddStageModal(true)}
                  className="flex items-center bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600 font-medium transition-colors shadow"
                >
                  <PlusIcon size={16} className="mr-2" />
                  Add Stage
                </button>
                <button
                  onClick={() => setShowManageStagesModal(true)}
                  className="flex items-center bg-white text-blue-600 px-4 py-2 rounded-md hover:bg-blue-50 font-medium transition-colors"
                >
                  <SettingsIcon size={16} className="mr-2" />
                  Manage Stages
                </button>
              </>
            )}
          </div>
        </div>

        {/* Analytics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-100 text-sm">Total Salesmen</p>
                <p className="text-2xl font-bold">{salesmen?.length || 0}</p>
              </div>
              <TrendingUpIcon size={24} className="text-blue-200" />
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-100 text-sm">Commission Stages</p>
                <p className="text-2xl font-bold">{commissionStages?.length || 0}</p>
              </div>
              <SettingsIcon size={24} className="text-blue-200" />
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-100 text-sm">Custom Overrides</p>
                <p className="text-2xl font-bold">{commissionOverrides?.length || 0}</p>
              </div>
              <PlusIcon size={24} className="text-blue-200" />
            </div>
          </div>
        </div>
      </div>

      {/* Commission Matrix */}
      <CommissionMatrix
        key={`commission-matrix-${commissionOverrides.length}-${Date.now()}`}
        stages={commissionStages}
        salesmen={salesmen}
        overrides={commissionOverrides}
        onUpdateOverride={handleUpdateOverride}
        loading={loading.stages || loading.overrides || loading.updating}
        user={user}
      />

      {/* Commission Stage Management Modal */}
      <CommissionStageModal
        isOpen={showManageStagesModal}
        onClose={() => setShowManageStagesModal(false)}
        onSubmit={handleUpdateCommissionStages}
        currentStages={commissionStages}
        loading={loading.updating}
      />

      {/* Add Commission Stage Modal */}
      <AddCommissionStageModal
        isOpen={showAddStageModal}
        onClose={() => setShowAddStageModal(false)}
        onSubmit={async (stageData) => {
          setLoading(prev => ({ ...prev, updating: true }));
          try {
            const token = localStorage.getItem('auth_token');
            const response = await fetch('http://localhost:3001/api/commissions/stages', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
              },
              body: JSON.stringify(stageData)
            });
            const result = await response.json();
            if (result.success) {
              setShowAddStageModal(false);
              await fetchCommissionStages();
            } else {
              alert(result.message || 'Failed to add commission stage');
            }
          } catch (error) {
            alert('Failed to add commission stage. Please try again.');
          } finally {
            setLoading(prev => ({ ...prev, updating: false }));
          }
        }}
        existingStages={commissionStages}
        loading={loading.updating}
      />
    </div>
  );
}

export default SalesCommissions;

const handleAddStage = async (newStage) => {
  try {
    setUpdating({ type: 'stage', status: true });
    const token = localStorage.getItem('auth_token');
    
    const response = await fetch('http://localhost:3001/api/commissions/stages', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(newStage)
    });

    const result = await response.json();
    
    if (!response.ok) throw new Error(result.message || 'Failed to create stage');

    setCommissionStages(prev => [...prev, result.data]);
    setShowAddStageModal(false);
    setNotification({ type: 'success', message: 'Stage created successfully!' });
  } catch (err) {
    setError(err.message);
    setNotification({ type: 'error', message: err.message });
  } finally {
    setUpdating({ type: 'stage', status: false });
  }
};
