import { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { MenuIcon, BellIcon, UserIcon } from 'lucide-react';
const Header = () => {
  const {
    user,
    logout
  } = useAuth();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  return (
    <header className="bg-white border-b border-gray-200">
      <div className="px-4 py-4 flex items-center justify-between">
        <div className="flex items-center md:hidden">
          <button onClick={() => setMobileMenuOpen(!mobileMenuOpen)} className="text-gray-600 hover:text-gray-900 focus:outline-none p-2 rounded-lg hover:bg-gray-100">
            <MenuIcon size={24} />
          </button>
        </div>
        <div className="flex-1 md:ml-4">
          <div className="relative max-w-xl">
            <input type="search" placeholder="Search..." className="w-full pl-4 pr-10 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
          </div>
        </div>
        <div className="flex items-center space-x-4">
          <button className="text-gray-600 hover:text-gray-900 relative p-2 rounded-lg hover:bg-gray-100">
            <BellIcon size={20} />
            <span className="absolute top-2 right-2 w-2 h-2 bg-red-500 rounded-full"></span>
          </button>
          <div className="relative">
            <button onClick={() => setShowUserMenu(!showUserMenu)} className="flex items-center space-x-3 text-sm text-gray-700 hover:text-gray-900 p-2 rounded-lg hover:bg-gray-100">
              <div className="bg-gradient-to-br from-blue-500 to-blue-600 p-2 rounded-lg">
                <UserIcon size={18} className="text-white" />
              </div>
              <span className="hidden md:inline-block font-medium">
                {user?.name}
              </span>
            </button>
            {showUserMenu && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-1 z-10">
                <button onClick={logout} className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  Sign out
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
      {mobileMenuOpen && (
        <div className="md:hidden bg-blue-800 text-white">
          <nav className="px-2 pt-2 pb-4">
            <ul className="space-y-1">
              <li>
                <a href="/dashboard" className="block px-3 py-2 rounded-md hover:bg-blue-700">
                  Dashboard
                </a>
              </li>
              <li>
                <a href="/inventory" className="block px-3 py-2 rounded-md hover:bg-blue-700">
                  Inventory
                </a>
              </li>
              <li>
                <a href="/sales" className="block px-3 py-2 rounded-md hover:bg-blue-700">
                  Sales
                </a>
              </li>
              <li>
                <button onClick={logout} className="w-full text-left block px-3 py-2 rounded-md hover:bg-blue-700">
                  Logout
                </button>
              </li>
            </ul>
          </nav>
        </div>
      )}
    </header>
  );
};
export default Header;
