const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Product = sequelize.define('Product', {
  product_id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false
  },
  // Keep category field for backward compatibility (will be populated from category relationship)
  category: {
    type: DataTypes.STRING,
    allowNull: false
  },
  category_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'categories',
      key: 'category_id'
    }
  },
  sku: {
    type: DataTypes.STRING,
    unique: true
  },
  barcode: {
    type: DataTypes.STRING,
    unique: true
  },
  description: {
    type: DataTypes.TEXT
  },
  unit_price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  cost_price: {
    type: DataTypes.DECIMAL(10, 2)
  },
  current_stock: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    validate: {
      min: 0
    }
  },
  min_stock_level: {
    type: DataTypes.INTEGER,
    defaultValue: 10
  },
  max_stock_level: {
    type: DataTypes.INTEGER,
    defaultValue: 1000
  },
  unit_of_measure: {
    type: DataTypes.STRING,
    defaultValue: 'pieces'
  },
  supplier_id: {
    type: DataTypes.UUID,
    references: {
      model: 'suppliers',
      key: 'supplier_id'
    }
  },
  last_restocked: {
    type: DataTypes.DATE
  },
  storage_location: {
    type: DataTypes.STRING
  },
  batch_number: {
    type: DataTypes.STRING
  },
  expiration_date: {
    type: DataTypes.DATE
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  }
}, {
  tableName: 'products',
  indexes: [
    {
      fields: ['name']
    },
    {
      fields: ['category'] // Index on category field for backward compatibility
    },
    {
      fields: ['category_id'] // Index on category_id for foreign key relationship
    },
    {
      fields: ['sku']
    }
  ]
});

module.exports = Product;
