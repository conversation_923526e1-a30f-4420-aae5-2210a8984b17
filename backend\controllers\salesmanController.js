const { Salesman, User } = require('../models');
const { Op } = require('sequelize');

// Get all salesmen
const getSalesmen = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 50,
      search,
      status = 'active'
    } = req.query;

    const offset = (page - 1) * limit;
    const where = {};

    // Filter by status
    if (status !== 'all') {
      where.is_active = status === 'active';
    }

    // Search by name
    if (search) {
      where.full_name = {
        [Op.iLike]: `%${search}%`
      };
    }

    const { count, rows: salesmen } = await Salesman.findAndCountAll({
      where,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['user_id', 'email', 'role', 'is_active']
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['full_name', 'ASC']]
    });

    console.log(`👤 Found ${salesmen.length} salesmen`);

    res.json({
      success: true,
      data: {
        salesmen,
        pagination: {
          current_page: parseInt(page),
          total_pages: Math.ceil(count / limit),
          total_items: count,
          items_per_page: parseInt(limit)
        }
      }
    });
  } catch (error) {
    console.error('❌ Error fetching salesmen:', error);
    next(error);
  }
};

// Get salesman by ID
const getSalesman = async (req, res, next) => {
  try {
    const { id } = req.params;

    const salesman = await Salesman.findByPk(id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['user_id', 'email', 'role', 'is_active']
        }
      ]
    });

    if (!salesman) {
      return res.status(404).json({
        success: false,
        message: 'Salesman not found'
      });
    }

    console.log(`✅ Found salesman: ${salesman.full_name}`);

    res.json({
      success: true,
      data: salesman
    });
  } catch (error) {
    console.error('❌ Error fetching salesman:', error);
    next(error);
  }
};

// Create new salesman
const createSalesman = async (req, res, next) => {
  try {
    const {
      user_id,
      full_name,
      contact_info = {},
      commission_rate = 0.05,
      monthly_target = 0
    } = req.body;

    // Check if user exists and is not already a salesman
    const user = await User.findByPk(user_id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const existingSalesman = await Salesman.findOne({ where: { user_id } });
    if (existingSalesman) {
      return res.status(400).json({
        success: false,
        message: 'User is already a salesman'
      });
    }

    const salesman = await Salesman.create({
      user_id,
      full_name,
      contact_info,
      commission_rate,
      monthly_target
    });

    // Get created salesman with user info
    const createdSalesman = await Salesman.findByPk(salesman.salesman_id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['user_id', 'email', 'role', 'is_active']
        }
      ]
    });

    console.log(`✅ Salesman created: ${salesman.full_name}`);

    res.status(201).json({
      success: true,
      message: 'Salesman created successfully',
      data: createdSalesman
    });
  } catch (error) {
    console.error('❌ Error creating salesman:', error);
    next(error);
  }
};

// Update salesman
const updateSalesman = async (req, res, next) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const salesman = await Salesman.findByPk(id);
    if (!salesman) {
      return res.status(404).json({
        success: false,
        message: 'Salesman not found'
      });
    }

    await salesman.update(updateData);

    // Get updated salesman with user info
    const updatedSalesman = await Salesman.findByPk(id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['user_id', 'email', 'role', 'is_active']
        }
      ]
    });

    console.log(`✅ Salesman updated: ${salesman.full_name}`);

    res.json({
      success: true,
      message: 'Salesman updated successfully',
      data: updatedSalesman
    });
  } catch (error) {
    console.error('❌ Error updating salesman:', error);
    next(error);
  }
};

// Delete salesman (soft delete)
const deleteSalesman = async (req, res, next) => {
  try {
    const { id } = req.params;

    const salesman = await Salesman.findByPk(id);
    if (!salesman) {
      return res.status(404).json({
        success: false,
        message: 'Salesman not found'
      });
    }

    await salesman.update({ is_active: false });

    console.log(`✅ Salesman deactivated: ${salesman.full_name}`);

    res.json({
      success: true,
      message: 'Salesman deactivated successfully'
    });
  } catch (error) {
    console.error('❌ Error deleting salesman:', error);
    next(error);
  }
};

module.exports = {
  getSalesmen,
  getSalesman,
  createSalesman,
  updateSalesman,
  deleteSalesman
};
