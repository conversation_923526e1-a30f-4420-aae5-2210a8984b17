const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const InventoryTransaction = sequelize.define('InventoryTransaction', {
  transaction_id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  type: {
    type: DataTypes.ENUM('in', 'out', 'adjustment', 'transfer', 'loss'),
    allowNull: false
  },
  product_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'products',
      key: 'product_id'
    }
  },
  quantity: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  unit_price: {
    type: DataTypes.DECIMAL(10, 2)
  },
  total_cost: {
    type: DataTypes.DECIMAL(10, 2)
  },
  supplier_id: {
    type: DataTypes.UUID,
    references: {
      model: 'suppliers',
      key: 'supplier_id'
    }
  },
  customer_id: {
    type: DataTypes.UUID,
    references: {
      model: 'customers',
      key: 'customer_id'
    }
  },
  order_id: {
    type: DataTypes.UUID,
    references: {
      model: 'orders',
      key: 'order_id'
    }
  },
  reference_number: {
    type: DataTypes.STRING
  },
  invoice_number: {
    type: DataTypes.STRING
  },
  notes: {
    type: DataTypes.TEXT
  },
  status: {
    type: DataTypes.ENUM('pending', 'completed', 'cancelled'),
    defaultValue: 'completed'
  },
  recorded_by: {
    type: DataTypes.UUID,
    allowNull: true, // Allow null for system-generated transactions
    references: {
      model: 'users',
      key: 'user_id'
    }
  },
  transaction_date: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  batch_number: {
    type: DataTypes.STRING
  },
  expiration_date: {
    type: DataTypes.DATE
  },
  storage_location: {
    type: DataTypes.STRING
  }
}, {
  tableName: 'inventory_transactions',
  indexes: [
    {
      fields: ['product_id']
    },
    {
      fields: ['type']
    },
    {
      fields: ['transaction_date']
    },
    {
      fields: ['status']
    }
  ]
});

module.exports = InventoryTransaction;
