const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const CommissionAssignment = sequelize.define('CommissionAssignment', {
  assignment_id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  salesman_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'salesmen',
      key: 'salesman_id'
    }
  },
  order_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'orders',
      key: 'order_id'
    }
  },
  stage_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'commission_stages',
      key: 'stage_id'
    }
  },
  sales_amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false
  },
  commission_percentage: {
    type: DataTypes.DECIMAL(5, 4),
    allowNull: false
  },
  commission_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  bonus_amount: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0
  },
  total_commission: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  calculation_date: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  period_start: {
    type: DataTypes.DATE,
    allowNull: false
  },
  period_end: {
    type: DataTypes.DATE,
    allowNull: false
  },
  status: {
    type: DataTypes.ENUM('pending', 'calculated', 'approved', 'paid', 'cancelled'),
    defaultValue: 'calculated'
  },
  payment_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  payment_reference: {
    type: DataTypes.STRING,
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT
  },
  created_by: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'user_id'
    }
  },
  approved_by: {
    type: DataTypes.UUID,
    references: {
      model: 'users',
      key: 'user_id'
    }
  },
  paid_by: {
    type: DataTypes.UUID,
    references: {
      model: 'users',
      key: 'user_id'
    }
  }
}, {
  tableName: 'commission_assignments',
  indexes: [
    {
      fields: ['salesman_id']
    },
    {
      fields: ['order_id']
    },
    {
      fields: ['stage_id']
    },
    {
      fields: ['status']
    },
    {
      fields: ['calculation_date']
    },
    {
      fields: ['period_start', 'period_end']
    },
    {
      unique: true,
      fields: ['salesman_id', 'order_id'] // Prevent duplicate commission assignments
    }
  ]
});

module.exports = CommissionAssignment;
