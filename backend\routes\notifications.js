const express = require('express');
const router = express.Router();
const controller = require('../controllers/notificationController');
const { authenticateToken } = require('../middleware/auth');

// GET /api/notifications - Get notifications for current user
router.get('/', authenticateToken, (req, res, next) => controller.getNotifications(req, res, next));

// GET /api/notifications/unread-count - Get unread notification count
router.get('/unread-count', authenticateToken, (req, res, next) => controller.getUnreadCount(req, res, next));

// PUT /api/notifications/:notification_id/read - Mark notification as read
router.put('/:notification_id/read', authenticateToken, (req, res, next) => controller.markAsRead(req, res, next));

// PUT /api/notifications/mark-all-read - Mark all notifications as read
router.put('/mark-all-read', authenticateToken, (req, res, next) => controller.markAllAsRead(req, res, next));

// DELETE /api/notifications/:notification_id - Delete notification
router.delete('/:notification_id', authenticateToken, (req, res, next) => controller.deleteNotification(req, res, next));

module.exports = router;
