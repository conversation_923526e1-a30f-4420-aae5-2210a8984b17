import { useState, useMemo } from 'react';
import {
  SearchIcon,
  FilterIcon,
  DownloadIcon,
  RefreshCwIcon,
  PackageIcon,
  AlertTriangleIcon,
  EyeIcon,
  EditIcon,
  XIcon,
  SaveIcon,
  CalendarIcon,
  HashIcon,
  TruckIcon,
  MapPinIcon,
  ClockIcon
} from 'lucide-react';
import { useApiData } from '../contexts/ApiDataContext';
import { useAuth } from '../contexts/AuthContext';

// Utility function to get quantity direction and styling (same as InventoryHistory)
const getQuantityIndicator = (transaction) => {
  const { type, quantity, notes } = transaction;

  // Convert quantity to number to ensure proper comparison
  let qty = Number(quantity) || 0;

  // Determine if this transaction increases or decreases stock
  const increaseTypes = ['in', 'restock', 'return', 'transfer_in'];
  const decreaseTypes = ['out', 'sale', 'transfer_out', 'waste', 'damaged'];

  let isIncrease = false;
  let isDecrease = false;

  // Check transaction type first
  if (increaseTypes.includes(type)) {
    isIncrease = true;
  } else if (decreaseTypes.includes(type)) {
    isDecrease = true;
  } else if (type === 'adjustment') {
    // For adjustments, determine direction based on context and calculate actual difference
    if (notes) {
      const notesLower = notes.toLowerCase();

      // Look for stock change patterns like "70 → 80" or "Stock adjustment: 70 → 80"
      const stockPattern = notesLower.match(/(\d+)\s*→\s*(\d+)/);
      if (stockPattern) {
        const [, before, after] = stockPattern;
        const beforeQty = parseInt(before);
        const afterQty = parseInt(after);
        const actualDifference = afterQty - beforeQty;

        if (actualDifference > 0) {
          isIncrease = true;
          // Override quantity with actual difference for display
          qty = Math.abs(actualDifference);
        } else if (actualDifference < 0) {
          isDecrease = true;
          // Override quantity with actual difference for display
          qty = Math.abs(actualDifference);
        }
      } else if (notesLower.includes('stock restock') ||
                 notesLower.includes('increase') ||
                 notesLower.includes('add') ||
                 notesLower.includes('restock')) {
        isIncrease = true;
      } else if (notesLower.includes('decrease') ||
                 notesLower.includes('reduce') ||
                 notesLower.includes('remove') ||
                 notesLower.includes('sold') ||
                 notesLower.includes('sale') ||
                 notesLower.includes('out')) {
        isDecrease = true;
      } else {
        // For adjustments without clear patterns, we need to be more careful
        // Check if the notes contain any indication of direction
        if (notesLower.includes('updated') || notesLower.includes('edit') || notesLower.includes('changed')) {
          // For product updates, the quantity field might represent the new stock level, not the change
          // We should treat this as neutral unless we can determine the direction
          // Look for negative quantities which clearly indicate decreases
          if (qty < 0) {
            isDecrease = true;
            qty = Math.abs(qty); // Make quantity positive for display
          } else {
            // For positive quantities in updates, we can't reliably determine direction
            // without knowing the previous stock level, so treat as neutral
            isIncrease = false;
            isDecrease = false;
          }
        } else {
          // For other adjustment types, use the quantity sign as direction indicator
          if (qty > 0) {
            isIncrease = true;
          } else if (qty < 0) {
            isDecrease = true;
            qty = Math.abs(qty); // Make quantity positive for display
          }
        }
      }
    } else {
      // No notes, use quantity value
      if (qty > 0) {
        isIncrease = true;
      } else if (qty < 0) {
        isDecrease = true;
      }
    }
  }

  // Set display properties based on direction
  let direction = '';
  let colorClass = '';

  if (isIncrease) {
    direction = '+';
    colorClass = 'text-green-600 font-medium';
  } else if (isDecrease) {
    direction = '-';
    colorClass = 'text-red-600 font-medium';
  } else {
    // Neutral or unknown - show quantity without direction
    direction = '';
    colorClass = 'text-gray-600';
  }

  // Build display quantity with single symbol (no duplicate icons)
  const displayQuantity = `${direction}${Math.abs(qty)} units`;

  // Enhanced debug logging for development
  if (process.env.NODE_ENV === 'development') {
    console.log(`🔍 InventoryBrowser Quantity Indicator Debug:`, {
      originalQuantity: quantity,
      processedQuantity: qty,
      type,
      notes: notes?.substring(0, 100) + (notes?.length > 100 ? '...' : ''),
      isIncrease,
      isDecrease,
      direction,
      displayQuantity,
      colorClass
    });
  }

  return {
    direction,
    colorClass,
    displayQuantity, // This already includes the direction symbol
    isIncrease,
    isDecrease
  };
};

function InventoryBrowser({ products }) {
  const {
    updateProduct,
    updateProductStock,
    addProduct,
    inventoryTransactions,
    suppliers,
    categories, // Database categories for edit modal dropdown
    supplierOptions,
    categoryOptions, // Formatted category options for edit modal
    fetchProducts,
    fetchSuppliers,
    fetchCategories,
    fetchInventoryTransactions
  } = useApiData();
  const { user } = useAuth();

  const refreshAllData = async () => {
    console.log('🔄 Refreshing inventory browser data...');
    try {
      const startTime = Date.now();
      await Promise.all([
        fetchProducts(),
        fetchSuppliers(),
        fetchCategories(),
        fetchInventoryTransactions()
      ]);
      const endTime = Date.now();
      console.log(`✅ Inventory browser data refreshed successfully in ${endTime - startTime}ms`);
      console.log('📊 Updated data counts:', {
        products: products?.length || 0,
        suppliers: suppliers?.length || 0,
        categories: categories?.length || 0,
        transactions: inventoryTransactions?.length || 0
      });
    } catch (error) {
      console.error('❌ Failed to refresh inventory browser data:', error);
      throw error; // Re-throw to allow calling code to handle
    }
  };

  // Existing state
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [stockFilter, setStockFilter] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState('asc');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 20;

  // Modal state
  const [viewModalOpen, setViewModalOpen] = useState(false);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [showError, setShowError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  // Edit form state
  const [editFormData, setEditFormData] = useState({
    name: '',
    description: '',
    category: '',
    sku: '',
    barcode: '',
    stock: '',
    unitPrice: '',
    costPrice: '',
    minStock: '',
    maxStock: '',
    storageLocation: ''
  });

  // Track original values to detect critical changes
  const [originalValues, setOriginalValues] = useState({
    stock: '',
    unitPrice: ''
  });

  // Modal handlers
  const handleViewProduct = (product) => {
    setSelectedProduct(product);
    setViewModalOpen(true);
  };

  const handleEditProduct = (product) => {
    setSelectedProduct(product);
    setEditFormData({
      name: product.name || '',
      description: product.description || '',
      category: product.category || '',
      sku: product.sku || '',
      barcode: product.barcode || '',
      stock: product.stock?.toString() || '',
      unitPrice: product.price?.toString() || '',
      costPrice: product.costPrice?.toString() || '',
      minStock: product.minStock?.toString() || '',
      maxStock: product.maxStock?.toString() || '',
      storageLocation: product.storageLocation || ''
    });
    setOriginalValues({
      stock: product.stock?.toString() || '',
      unitPrice: product.price?.toString() || ''
    });
    setEditModalOpen(true);
  };

  const handleCloseModals = () => {
    setViewModalOpen(false);
    setEditModalOpen(false);
    setSelectedProduct(null);
    setShowSuccess(false);
    setShowError(false);
    setErrorMessage('');
    setSuccessMessage('');
  };

  const handleEditFormChange = (e) => {
    const { name, value } = e.target;
    setEditFormData(prev => ({ ...prev, [name]: value }));
  };

  // Check if critical changes (both stock and price changed)
  const hasCriticalChanges = () => {
    const stockChanged = editFormData.stock !== originalValues.stock;
    const priceChanged = editFormData.unitPrice !== originalValues.unitPrice;
    return stockChanged && priceChanged;
  };

  // Check if stock changed (for transaction creation)
  const hasStockChange = () => {
    return editFormData.stock !== originalValues.stock;
  };

  // Get stock change details
  const getStockChangeDetails = () => {
    const originalStock = parseInt(originalValues.stock) || 0;
    const newStock = parseInt(editFormData.stock) || 0;
    const difference = newStock - originalStock;
    return {
      originalStock,
      newStock,
      difference,
      isIncrease: difference > 0,
      isDecrease: difference < 0
    };
  };

  // Handle form submission
  const handleEditSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setShowError(false);
    setErrorMessage('');

    try {
      const isCriticalChange = hasCriticalChanges();

      if (isCriticalChange) {
        // Create new product variant - Transform to backend field names
        const newProductData = {
          name: editFormData.name,
          category: editFormData.category,
          sku: editFormData.sku + '-V2', // Add variant suffix
          barcode: editFormData.barcode,
          description: editFormData.description,
          unit_price: parseFloat(editFormData.unitPrice),
          cost_price: parseFloat(editFormData.costPrice),
          current_stock: parseInt(editFormData.stock),
          min_stock_level: parseInt(editFormData.minStock || 50),
          max_stock_level: parseInt(editFormData.maxStock || 1000),
          storage_location: editFormData.storageLocation,
          supplier_id: selectedProduct.supplierId,
          recorded_by: user?.user_id || user?.email || 'System'
        };

        console.log('🔄 Creating product variant with backend field names:', {
          originalProduct: selectedProduct.name,
          variantData: newProductData
        });

        const variantResult = await addProduct(newProductData);
        console.log('✅ Product variant creation result:', variantResult);
        setSuccessMessage(`New product variant created successfully! Original product preserved.`);
      } else {
        // Check if stock changed to determine if we need to create a transaction
        const originalStock = parseInt(originalValues.stock);
        const newStock = parseInt(editFormData.stock);
        const stockChanged = originalStock !== newStock;

        console.log('🔍 Stock change analysis:', {
          originalStock,
          newStock,
          stockChanged,
          difference: newStock - originalStock
        });

        if (stockChanged) {
          // Stock changed - use updateProductStock to create transaction
          console.log('📦 Stock change detected - using updateProductStock for transaction creation');

          // First update the product data (excluding stock)
          const productUpdateData = {
            name: editFormData.name,
            description: editFormData.description,
            category: editFormData.category,
            sku: editFormData.sku,
            barcode: editFormData.barcode,
            unit_price: parseFloat(editFormData.unitPrice),
            cost_price: parseFloat(editFormData.costPrice),
            min_stock_level: parseInt(editFormData.minStock || 50),
            max_stock_level: parseInt(editFormData.maxStock || 1000),
            storage_location: editFormData.storageLocation
            // Note: current_stock is excluded - will be handled by updateProductStock
          };

          console.log('🔄 Updating product data (excluding stock):', productUpdateData);
          await updateProduct(selectedProduct.id, productUpdateData);

          // Then update stock with transaction creation
          const stockUpdateData = {
            notes: `Stock adjustment via InventoryBrowser: ${originalStock} → ${newStock} units`,
            unitPrice: parseFloat(editFormData.unitPrice),
            storageLocation: editFormData.storageLocation
          };

          console.log('📦 Updating stock with transaction creation:', {
            productId: selectedProduct.id,
            newStock,
            transactionType: 'adjustment',
            additionalData: stockUpdateData
          });

          await updateProductStock(selectedProduct.id, newStock, 'adjustment', stockUpdateData);
          setSuccessMessage('Product and stock updated successfully! Transaction record created.');
        } else {
          // No stock change - use regular product update
          console.log('📝 No stock change - using regular product update');

          const updateData = {
            name: editFormData.name,
            description: editFormData.description,
            category: editFormData.category,
            sku: editFormData.sku,
            barcode: editFormData.barcode,
            current_stock: parseInt(editFormData.stock), // Same as original, no change
            unit_price: parseFloat(editFormData.unitPrice),
            cost_price: parseFloat(editFormData.costPrice),
            min_stock_level: parseInt(editFormData.minStock || 50),
            max_stock_level: parseInt(editFormData.maxStock || 1000),
            storage_location: editFormData.storageLocation
          };

          console.log('🔄 Sending product update to backend:', {
            productId: selectedProduct.id,
            updateData: updateData
          });

          const updateResult = await updateProduct(selectedProduct.id, updateData);
          console.log('✅ Product update result:', updateResult);
          setSuccessMessage('Product updated successfully!');
        }
      }

      setShowSuccess(true);
      setTimeout(() => {
        handleCloseModals();
        refreshAllData(); // Refresh all data to ensure synchronization
      }, 2000);

    } catch (error) {
      console.error('❌ Error updating product:', error);
      console.error('❌ Error details:', {
        message: error.message,
        stack: error.stack,
        productId: selectedProduct?.id,
        formData: editFormData
      });

      // More specific error messages
      let errorMsg = 'Failed to update product. Please try again.';
      if (error.message?.includes('authentication') || error.message?.includes('token')) {
        errorMsg = 'Authentication failed. Please log in again.';
      } else if (error.message?.includes('permission') || error.message?.includes('access')) {
        errorMsg = 'You do not have permission to update this product.';
      } else if (error.message?.includes('validation')) {
        errorMsg = 'Invalid data provided. Please check your inputs.';
      } else if (error.message?.includes('network') || error.message?.includes('fetch')) {
        errorMsg = 'Network error. Please check your connection and try again.';
      } else if (error.message) {
        errorMsg = error.message;
      }

      setErrorMessage(errorMsg);
      setShowError(true);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get unique categories from current products for filter dropdown
  // Note: This is different from 'categories' from ApiDataContext which is used for edit modal
  const uniqueCategories = useMemo(() => {
    const cats = [...new Set(products.map(p => p.category))];
    return cats.filter(Boolean);
  }, [products]);

  // Filter and sort products
  const filteredProducts = useMemo(() => {
    let filtered = products.filter(product => {
      const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           (product.sku && product.sku.toLowerCase().includes(searchTerm.toLowerCase()));
      const matchesCategory = !categoryFilter || product.category === categoryFilter;

      let matchesStock = true;
      if (stockFilter === 'low') {
        matchesStock = product.stock <= (product.minStock || 50);
      } else if (stockFilter === 'out') {
        matchesStock = product.stock === 0;
      } else if (stockFilter === 'overstocked') {
        matchesStock = product.stock > (product.minStock || 50) * 3;
      }

      return matchesSearch && matchesCategory && matchesStock;
    });

    // Sort products
    filtered.sort((a, b) => {
      let aValue, bValue;

      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'stock':
          aValue = a.stock;
          bValue = b.stock;
          break;
        case 'price':
          aValue = a.price;
          bValue = b.price;
          break;
        case 'value':
          aValue = a.stock * a.price;
          bValue = b.stock * b.price;
          break;
        case 'lastRestocked':
          aValue = new Date(a.lastRestocked || 0);
          bValue = new Date(b.lastRestocked || 0);
          break;
        default:
          aValue = a[sortBy];
          bValue = b[sortBy];
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return filtered;
  }, [products, searchTerm, categoryFilter, stockFilter, sortBy, sortOrder]);

  // Pagination
  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);
  const paginatedProducts = filteredProducts.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const getStockStatus = (product) => {
    const minStock = product.minStock || 50;
    if (product.stock === 0) return { status: 'out', color: 'bg-red-100 text-red-800', text: 'Out of Stock' };
    if (product.stock <= minStock) return { status: 'low', color: 'bg-yellow-100 text-yellow-800', text: 'Low Stock' };
    if (product.stock > minStock * 3) return { status: 'over', color: 'bg-blue-100 text-blue-800', text: 'Overstocked' };
    return { status: 'normal', color: 'bg-green-100 text-green-800', text: 'In Stock' };
  };

  const exportToCSV = () => {
    const headers = ['Product Name', 'Category', 'SKU', 'Current Stock', 'Unit Price', 'Total Value', 'Supplier', 'Last Restocked', 'Storage Location'];
    const csvData = filteredProducts.map(product => [
      product.name,
      product.category,
      product.sku || '',
      product.stock,
      product.price,
      (product.stock * product.price).toFixed(2),
      product.supplier || '',
      product.lastRestocked || '',
      product.storageLocation || ''
    ]);

    const csvContent = [headers, ...csvData]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `inventory_${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };



  // Safety check for data
  if (!products) {
    return (
      <div className="flex items-center justify-center h-64 bg-yellow-50 border border-yellow-200 rounded-lg">
        <div className="text-center">
          <PackageIcon size={48} className="text-yellow-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-yellow-700 mb-2">No Product Data</h3>
          <p className="text-yellow-600">Product data is not available.</p>
        </div>
      </div>
    );
  }

  if (products.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="text-center">
          <PackageIcon size={48} className="text-blue-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-blue-700 mb-2">No Products Found</h3>
          <p className="text-blue-600">No products have been added to inventory yet.</p>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center mb-6">
        <SearchIcon size={20} className="text-blue-600 mr-2" />
        <h2 className="text-lg font-medium text-gray-800">Inventory Browser</h2>
      </div>

      {/* Search and Filters */}
      <div className="bg-gray-50 rounded-lg p-4 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Search</label>
            <div className="relative">
              <SearchIcon size={16} className="absolute left-3 top-3 text-gray-400" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search by name or SKU"
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
            <select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Categories</option>
              {uniqueCategories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Stock Level</label>
            <select
              value={stockFilter}
              onChange={(e) => setStockFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Stock Levels</option>
              <option value="low">Low Stock</option>
              <option value="out">Out of Stock</option>
              <option value="overstocked">Overstocked</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
            <div className="flex space-x-2">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="name">Name</option>
                <option value="stock">Stock</option>
                <option value="price">Price</option>
                <option value="value">Total Value</option>
                <option value="lastRestocked">Last Restocked</option>
              </select>
              <button
                onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                className="px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-100"
                title={`Sort ${sortOrder === 'asc' ? 'Descending' : 'Ascending'}`}
              >
                {sortOrder === 'asc' ? '↑' : '↓'}
              </button>
            </div>
          </div>
        </div>

        <div className="flex justify-between items-center">
          <div className="text-sm text-gray-600">
            Showing {paginatedProducts.length} of {filteredProducts.length} products
          </div>
          <div className="flex space-x-2">
            <button
              onClick={exportToCSV}
              className="flex items-center px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
            >
              <DownloadIcon size={16} className="mr-2" />
              Export CSV
            </button>
            <button
              onClick={() => window.location.reload()}
              className="flex items-center px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
            >
              <RefreshCwIcon size={16} className="mr-2" />
              Refresh
            </button>
          </div>
        </div>
      </div>

      {/* Products Table */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        {/* Simple fallback if no paginated products */}
        {paginatedProducts.length === 0 ? (
          <div className="p-8 text-center">
            <div className="bg-gray-50 rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-700 mb-4">No Products to Display</h3>
              <div className="text-sm text-gray-600 space-y-2">
                <div>Total products: {products?.length || 0}</div>
                <div>Filtered products: {filteredProducts?.length || 0}</div>
                <div>Paginated products: {paginatedProducts?.length || 0}</div>
                <div>Current page: {currentPage}</div>
                <div>Items per page: {itemsPerPage}</div>
              </div>
              {products?.length > 0 && (
                <div className="mt-4 p-4 bg-blue-50 rounded text-left">
                  <h4 className="font-medium text-blue-800 mb-2">Sample Product Data:</h4>
                  <div className="text-xs text-blue-700">
                    <div>Name: {products[0].name || 'Unknown'}</div>
                    <div>Category: {products[0].category || 'Unknown'}</div>
                    <div>Stock: {products[0].stock || 'Unknown'}</div>
                    <div>Price: ${products[0].price || 'Unknown'}</div>
                  </div>
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product Details</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category & SKU</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock & Pricing</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Storage & Batch</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {paginatedProducts.map((product) => {
                const stockStatus = getStockStatus(product);
                return (
                  <tr key={product.id} className="hover:bg-gray-50">
                    {/* Product Details */}
                    <td className="px-6 py-4">
                      <div className="text-sm font-medium text-gray-900">{product.name}</div>
                      <div className="text-xs text-gray-500 mt-1">
                        {product.description && (
                          <div className="truncate max-w-48" title={product.description}>
                            {product.description}
                          </div>
                        )}
                        {product.barcode && (
                          <div className="text-xs text-gray-400">Barcode: {product.barcode}</div>
                        )}
                      </div>
                    </td>

                    {/* Category & SKU */}
                    <td className="px-6 py-4 text-sm text-gray-500">
                      <div className="font-medium">{product.category}</div>
                      <div className="text-xs text-gray-400">SKU: {product.sku || 'N/A'}</div>
                      <div className="text-xs text-gray-400">
                        Unit: {product.unitOfMeasure || 'pieces'}
                      </div>
                    </td>

                    {/* Stock & Pricing */}
                    <td className="px-6 py-4 text-sm text-gray-500">
                      <div className="space-y-1">
                        <div className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${stockStatus.color}`}>
                          {product.stock} units
                        </div>
                        <div className="text-xs">
                          <span className="font-medium">Unit:</span> ${product.price?.toFixed(2) || '0.00'}
                        </div>
                        {product.costPrice && (
                          <div className="text-xs">
                            <span className="font-medium">Cost:</span> ${product.costPrice.toFixed(2)}
                          </div>
                        )}
                        <div className="text-xs font-semibold text-green-600">
                          Total: ${((product.stock || 0) * (product.price || 0)).toFixed(2)}
                        </div>
                        <div className="text-xs text-gray-400">
                          Min: {product.minStock || 'N/A'} | Max: {product.maxStock || 'N/A'}
                        </div>
                      </div>
                    </td>

                    {/* Supplier */}
                    <td className="px-6 py-4 text-sm text-gray-500">
                      <div className="font-medium">
                        {product.supplier || 'Unknown Supplier'}
                      </div>
                      {product.supplierId && (
                        <div className="text-xs text-gray-400">ID: {product.supplierId}</div>
                      )}
                    </td>

                    {/* Storage & Batch */}
                    <td className="px-6 py-4 text-sm text-gray-500">
                      <div className="space-y-1">
                        {product.storageLocation && (
                          <div className="text-xs">
                            <span className="font-medium">Location:</span> {product.storageLocation}
                          </div>
                        )}
                        {product.batchNumber && (
                          <div className="text-xs">
                            <span className="font-medium">Batch:</span> {product.batchNumber}
                          </div>
                        )}
                        {product.expirationDate && (
                          <div className="text-xs text-orange-600">
                            <span className="font-medium">Expires:</span> {product.expirationDate}
                          </div>
                        )}
                        {product.lastRestocked && (
                          <div className="text-xs text-gray-400">
                            <span className="font-medium">Restocked:</span> {product.lastRestocked}
                          </div>
                        )}
                      </div>
                    </td>

                    {/* Status */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${stockStatus.color}`}>
                        {stockStatus.text}
                      </span>
                    </td>

                    {/* Actions */}
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        onClick={() => handleViewProduct(product)}
                        className="text-indigo-600 hover:text-indigo-900 mr-3 flex items-center"
                        title="View Product Details"
                      >
                        <EyeIcon size={14} className="mr-1" />
                        View
                      </button>
                      <button
                        onClick={() => handleEditProduct(product)}
                        className="text-green-600 hover:text-green-900 flex items-center"
                        title="Edit Product"
                      >
                        <EditIcon size={14} className="mr-1" />
                        Edit
                      </button>
                    </td>
                  </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Previous
              </button>
              <button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Next
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Showing <span className="font-medium">{(currentPage - 1) * itemsPerPage + 1}</span> to{' '}
                  <span className="font-medium">{Math.min(currentPage * itemsPerPage, filteredProducts.length)}</span> of{' '}
                  <span className="font-medium">{filteredProducts.length}</span> results
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                    <button
                      key={page}
                      onClick={() => setCurrentPage(page)}
                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                        page === currentPage
                          ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      {page}
                    </button>
                  ))}
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* View Product Modal */}
      {viewModalOpen && selectedProduct && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-bold text-gray-900 flex items-center">
                <PackageIcon size={20} className="mr-2 text-blue-600" />
                Product Details: {selectedProduct.name}
              </h3>
              <button
                onClick={handleCloseModals}
                className="text-gray-400 hover:text-gray-600"
              >
                <XIcon size={24} />
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Basic Information */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-semibold text-gray-700 mb-3 flex items-center">
                  <PackageIcon size={16} className="mr-2" />
                  Basic Information
                </h4>
                <div className="space-y-2 text-sm">
                  <div><span className="font-medium">Name:</span> {selectedProduct.name}</div>
                  <div><span className="font-medium">Category:</span> {selectedProduct.category}</div>
                  <div><span className="font-medium">SKU:</span> {selectedProduct.sku || 'N/A'}</div>
                  <div><span className="font-medium">Barcode:</span> {selectedProduct.barcode || 'N/A'}</div>
                  <div><span className="font-medium">Description:</span> {selectedProduct.description || 'N/A'}</div>
                </div>
              </div>

              {/* Stock & Pricing */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-semibold text-gray-700 mb-3 flex items-center">
                  <HashIcon size={16} className="mr-2" />
                  Stock & Pricing
                </h4>
                <div className="space-y-2 text-sm">
                  <div><span className="font-medium">Current Stock:</span> {selectedProduct.stock} units</div>
                  <div><span className="font-medium">Unit Price:</span> ${selectedProduct.price?.toFixed(2) || '0.00'}</div>
                  <div><span className="font-medium">Cost Price:</span> ${selectedProduct.costPrice?.toFixed(2) || 'N/A'}</div>
                  <div><span className="font-medium">Min Stock:</span> {selectedProduct.minStock || 'N/A'}</div>
                  <div><span className="font-medium">Max Stock:</span> {selectedProduct.maxStock || 'N/A'}</div>
                  <div><span className="font-medium">Total Value:</span> ${((selectedProduct.stock || 0) * (selectedProduct.price || 0)).toFixed(2)}</div>
                </div>
              </div>

              {/* Supplier & Storage */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-semibold text-gray-700 mb-3 flex items-center">
                  <TruckIcon size={16} className="mr-2" />
                  Supplier & Storage
                </h4>
                <div className="space-y-2 text-sm">
                  <div><span className="font-medium">Supplier:</span> {selectedProduct.supplier || 'Unknown'}</div>
                  <div><span className="font-medium">Storage Location:</span> {selectedProduct.storageLocation || 'N/A'}</div>
                  <div><span className="font-medium">Batch Number:</span> {selectedProduct.batchNumber || 'N/A'}</div>
                  <div><span className="font-medium">Expiration Date:</span> {selectedProduct.expirationDate || 'N/A'}</div>
                  <div><span className="font-medium">Last Restocked:</span> {selectedProduct.lastRestocked || 'N/A'}</div>
                </div>
              </div>
            </div>

            {/* Recent Transactions */}
            <div className="mt-6">
              <h4 className="font-semibold text-gray-700 mb-3 flex items-center">
                <ClockIcon size={16} className="mr-2" />
                Recent Transactions
              </h4>
              <div className="bg-gray-50 rounded-lg p-4 max-h-60 overflow-y-auto">
                {inventoryTransactions
                  ?.filter(t =>
                    t.productId === selectedProduct.id ||
                    t.productName === selectedProduct.name ||
                    t.product_id === selectedProduct.id
                  )
                  ?.slice(0, 10)
                  ?.map((transaction, index) => (
                    <div key={index} className="flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0">
                      <div className="flex items-center">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium mr-3 ${
                          transaction.type === 'in' ? 'bg-green-100 text-green-800' :
                          transaction.type === 'out' ? 'bg-red-100 text-red-800' :
                          'bg-blue-100 text-blue-800'
                        }`}>
                          {transaction.type?.toUpperCase()}
                        </span>
                        {(() => {
                          const indicator = getQuantityIndicator(transaction);
                          return (
                            <span className={`text-sm ${indicator.colorClass}`}>
                              {indicator.displayQuantity}
                            </span>
                          );
                        })()}
                      </div>
                      <div className="text-right text-sm text-gray-600">
                        <div>{transaction.date}</div>
                        <div className="text-xs">${transaction.totalCost?.toFixed(2) || '0.00'}</div>
                      </div>
                    </div>
                  )) || (
                  <div className="text-gray-500 text-center py-4">No recent transactions found</div>
                )}
              </div>
            </div>

            <div className="flex justify-end mt-6">
              <button
                onClick={handleCloseModals}
                className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Product Modal */}
      {editModalOpen && selectedProduct && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-10 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-bold text-gray-900 flex items-center">
                <EditIcon size={20} className="mr-2 text-green-600" />
                Edit Product: {selectedProduct.name}
              </h3>
              <button
                onClick={handleCloseModals}
                className="text-gray-400 hover:text-gray-600"
              >
                <XIcon size={24} />
              </button>
            </div>

            {/* Success/Error Messages */}
            {showSuccess && (
              <div className="bg-green-50 border-l-4 border-green-500 p-4 mb-6">
                <p className="text-green-700 font-medium">Success!</p>
                <p className="text-green-600">{successMessage}</p>
              </div>
            )}

            {showError && (
              <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
                <p className="text-red-700 font-medium">Error</p>
                <p className="text-red-600">{errorMessage}</p>
              </div>
            )}

            {/* Critical Change Warning */}
            {hasCriticalChanges() && (
              <div className="bg-yellow-50 border-l-4 border-yellow-500 p-4 mb-6">
                <p className="text-yellow-700 font-medium">⚠️ Critical Change Detected</p>
                <p className="text-yellow-600">
                  You are changing both stock quantity and unit price. This will create a new product variant
                  instead of updating the existing product to preserve historical data.
                </p>
              </div>
            )}

            {/* Stock Change Warning */}
            {hasStockChange() && !hasCriticalChanges() && (
              <div className="bg-blue-50 border-l-4 border-blue-500 p-4 mb-6">
                <p className="text-blue-700 font-medium">📦 Stock Adjustment Detected</p>
                <p className="text-blue-600">
                  {(() => {
                    const details = getStockChangeDetails();
                    return (
                      <>
                        Stock will be adjusted from <strong>{details.originalStock}</strong> to <strong>{details.newStock}</strong> units
                        ({details.isIncrease ? '+' : ''}{details.difference} units).
                        <br />
                        <strong>An inventory transaction record will be created</strong> for audit trail purposes.
                      </>
                    );
                  })()}
                </p>
              </div>
            )}

            <form onSubmit={handleEditSubmit}>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* Basic Information */}
                <div className="space-y-4">
                  <h4 className="font-semibold text-gray-700 flex items-center">
                    <PackageIcon size={16} className="mr-2" />
                    Basic Information
                  </h4>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Product Name *</label>
                    <input
                      type="text"
                      name="name"
                      value={editFormData.name}
                      onChange={handleEditFormChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                    <textarea
                      name="description"
                      value={editFormData.description}
                      onChange={handleEditFormChange}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Category *</label>
                    <select
                      name="category"
                      value={editFormData.category}
                      onChange={handleEditFormChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    >
                      {categoryOptions?.map(option => (
                        <option key={option.value} value={option.value}>{option.label}</option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* Product Codes & Stock */}
                <div className="space-y-4">
                  <h4 className="font-semibold text-gray-700 flex items-center">
                    <HashIcon size={16} className="mr-2" />
                    Codes & Stock
                  </h4>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">SKU</label>
                    <input
                      type="text"
                      name="sku"
                      value={editFormData.sku}
                      onChange={handleEditFormChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Barcode</label>
                    <input
                      type="text"
                      name="barcode"
                      value={editFormData.barcode}
                      onChange={handleEditFormChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Current Stock *
                      {editFormData.stock !== originalValues.stock && (
                        <span className="text-yellow-600 text-xs ml-1">(Changed)</span>
                      )}
                    </label>
                    <input
                      type="number"
                      name="stock"
                      value={editFormData.stock}
                      onChange={handleEditFormChange}
                      min="0"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                </div>

                {/* Pricing & Limits */}
                <div className="space-y-4">
                  <h4 className="font-semibold text-gray-700 flex items-center">
                    <CalendarIcon size={16} className="mr-2" />
                    Pricing & Limits
                  </h4>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Unit Price ($) *
                      {editFormData.unitPrice !== originalValues.unitPrice && (
                        <span className="text-yellow-600 text-xs ml-1">(Changed)</span>
                      )}
                    </label>
                    <input
                      type="number"
                      name="unitPrice"
                      value={editFormData.unitPrice}
                      onChange={handleEditFormChange}
                      step="0.01"
                      min="0"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Cost Price ($)</label>
                    <input
                      type="number"
                      name="costPrice"
                      value={editFormData.costPrice}
                      onChange={handleEditFormChange}
                      step="0.01"
                      min="0"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Min Stock Level</label>
                    <input
                      type="number"
                      name="minStock"
                      value={editFormData.minStock}
                      onChange={handleEditFormChange}
                      min="0"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Max Stock Level</label>
                    <input
                      type="number"
                      name="maxStock"
                      value={editFormData.maxStock}
                      onChange={handleEditFormChange}
                      min="0"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Storage Location</label>
                    <input
                      type="text"
                      name="storageLocation"
                      value={editFormData.storageLocation}
                      onChange={handleEditFormChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={handleCloseModals}
                  className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600"
                  disabled={isSubmitting}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className={`flex items-center px-6 py-2 rounded-md transition-colors ${
                    hasCriticalChanges()
                      ? 'bg-blue-600 hover:bg-blue-700 text-white'
                      : 'bg-green-600 hover:bg-green-700 text-white'
                  } disabled:bg-gray-400`}
                >
                  {isSubmitting ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      {hasCriticalChanges() ? 'Creating Variant...' : 'Updating...'}
                    </>
                  ) : (
                    <>
                      <SaveIcon size={16} className="mr-2" />
                      {hasCriticalChanges()
                        ? 'Add to Inventory'
                        : hasStockChange()
                          ? 'Update Product & Create Transaction'
                          : 'Update Product'
                      }
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}

export default InventoryBrowser;
