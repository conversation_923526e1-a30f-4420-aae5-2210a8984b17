const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Employee = sequelize.define('Employee', {
  employee_id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  employee_number: {
    type: DataTypes.STRING,
    unique: true,
    allowNull: false
  },
  full_name: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      len: [2, 100]
    }
  },
  email: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    validate: {
      isEmail: true
    }
  },
  phone: {
    type: DataTypes.STRING,
    validate: {
      len: [10, 20]
    }
  },
  position: {
    type: DataTypes.STRING,
    allowNull: false
  },
  department: {
    type: DataTypes.STRING,
    allowNull: false
  },
  hire_date: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  termination_date: {
    type: DataTypes.DATE
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive', 'terminated', 'on_leave'),
    defaultValue: 'active'
  },
  address: {
    type: DataTypes.JSONB,
    defaultValue: {}
  },
  emergency_contact: {
    type: DataTypes.JSONB,
    defaultValue: {}
  },
  bank_details: {
    type: DataTypes.JSONB,
    defaultValue: {}
  },
  profile_photo: {
    type: DataTypes.STRING // URL to photo file
  },
  employee_type: {
    type: DataTypes.ENUM('full_time', 'part_time', 'contract', 'intern'),
    defaultValue: 'full_time'
  },
  manager_id: {
    type: DataTypes.UUID,
    references: {
      model: 'employees',
      key: 'employee_id'
    }
  },
  notes: {
    type: DataTypes.TEXT
  },
  created_by: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'user_id'
    }
  },
  updated_by: {
    type: DataTypes.UUID,
    references: {
      model: 'users',
      key: 'user_id'
    }
  }
}, {
  tableName: 'employees',
  indexes: [
    {
      fields: ['employee_number']
    },
    {
      fields: ['email']
    },
    {
      fields: ['status']
    },
    {
      fields: ['department']
    },
    {
      fields: ['position']
    },
    {
      fields: ['hire_date']
    }
  ],
  hooks: {
    beforeCreate: async (employee) => {
      console.log('🔧 beforeCreate hook triggered for employee:', employee.full_name);

      if (!employee.employee_number) {
        try {
          // Generate employee number: EMP-YYYY-XXXX
          const year = new Date().getFullYear();

          // Use a simpler approach to avoid circular dependency
          const { Op } = require('sequelize');
          const count = await sequelize.query(
            `SELECT COUNT(*) as count FROM employees WHERE employee_number LIKE 'EMP-${year}-%'`,
            { type: sequelize.QueryTypes.SELECT }
          );

          const employeeCount = count[0]?.count || 0;
          employee.employee_number = `EMP-${year}-${String(parseInt(employeeCount) + 1).padStart(4, '0')}`;
          console.log(`✅ Generated employee number: ${employee.employee_number}`);
        } catch (error) {
          console.error('❌ Error generating employee number:', error);
          // Fallback to timestamp-based number
          const timestamp = Date.now().toString().slice(-4);
          employee.employee_number = `EMP-${new Date().getFullYear()}-${timestamp}`;
          console.log(`⚠️ Using fallback employee number: ${employee.employee_number}`);
        }
      } else {
        console.log(`✅ Employee number already provided: ${employee.employee_number}`);
      }
    }
  }
});

module.exports = Employee;
