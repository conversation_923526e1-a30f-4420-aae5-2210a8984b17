const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const SalaryAssignment = sequelize.define('SalaryAssignment', {
  assignment_id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  employee_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'employees',
      key: 'employee_id'
    }
  },
  base_salary: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: 0
    }
  },
  allowances: {
    type: DataTypes.JSONB,
    defaultValue: {
      housing: 0,
      transport: 0,
      medical: 0,
      food: 0,
      other: 0
    }
  },
  bonuses: {
    type: DataTypes.JSONB,
    defaultValue: {
      performance: 0,
      annual: 0,
      project: 0,
      other: 0
    }
  },
  deductions: {
    type: DataTypes.JSONB,
    defaultValue: {
      tax: 0,
      insurance: 0,
      pension: 0,
      loan: 0,
      other: 0
    }
  },
  currency: {
    type: DataTypes.STRING,
    defaultValue: 'USD'
  },
  pay_frequency: {
    type: DataTypes.ENUM('weekly', 'bi_weekly', 'monthly', 'quarterly', 'annually'),
    defaultValue: 'monthly'
  },
  effective_date: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  end_date: {
    type: DataTypes.DATE
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  overtime_rate: {
    type: DataTypes.DECIMAL(5, 2),
    defaultValue: 1.5 // 1.5x regular rate
  },
  notes: {
    type: DataTypes.TEXT
  },
  created_by: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'user_id'
    }
  },
  approved_by: {
    type: DataTypes.UUID,
    references: {
      model: 'users',
      key: 'user_id'
    }
  },
  approval_date: {
    type: DataTypes.DATE
  }
}, {
  tableName: 'salary_assignments',
  indexes: [
    {
      fields: ['employee_id']
    },
    {
      fields: ['effective_date']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['pay_frequency']
    }
  ],
  hooks: {
    beforeCreate: async (assignment) => {
      // Deactivate previous assignments for the same employee
      await SalaryAssignment.update(
        { is_active: false, end_date: new Date() },
        {
          where: {
            employee_id: assignment.employee_id,
            is_active: true
          }
        }
      );
    }
  }
});

module.exports = SalaryAssignment;
