
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { LayoutDashboardIcon, PackageIcon, ShoppingCartIcon, DollarSignIcon, UsersIcon, BarChart3Icon, BellIcon, SettingsIcon, DatabaseIcon, LogOutIcon } from 'lucide-react';
const Sidebar = () => {
  const {
    user,
    logout
  } = useAuth();
  const location = useLocation();
  const isAdmin = user?.role === 'admin';
  const navItems = [...(isAdmin ? [{
    name: 'Dashboard',
    path: '/admin/dashboard',
    icon: <LayoutDashboardIcon size={20} />
  }] : [{
    name: 'Dashboard',
    path: '/salesman/dashboard',
    icon: <LayoutDashboardIcon size={20} />
  }]), ...(isAdmin ? [{
    name: 'Inventory',
    path: '/inventory',
    icon: <PackageIcon size={20} />,
    submenu: [{
      name: 'Inventory In',
      path: '/inventory/in'
    }, {
      name: 'Inventory Out',
      path: '/inventory/out'
    }, {
      name: 'History',
      path: '/inventory/history'
    }]
  }] : []), {
    name: 'Sales',
    path: '/sales',
    icon: <ShoppingCartIcon size={20} />,
    submenu: [{
      name: 'Orders',
      path: '/sales/orders'
    }, ...(isAdmin ? [{
      name: 'Commissions',
      path: '/sales/commissions'
    }] : []), ...(isAdmin ? [{
      name: 'Credits',
      path: '/sales/credits'
    }] : [])]
  }, ...(isAdmin ? [{
    name: 'Finances',
    path: '/finances',
    icon: <DollarSignIcon size={20} />,
    submenu: [{
      name: 'Expenses',
      path: '/finances/expenses'
    }, {
      name: 'Salaries',
      path: '/finances/salaries'
    }, {
      name: 'Billing',
      path: '/finances/billing'
    }]
  }, {
    name: 'Customers',
    path: '/customers',
    icon: <UsersIcon size={20} />
  }, {
    name: 'Reports',
    path: '/reports',
    icon: <BarChart3Icon size={20} />
  }] : []), {
    name: 'Notifications',
    path: '/notifications',
    icon: <BellIcon size={20} />
  }, ...(isAdmin ? [{
    name: 'Settings',
    path: '/settings',
    icon: <SettingsIcon size={20} />
  }, {
    name: 'Database Monitor',
    path: '/monitor/database',
    icon: <DatabaseIcon size={20} />
  }] : [])];
  const isActive = (path) => {
    return location.pathname === path;
  };

  const isParentActive = (item) => {
    if (item.submenu) {
      return item.submenu.some(subitem => location.pathname === subitem.path);
    }
    return location.pathname === item.path;
  };
  return (
    <div className="bg-blue-800 text-white w-64 flex-shrink-0 hidden md:block h-screen overflow-y-auto">
      <div className="p-4">
        <h2 className="text-2xl font-bold tracking-tight">ZIDAN Enterprises</h2>
        <p className="text-blue-200 text-sm">
          {user?.role === 'admin' ? 'Administrator' : 'Salesman'}
        </p>
      </div>
      <nav className="mt-6">
        <ul className="space-y-1">
          {navItems.map(item => (
            <li key={item.name}>
              {item.submenu ? (
                // Parent item with submenu - not clickable, just shows submenu
                <div className={`flex items-center px-4 py-3 ${isParentActive(item) ? 'bg-blue-700' : ''}`}>
                  <span className="mr-3">{item.icon}</span>
                  <span>{item.name}</span>
                </div>
              ) : (
                // Regular navigation item
                <Link to={item.path} className={`flex items-center px-4 py-3 hover:bg-blue-700 transition-colors ${isActive(item.path) ? 'bg-blue-700' : ''}`}>
                  <span className="mr-3">{item.icon}</span>
                  <span>{item.name}</span>
                </Link>
              )}
              {item.submenu && (
                <ul className="ml-8 mt-1 space-y-1">
                  {item.submenu.map(subitem => (
                    <li key={subitem.name}>
                      <Link to={subitem.path} className={`block px-4 py-2 text-sm hover:bg-blue-700 transition-colors rounded-md ${isActive(subitem.path) ? 'bg-blue-700 font-medium' : ''}`}>
                        {subitem.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              )}
            </li>
          ))}
          <li>
            <button onClick={logout} className="flex items-center w-full px-4 py-3 hover:bg-blue-700">
              <span className="mr-3">
                <LogOutIcon size={20} />
              </span>
              <span>Logout</span>
            </button>
          </li>
        </ul>
      </nav>
    </div>
  );
};
export default Sidebar;
