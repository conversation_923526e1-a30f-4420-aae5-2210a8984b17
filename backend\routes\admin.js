const express = require('express');
const router = express.Router();
const { Sequelize } = require('sequelize');
const {
  User,
  Salesman,
  Customer,
  Product,
  Order,
  OrderItem,
  Payment,
  Expense,
  InventoryTransaction,
  Supplier,
  Category,
  sequelize
} = require('../models');
const { authenticateToken, adminOnly } = require('../middleware/auth');
const chalk = require('chalk');
const moment = require('moment');

// Test endpoint (no auth required for testing)
router.get('/test', (req, res) => {
  res.json({
    success: true,
    message: 'Admin routes are working!',
    timestamp: new Date().toISOString()
  });
});

// Test foreign key constraint handling
router.get('/test-fk', async (req, res) => {
  try {
    console.log(chalk.blue('🧪 Testing foreign key constraint handling...'));

    // Test disabling and enabling foreign keys
    await sequelize.query('PRAGMA foreign_keys = OFF');
    console.log(chalk.green('✅ Foreign keys disabled successfully'));

    await sequelize.query('PRAGMA foreign_keys = ON');
    console.log(chalk.green('✅ Foreign keys enabled successfully'));

    res.json({
      success: true,
      message: 'Foreign key constraint handling is working',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error(chalk.red('❌ Foreign key test error:'), error);
    res.status(500).json({
      success: false,
      message: `Foreign key test failed: ${error.message}`
    });
  }
});

// Simple database stats endpoint (no auth required for testing)
router.get('/database/stats/simple', async (req, res) => {
  try {
    console.log(chalk.blue(`\n📊 [${moment().format('YYYY-MM-DD HH:mm:ss.SSS')}] SIMPLE DATABASE STATS REQUEST`));

    const models = {
      users: User,
      customers: Customer,
      products: Product,
      orders: Order,
      order_items: OrderItem,
      payments: Payment,
      expenses: Expense,
      inventory_transactions: InventoryTransaction,
      salesmen: Salesman,
      suppliers: Supplier,
      categories: Category
    };

    const stats = {
      tables: {},
      health: { status: 'healthy', message: 'Database connection successful' },
      lastUpdated: {},
      dataIntegrity: {},
      timestamp: new Date().toISOString()
    };

    // Get record counts
    for (const [tableName, Model] of Object.entries(models)) {
      try {
        const count = await Model.count();
        stats.tables[tableName] = count;
        console.log(`✅ ${tableName}: ${count} records`);
      } catch (error) {
        console.error(`❌ Error counting ${tableName}:`, error.message);
        stats.tables[tableName] = 0;
      }
    }

    // Calculate total records
    stats.totalRecords = Object.values(stats.tables).reduce((sum, count) => sum + count, 0);

    console.log(chalk.green(`✅ Simple database stats generated successfully`));
    res.json({ success: true, data: stats });

  } catch (error) {
    console.error(chalk.red('❌ Simple database stats error:'), error);
    res.status(500).json({ success: false, message: error.message });
  }
});

// Simple table data endpoint (no auth required for testing)
router.get('/data/:table/simple', async (req, res) => {
  try {
    const { table } = req.params;
    const { limit = 50 } = req.query;

    console.log(chalk.blue(`\n📋 [${moment().format('YYYY-MM-DD HH:mm:ss.SSS')}] SIMPLE TABLE DATA REQUEST:`));
    console.log(chalk.white(`Table: ${table.toUpperCase()}, Limit: ${limit}`));

    const models = {
      users: User,
      customers: Customer,
      products: Product,
      orders: Order,
      order_items: OrderItem,
      payments: Payment,
      expenses: Expense,
      inventory_transactions: InventoryTransaction,
      salesmen: Salesman,
      suppliers: Supplier
    };

    const Model = models[table];
    if (!Model) {
      return res.status(400).json({ success: false, message: 'Invalid table name' });
    }

    // Fetch data with limit
    const data = await Model.findAll({
      limit: parseInt(limit),
      order: [['created_at', 'DESC']],
      raw: true
    });

    console.log(chalk.green(`✅ Simple table data retrieved: ${data.length} records`));
    res.json({
      success: true,
      data: data,
      metadata: {
        table,
        recordCount: data.length,
        columns: Object.keys(Model.rawAttributes)
      }
    });

  } catch (error) {
    console.error(chalk.red('❌ Simple table data error:'), error);
    res.status(500).json({ success: false, message: error.message });
  }
});

// Clean up malformed categories (UUIDs as names)
router.post('/cleanup-categories', async (req, res) => {
  try {
    console.log(chalk.blue('🧹 Starting category cleanup...'));

    // Find categories with UUID-like names (36 characters with dashes)
    // SQLite doesn't have REGEXP, so we'll get all categories and filter in JavaScript
    const allCategories = await Category.findAll();

    const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    const malformedCategories = allCategories.filter(category =>
      uuidPattern.test(category.name)
    );

    console.log(`🔍 Found ${malformedCategories.length} malformed categories`);

    let deletedCount = 0;
    for (const category of malformedCategories) {
      console.log(`🗑️ Deleting malformed category: ${category.name}`);
      await category.destroy();
      deletedCount++;
    }

    // Also clean up empty categories
    const emptyCategories = await Category.findAll({
      where: {
        [Sequelize.Op.or]: [
          { name: '' },
          { name: null },
          { name: 'Select category' }
        ]
      }
    });

    for (const category of emptyCategories) {
      console.log(`🗑️ Deleting empty category: "${category.name}"`);
      await category.destroy();
      deletedCount++;
    }

    console.log(`✅ Cleanup complete: ${deletedCount} malformed/empty categories deleted`);

    res.json({
      success: true,
      message: `Category cleanup complete: ${deletedCount} malformed/empty categories deleted`,
      deletedCount,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error(chalk.red('❌ Category cleanup error:'), error);
    res.status(500).json({
      success: false,
      message: `Category cleanup failed: ${error.message}`
    });
  }
});

// Apply authentication and admin-only access to protected routes
// Note: Simple endpoints are above this middleware, so they don't require auth
router.use('/database/stats', authenticateToken, adminOnly); // Protect the enhanced stats endpoint
router.use('/data/:table', authenticateToken, adminOnly); // Protect enhanced data endpoints (but not simple ones)
router.use('/reset', authenticateToken, adminOnly);
router.use('/users', authenticateToken, adminOnly);

// ============================================================================
// ENHANCED DATA STATISTICS AND HEALTH ENDPOINTS
// ============================================================================

// Get comprehensive database statistics with health metrics
router.get('/database/stats', async (req, res) => {
  try {
    console.log(chalk.blue(`\n📊 [${moment().format('YYYY-MM-DD HH:mm:ss.SSS')}] ENHANCED DATABASE STATS REQUEST`));

    const models = {
      users: User,
      customers: Customer,
      products: Product,
      orders: Order,
      order_items: OrderItem,
      payments: Payment,
      expenses: Expense,
      inventory_transactions: InventoryTransaction,
      salesmen: Salesman,
      suppliers: Supplier
    };

    const stats = {
      tables: {},
      health: {},
      lastUpdated: {},
      dataIntegrity: {},
      timestamp: new Date().toISOString()
    };

    // Get record counts and last updated timestamps
    for (const [tableName, Model] of Object.entries(models)) {
      try {
        const count = await Model.count();
        stats.tables[tableName] = count;

        // Get last updated timestamp
        if (count > 0) {
          const lastRecord = await Model.findOne({
            order: [['updated_at', 'DESC']],
            attributes: ['updated_at']
          });
          stats.lastUpdated[tableName] = lastRecord?.updated_at || null;
        } else {
          stats.lastUpdated[tableName] = null;
        }

        // Basic health check
        stats.health[tableName] = {
          status: count >= 0 ? 'healthy' : 'error',
          recordCount: count,
          isEmpty: count === 0
        };

      } catch (error) {
        console.error(`Error getting stats for ${tableName}:`, error);
        stats.tables[tableName] = 0;
        stats.health[tableName] = {
          status: 'error',
          error: error.message
        };
      }
    }

    // Data integrity checks
    try {
      // Check for orphaned records
      const orphanedOrderItems = await OrderItem.count({
        include: [{
          model: Order,
          as: 'order',
          required: false
        }],
        where: {
          '$order.order_id$': null
        }
      });

      const orphanedPayments = await Payment.count({
        include: [{
          model: Order,
          as: 'order',
          required: false
        }],
        where: {
          '$order.order_id$': null
        }
      });

      stats.dataIntegrity = {
        orphanedOrderItems,
        orphanedPayments,
        totalOrphaned: orphanedOrderItems + orphanedPayments
      };

    } catch (error) {
      console.error('Error checking data integrity:', error);
      stats.dataIntegrity = { error: error.message };
    }

    // Calculate total records
    stats.totalRecords = Object.values(stats.tables).reduce((sum, count) => sum + count, 0);

    console.log(chalk.green(`✅ Enhanced database stats generated successfully`));
    res.json({ success: true, data: stats });

  } catch (error) {
    console.error(chalk.red('❌ Enhanced database stats error:'), error);
    res.status(500).json({ success: false, message: error.message });
  }
});

// Enhanced data viewer with pagination, filtering, and export
router.get('/data/:table', async (req, res) => {
  try {
    const { table } = req.params;
    const {
      limit = 50,
      offset = 0,
      search = '',
      sortBy = 'created_at',
      sortOrder = 'DESC',
      export: exportFormat = null
    } = req.query;

    console.log(chalk.blue(`\n📋 [${moment().format('YYYY-MM-DD HH:mm:ss.SSS')}] ENHANCED DATA VIEWER REQUEST:`));
    console.log(chalk.white(`Table: ${table.toUpperCase()}, Limit: ${limit}, Offset: ${offset}`));

    const models = {
      users: User,
      customers: Customer,
      products: Product,
      orders: Order,
      order_items: OrderItem,
      payments: Payment,
      expenses: Expense,
      inventory_transactions: InventoryTransaction,
      salesmen: Salesman,
      suppliers: Supplier
    };

    const Model = models[table];
    if (!Model) {
      return res.status(400).json({ success: false, message: 'Invalid table name' });
    }

    // Build search conditions
    const whereConditions = {};
    if (search) {
      // Get model attributes for search
      const attributes = Object.keys(Model.rawAttributes);
      const searchConditions = attributes
        .filter(attr => ['STRING', 'TEXT'].includes(Model.rawAttributes[attr].type.constructor.name))
        .map(attr => ({
          [attr]: {
            [sequelize.Op.like]: `%${search}%`
          }
        }));

      if (searchConditions.length > 0) {
        whereConditions[sequelize.Op.or] = searchConditions;
      }
    }

    // Get total count for pagination
    const totalCount = await Model.count({ where: whereConditions });

    // Fetch data with pagination and sorting
    const data = await Model.findAll({
      where: whereConditions,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [[sortBy, sortOrder.toUpperCase()]],
      raw: true
    });

    const response = {
      success: true,
      data: {
        records: data,
        pagination: {
          total: totalCount,
          limit: parseInt(limit),
          offset: parseInt(offset),
          pages: Math.ceil(totalCount / limit),
          currentPage: Math.floor(offset / limit) + 1
        },
        metadata: {
          table,
          columns: Object.keys(Model.rawAttributes),
          searchTerm: search,
          sortBy,
          sortOrder
        }
      }
    };

    // Handle export formats
    if (exportFormat === 'csv') {
      const csv = convertToCSV(data);
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="${table}_export_${moment().format('YYYY-MM-DD')}.csv"`);
      return res.send(csv);
    }

    if (exportFormat === 'json') {
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', `attachment; filename="${table}_export_${moment().format('YYYY-MM-DD')}.json"`);
      return res.json(data);
    }

    console.log(chalk.green(`✅ Data retrieved: ${data.length} records`));
    res.json(response);

  } catch (error) {
    console.error(chalk.red('❌ Enhanced data viewer error:'), error);
    res.status(500).json({ success: false, message: error.message });
  }
});

// Utility function to convert data to CSV
function convertToCSV(data) {
  if (!data.length) return '';

  const headers = Object.keys(data[0]);
  const csvHeaders = headers.join(',');

  const csvRows = data.map(row =>
    headers.map(header => {
      const value = row[header];
      // Escape commas and quotes in CSV
      if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
        return `"${value.replace(/"/g, '""')}"`;
      }
      return value;
    }).join(',')
  );

  return [csvHeaders, ...csvRows].join('\n');
}

// ============================================================================
// RESET/DELETE FUNCTIONALITY WITH SAFETY MEASURES
// ============================================================================

// Reset specific table data (SAFER VERSION)
router.delete('/reset/:table', async (req, res) => {
  let transaction;

  try {
    const { table } = req.params;
    const { confirmationText } = req.body;

    console.log(chalk.red(`\n🗑️ [${moment().format('YYYY-MM-DD HH:mm:ss.SSS')}] TABLE RESET REQUEST:`));
    console.log(chalk.white(`Table: ${table.toUpperCase()}, User: ${req.user?.email || 'Unknown'}`));

    // Validate confirmation text
    if (confirmationText !== 'DELETE') {
      console.log(chalk.yellow(`⚠️ Invalid confirmation text: "${confirmationText}"`));
      return res.status(400).json({
        success: false,
        message: 'Invalid confirmation text. Please type "DELETE" to confirm.'
      });
    }

    // Start transaction with timeout
    transaction = await sequelize.transaction({
      isolationLevel: Sequelize.Transaction.ISOLATION_LEVELS.READ_COMMITTED,
      timeout: 30000 // 30 second timeout
    });

    const models = {
      users: User,
      customers: Customer,
      products: Product,
      orders: Order,
      order_items: OrderItem,
      payments: Payment,
      expenses: Expense,
      inventory_transactions: InventoryTransaction,
      salesmen: Salesman,
      suppliers: Supplier,
      categories: Category
    };

    const Model = models[table];
    if (!Model) {
      console.log(chalk.yellow(`⚠️ Invalid table name: ${table}`));
      return res.status(400).json({ success: false, message: 'Invalid table name' });
    }

    console.log(chalk.blue(`🔍 Checking record count for ${table}...`));

    // Get count before deletion for logging (with timeout)
    const countBefore = await Promise.race([
      Model.count(),
      new Promise((_, reject) => setTimeout(() => reject(new Error('Count query timeout')), 10000))
    ]);

    console.log(chalk.blue(`📊 Found ${countBefore} records in ${table}`));

    if (countBefore === 0) {
      console.log(chalk.green(`✅ Table ${table} is already empty`));
      return res.json({
        success: true,
        message: `Table ${table} is already empty`,
        recordsDeleted: 0
      });
    }

    console.log(chalk.yellow(`🗑️ Starting deletion of ${countBefore} records from ${table}...`));

    // FOREIGN KEY AWARE RESET LOGIC
    let recordsDeleted = 0;

    // Temporarily disable foreign key constraints for this transaction
    console.log(chalk.blue(`🔓 Temporarily disabling foreign key constraints...`));
    await sequelize.query('PRAGMA foreign_keys = OFF', { transaction });

    // Verify foreign keys are disabled
    const [fkResult] = await sequelize.query('PRAGMA foreign_keys', { transaction });
    console.log(chalk.blue(`🔍 Foreign keys status: ${fkResult[0]?.foreign_keys === 0 ? 'DISABLED' : 'ENABLED'}`));

    try {
      if (table === 'users') {
        // Special case: Don't delete current user
        console.log(chalk.blue(`👤 Deleting users except current user: ${req.user?.user_id}`));
        recordsDeleted = await Model.destroy({
          where: {
            user_id: { [Sequelize.Op.ne]: req.user?.user_id || 'none' }
          },
          transaction,
          force: true
        });
      } else if (table === 'customers') {
        // For customers, delete related data first
        console.log(chalk.blue(`🗑️ Deleting customer-related data first...`));

        // Delete order items for customer orders
        const customerOrderItems = await sequelize.query(`
          DELETE FROM order_items
          WHERE order_id IN (
            SELECT order_id FROM orders WHERE customer_id IN (
              SELECT customer_id FROM customers
            )
          )
        `, { transaction });

        // Delete payments for customers
        await Payment.destroy({ where: {}, transaction, force: true });

        // Delete orders for customers
        await Order.destroy({ where: {}, transaction, force: true });

        // Delete inventory transactions for customers
        await InventoryTransaction.destroy({
          where: { customer_id: { [Sequelize.Op.ne]: null } },
          transaction,
          force: true
        });

        // Finally delete customers
        recordsDeleted = await Customer.destroy({ where: {}, transaction, force: true });

      } else if (table === 'products') {
        // For products, delete related data first
        console.log(chalk.blue(`🗑️ Deleting product-related data first...`));

        // Delete order items that reference products
        await OrderItem.destroy({ where: {}, transaction, force: true });

        // Delete inventory transactions for products
        await InventoryTransaction.destroy({
          where: { product_id: { [Sequelize.Op.ne]: null } },
          transaction,
          force: true
        });

        // Finally delete products
        recordsDeleted = await Product.destroy({ where: {}, transaction, force: true });

      } else if (table === 'orders') {
        // For orders, delete related data first
        console.log(chalk.blue(`🗑️ Deleting order-related data first...`));

        // Delete order items first
        await OrderItem.destroy({ where: {}, transaction, force: true });

        // Delete payments for orders
        await Payment.destroy({
          where: { order_id: { [Sequelize.Op.ne]: null } },
          transaction,
          force: true
        });

        // Delete inventory transactions for orders
        await InventoryTransaction.destroy({
          where: { order_id: { [Sequelize.Op.ne]: null } },
          transaction,
          force: true
        });

        // Finally delete orders
        recordsDeleted = await Order.destroy({ where: {}, transaction, force: true });

      } else if (table === 'categories') {
        // For categories, delete products that reference categories first
        console.log(chalk.blue(`🗑️ Deleting products that reference categories first...`));

        // Delete order items that reference products
        await OrderItem.destroy({ where: {}, transaction, force: true });

        // Delete inventory transactions for products
        await InventoryTransaction.destroy({
          where: { product_id: { [Sequelize.Op.ne]: null } },
          transaction,
          force: true
        });

        // Delete products that reference categories
        await Product.destroy({ where: {}, transaction, force: true });

        // Finally delete categories
        recordsDeleted = await Category.destroy({ where: {}, transaction, force: true });

      } else if (table === 'suppliers') {
        // For suppliers, delete products that reference suppliers first
        console.log(chalk.blue(`🗑️ Deleting products that reference suppliers first...`));

        // Delete order items that reference products
        await OrderItem.destroy({ where: {}, transaction, force: true });

        // Delete inventory transactions for products
        await InventoryTransaction.destroy({
          where: { product_id: { [Sequelize.Op.ne]: null } },
          transaction,
          force: true
        });

        // Delete products that reference suppliers
        await Product.destroy({ where: {}, transaction, force: true });

        // Finally delete suppliers
        recordsDeleted = await Supplier.destroy({ where: {}, transaction, force: true });

      } else if (table === 'salesmen') {
        // For salesmen, delete orders that reference salesmen first
        console.log(chalk.blue(`🗑️ Deleting orders that reference salesmen first...`));

        // Delete order items first
        await OrderItem.destroy({ where: {}, transaction, force: true });

        // Delete payments for orders
        await Payment.destroy({
          where: { order_id: { [Sequelize.Op.ne]: null } },
          transaction,
          force: true
        });

        // Delete inventory transactions for orders
        await InventoryTransaction.destroy({
          where: { order_id: { [Sequelize.Op.ne]: null } },
          transaction,
          force: true
        });

        // Delete orders that reference salesmen
        await Order.destroy({ where: {}, transaction, force: true });

        // Finally delete salesmen
        recordsDeleted = await Salesman.destroy({ where: {}, transaction, force: true });

      } else {
        // For other tables (payments, expenses, inventory_transactions, order_items), simple deletion should work
        console.log(chalk.blue(`🗑️ Deleting all records from ${table}...`));
        recordsDeleted = await Model.destroy({
          where: {},
          transaction,
          force: true
        });
      }

      console.log(chalk.green(`✅ Deleted ${recordsDeleted} records from ${table}`));

    } finally {
      // Re-enable foreign key constraints
      console.log(chalk.blue(`🔒 Re-enabling foreign key constraints...`));
      try {
        await sequelize.query('PRAGMA foreign_keys = ON', { transaction });

        // Verify foreign keys are re-enabled
        const [fkResult] = await sequelize.query('PRAGMA foreign_keys', { transaction });
        console.log(chalk.blue(`🔍 Foreign keys status after re-enable: ${fkResult[0]?.foreign_keys === 1 ? 'ENABLED' : 'DISABLED'}`));
      } catch (fkError) {
        console.error(chalk.red(`❌ Error re-enabling foreign keys: ${fkError.message}`));
        // Don't throw here as the main operation might have succeeded
      }
    }

    console.log(chalk.blue(`💾 Committing transaction...`));
    await transaction.commit();
    console.log(chalk.green(`✅ Transaction committed successfully`));

    // Log the deletion
    console.log(chalk.red(`🗑️ TABLE RESET COMPLETED:`));
    console.log(chalk.white(`Table: ${table}, Records Deleted: ${recordsDeleted}, User: ${req.user?.email || 'Unknown'}`));

    const response = {
      success: true,
      message: `Successfully reset ${table} table`,
      recordsDeleted,
      timestamp: new Date().toISOString()
    };

    console.log(chalk.green(`📤 Sending success response:`, JSON.stringify(response, null, 2)));
    res.json(response);

  } catch (error) {
    console.error(chalk.red('❌ Table reset error:'), error);

    // Rollback transaction if it exists
    if (transaction) {
      try {
        console.log(chalk.yellow(`🔄 Rolling back transaction...`));
        await transaction.rollback();
        console.log(chalk.yellow(`✅ Transaction rolled back successfully`));
      } catch (rollbackError) {
        console.error(chalk.red('❌ Rollback error:'), rollbackError);
      }
    }

    const errorResponse = {
      success: false,
      message: `Failed to reset table: ${error.message}`,
      error: error.name,
      timestamp: new Date().toISOString()
    };

    console.log(chalk.red(`📤 Sending error response:`, JSON.stringify(errorResponse, null, 2)));
    res.status(500).json(errorResponse);
  }
});

// Reset ALL data (nuclear option) - SAFER VERSION
router.delete('/reset/all', async (req, res) => {
  let transaction;

  try {
    const { confirmationText } = req.body;

    console.log(chalk.red(`\n💥 [${moment().format('YYYY-MM-DD HH:mm:ss.SSS')}] NUCLEAR RESET REQUEST:`));
    console.log(chalk.white(`User: ${req.user?.email || 'Unknown'}`));

    // Validate confirmation text
    if (confirmationText !== 'DELETE ALL DATA') {
      console.log(chalk.yellow(`⚠️ Invalid confirmation text: "${confirmationText}"`));
      return res.status(400).json({
        success: false,
        message: 'Invalid confirmation text. Please type "DELETE ALL DATA" to confirm.'
      });
    }

    // Start transaction with timeout
    transaction = await sequelize.transaction({
      isolationLevel: Sequelize.Transaction.ISOLATION_LEVELS.READ_COMMITTED,
      timeout: 60000 // 60 second timeout for nuclear reset
    });

    console.log(chalk.yellow(`💥 Starting nuclear reset operation...`));

    // Temporarily disable foreign key constraints for nuclear reset
    console.log(chalk.blue(`🔓 Temporarily disabling foreign key constraints...`));
    await sequelize.query('PRAGMA foreign_keys = OFF', { transaction });

    const deletionResults = {};
    let totalDeleted = 0;

    try {
      // FOREIGN KEY AWARE NUCLEAR RESET - Delete in safe order
      const deletionOrder = [
        { model: OrderItem, name: 'order_items' },
        { model: Payment, name: 'payments' },
        { model: Order, name: 'orders' },
        { model: InventoryTransaction, name: 'inventory_transactions' },
        { model: Expense, name: 'expenses' },
        { model: Product, name: 'products' }, // Delete products before categories (products reference categories)
        { model: Customer, name: 'customers' },
        { model: Supplier, name: 'suppliers' },
        { model: Salesman, name: 'salesmen' },
        { model: Category, name: 'categories' } // Delete categories after products
        // Don't delete users to preserve admin access
      ];

      for (const { model, name } of deletionOrder) {
        try {
          console.log(chalk.blue(`🗑️ Deleting ${name}...`));
          const deleted = await model.destroy({
            where: {},
            transaction,
            force: true
          });
          deletionResults[name] = deleted;
          totalDeleted += deleted;
          console.log(chalk.green(`✅ Deleted ${deleted} records from ${name}`));
        } catch (error) {
          console.error(chalk.red(`❌ Error deleting ${name}:`, error.message));
          deletionResults[name] = 0;
        }
      }

    } finally {
      // Re-enable foreign key constraints
      console.log(chalk.blue(`🔒 Re-enabling foreign key constraints...`));
      await sequelize.query('PRAGMA foreign_keys = ON', { transaction });
    }

    console.log(chalk.blue(`💾 Committing nuclear reset transaction...`));
    await transaction.commit();
    console.log(chalk.green(`✅ Nuclear reset transaction committed successfully`));

    // Log the nuclear reset
    console.log(chalk.red(`💥 NUCLEAR RESET COMPLETED:`));
    console.log(chalk.white(`Total Records Deleted: ${totalDeleted}, User: ${req.user?.email || 'Unknown'}`));
    console.log(chalk.white(`Deletion Results:`, JSON.stringify(deletionResults, null, 2)));

    const response = {
      success: true,
      message: 'Successfully reset all data (users preserved)',
      deletionResults,
      totalDeleted,
      timestamp: new Date().toISOString()
    };

    console.log(chalk.green(`📤 Sending nuclear reset success response`));
    res.json(response);

  } catch (error) {
    console.error(chalk.red('❌ Nuclear reset error:'), error);

    // Rollback transaction if it exists
    if (transaction) {
      try {
        console.log(chalk.yellow(`🔄 Rolling back nuclear reset transaction...`));
        await transaction.rollback();
        console.log(chalk.yellow(`✅ Nuclear reset transaction rolled back successfully`));
      } catch (rollbackError) {
        console.error(chalk.red('❌ Nuclear reset rollback error:'), rollbackError);
      }
    }

    const errorResponse = {
      success: false,
      message: `Failed to reset all data: ${error.message}`,
      error: error.name,
      timestamp: new Date().toISOString()
    };

    console.log(chalk.red(`📤 Sending nuclear reset error response`));
    res.status(500).json(errorResponse);
  }
});

// Get all users
router.get('/users', async (req, res) => {
  try {
    const users = await User.findAll({
      attributes: ['user_id', 'email', 'role', 'is_active', 'email_verified', 'created_at'],
      order: [['created_at', 'DESC']]
    });
    res.json({ success: true, data: users });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// Get all customers
router.get('/customers', async (req, res) => {
  try {
    const customers = await Customer.findAll({
      order: [['created_at', 'DESC']]
    });
    res.json({ success: true, data: customers });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// Get all products
router.get('/products', async (req, res) => {
  try {
    const products = await Product.findAll({
      order: [['created_at', 'DESC']]
    });
    res.json({ success: true, data: products });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// Get all orders
router.get('/orders', async (req, res) => {
  try {
    const orders = await Order.findAll({
      order: [['created_at', 'DESC']]
    });
    res.json({ success: true, data: orders });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// Get all payments
router.get('/payments', async (req, res) => {
  try {
    const payments = await Payment.findAll({
      order: [['created_at', 'DESC']]
    });
    res.json({ success: true, data: payments });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// Get all expenses
router.get('/expenses', async (req, res) => {
  try {
    const expenses = await Expense.findAll({
      order: [['created_at', 'DESC']]
    });
    res.json({ success: true, data: expenses });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// Get database statistics
router.get('/stats', async (req, res) => {
  try {
    const stats = {
      users: await User.count(),
      customers: await Customer.count(),
      products: await Product.count(),
      orders: await Order.count(),
      payments: await Payment.count(),
      expenses: await Expense.count()
    };
    res.json({ success: true, data: stats });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

module.exports = router;
