@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Custom styles */
body {
  @apply antialiased text-gray-800;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Better focus styles */
*:focus {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
::-webkit-scrollbar-track {
  @apply bg-gray-100;
}
::-webkit-scrollbar-thumb {
  @apply bg-gray-400 rounded-full;
}
::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-500;
}

/* Transitions */
.custom-transition {
  transition: all 200ms ease-in-out;
}

/* Input styles */
input, select, textarea {
  @apply transition-colors duration-200;
}

/* Button hover effects */
button {
  @apply transition-colors duration-200;
}

/* Card hover effects */
.hover-card {
  @apply transition-transform duration-200 hover:shadow-lg hover:-translate-y-1;
}