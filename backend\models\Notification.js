const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Notification = sequelize.define('Notification', {
  notification_id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  type: {
    type: DataTypes.ENUM('stage_progression', 'commission_milestone', 'system_alert'),
    allowNull: false,
    defaultValue: 'stage_progression'
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false
  },
  message: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  data: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Additional data like salesman_id, stage_id, sales_amount, etc.'
  },
  is_read: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  priority: {
    type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
    defaultValue: 'medium'
  },
  target_user_id: {
    type: DataTypes.UUID,
    allowNull: true,
    comment: 'If null, notification is for all admin users'
  },
  created_by: {
    type: DataTypes.UUID,
    allowNull: true,
    comment: 'User who triggered the notification (can be system)'
  },
  expires_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'When notification should be automatically dismissed'
  }
}, {
  tableName: 'notifications',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Associations
Notification.associate = (models) => {
  // Notification belongs to target user (optional)
  Notification.belongsTo(models.User, {
    foreignKey: 'target_user_id',
    as: 'targetUser',
    onDelete: 'CASCADE'
  });

  // Notification created by user (optional)
  Notification.belongsTo(models.User, {
    foreignKey: 'created_by',
    as: 'createdBy',
    onDelete: 'SET NULL'
  });
};

// Instance methods
Notification.prototype.markAsRead = async function() {
  this.is_read = true;
  await this.save();
  return this;
};

// Static methods
Notification.createStageProgressionNotification = async function(salesmanData, stageData, salesAmount) {
  const notification = await this.create({
    type: 'stage_progression',
    title: `Salesman Exceeded Commission Stage`,
    message: `${salesmanData.name} has exceeded their current commission stage with $${salesAmount.toLocaleString()} in sales. Consider creating the next commission stage.`,
    data: {
      salesman_id: salesmanData.salesman_id,
      salesman_name: salesmanData.name,
      current_stage_id: stageData?.stage_id,
      current_stage_name: stageData?.stage_name,
      sales_amount: salesAmount,
      exceeded_at: new Date()
    },
    priority: 'high',
    target_user_id: null // For all admin users
  });

  return notification;
};

Notification.getUnreadForUser = async function(userId = null) {
  const whereClause = {
    is_read: false
  };

  if (userId) {
    whereClause.target_user_id = userId;
  } else {
    whereClause.target_user_id = null; // System-wide notifications
  }

  return await this.findAll({
    where: whereClause,
    order: [['created_at', 'DESC']],
    limit: 50
  });
};

Notification.markAllAsReadForUser = async function(userId = null) {
  const whereClause = {
    is_read: false
  };

  if (userId) {
    whereClause.target_user_id = userId;
  } else {
    whereClause.target_user_id = null;
  }

  return await this.update(
    { is_read: true },
    { where: whereClause }
  );
};

module.exports = Notification;
