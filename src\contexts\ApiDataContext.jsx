import { createContext, useContext, useState, useEffect, useMemo, useCallback } from 'react';
import {
  productsAPI,
  customersAPI,
  ordersAPI,
  inventoryAPI,
  paymentsAPI,
  expensesAPI,
  dashboardAPI,
  suppliersAPI,
  categoriesAPI
} from '../services/api';

// Create the context
const ApiDataContext = createContext(undefined);

export const ApiDataProvider = ({ children }) => {
  // Core business entities - Load from API
  const [products, setProducts] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [orders, setOrders] = useState([]);
  const [payments, setPayments] = useState([]);
  const [expenses, setExpenses] = useState([]);
  const [inventoryTransactions, setInventoryTransactions] = useState([]);
  const [suppliers, setSuppliers] = useState([]);
  const [categories, setCategories] = useState([]);

  // Loading states
  const [loading, setLoading] = useState({
    products: false,
    customers: false,
    orders: false,
    payments: false,
    expenses: false,
    transactions: false,
    suppliers: false,
    categories: false
  });

  // Error states
  const [errors, setErrors] = useState({});

  // Helper function to update loading state
  const setLoadingState = useCallback((key, value) => {
    setLoading(prev => ({ ...prev, [key]: value }));
  }, []);

  // Helper function to handle API errors
  const handleError = useCallback((key, error) => {
    console.error(`API Error (${key}):`, error);
    setErrors(prev => ({ ...prev, [key]: error.message }));
  }, []);

  // ============================================================================
  // DATA FETCHING FUNCTIONS
  // ============================================================================

  const fetchProducts = useCallback(async (params = {}) => {
    try {
      setLoadingState('products', true);
      const response = await productsAPI.getAll(params);
      if (response.success) {
        // Transform backend data to match component expectations
        const transformedProducts = (response.data.products || []).map(product => ({
          id: product.product_id,
          name: product.name,
          category: product.category || 'Uncategorized',
          sku: product.sku,
          barcode: product.barcode,
          description: product.description,
          price: product.unit_price,
          costPrice: product.cost_price,
          stock: product.current_stock,
          minStock: product.min_stock_level,
          maxStock: product.max_stock_level,
          supplier: product.supplier?.name || 'Unknown Supplier',
          supplierId: product.supplier_id,
          storageLocation: product.storage_location,
          batchNumber: product.batch_number,
          expirationDate: product.expiration_date?.split('T')[0],
          lastRestocked: product.last_restocked?.split('T')[0],
          status: product.status || 'active'
        }));
        setProducts(transformedProducts);
        console.log('🔍 ApiDataContext - Products fetched and transformed:', {
          rawCount: response.data.products?.length || 0,
          transformedCount: transformedProducts.length,
          sampleProduct: transformedProducts[0]
        });
      }
    } catch (error) {
      handleError('products', error);
    } finally {
      setLoadingState('products', false);
    }
  }, [setLoadingState, handleError]);

  const fetchCustomers = useCallback(async (params = {}) => {
    try {
      setLoadingState('customers', true);
      const response = await customersAPI.getAll(params);
      if (response.success) {
        setCustomers(response.data.customers || []);
      }
    } catch (error) {
      handleError('customers', error);
    } finally {
      setLoadingState('customers', false);
    }
  }, [setLoadingState, handleError]);

  const fetchOrders = useCallback(async (params = {}) => {
    try {
      setLoadingState('orders', true);
      const response = await ordersAPI.getAll(params);
      if (response.success) {
        setOrders(response.data.orders || []);
      }
    } catch (error) {
      handleError('orders', error);
    } finally {
      setLoadingState('orders', false);
    }
  }, [setLoadingState, handleError]);

  const fetchInventoryTransactions = useCallback(async (params = {}) => {
    try {
      setLoadingState('transactions', true);
      const response = await inventoryAPI.getTransactions(params);
      if (response.success) {
        // Transform backend data to match component expectations
        const transformedTransactions = (response.data.transactions || []).map(transaction => ({
          id: transaction.transaction_id,
          type: transaction.type,
          productId: transaction.product_id,
          productName: transaction.product?.name || 'Unknown Product',
          quantity: transaction.quantity,
          unitPrice: transaction.unit_price,
          totalCost: transaction.total_cost,
          supplier: transaction.supplier?.name || '-',
          customer: transaction.customer?.name || '-',
          date: transaction.transaction_date?.split('T')[0] || new Date().toISOString().split('T')[0],
          status: transaction.status,
          notes: transaction.notes,
          recordedBy: transaction.recorder?.email || transaction.recorded_by,
          invoiceNumber: transaction.invoice_number,
          referenceNumber: transaction.reference_number,
          batchNumber: transaction.batch_number,
          expirationDate: transaction.expiration_date?.split('T')[0],
          storageLocation: transaction.storage_location,
          // FIXED: Preserve order relationship data for salesman information display
          // This ensures that order and salesman data from backend is available in frontend components
          order: transaction.order ? {
            order_id: transaction.order.order_id,
            order_number: transaction.order.order_number,
            salesman_id: transaction.order.salesman_id,
            salesman: transaction.order.salesman ? {
              salesman_id: transaction.order.salesman.salesman_id,
              full_name: transaction.order.salesman.full_name
            } : null
          } : null
        }));
        setInventoryTransactions(transformedTransactions);
        console.log('🔍 ApiDataContext - Transactions fetched and transformed:', {
          rawCount: response.data.transactions?.length || 0,
          transformedCount: transformedTransactions.length,
          sampleRawTransaction: response.data.transactions?.[0],
          sampleTransformedTransaction: transformedTransactions[0],
          outTransactionsWithOrders: transformedTransactions.filter(t => t.type === 'out' && t.order).length
        });
      }
    } catch (error) {
      handleError('transactions', error);
    } finally {
      setLoadingState('transactions', false);
    }
  }, [setLoadingState, handleError]);

  const fetchSuppliers = useCallback(async (params = {}) => {
    try {
      setLoadingState('suppliers', true);
      const response = await suppliersAPI.getAll(params);
      if (response.success) {
        // Transform backend data to match component expectations
        const transformedSuppliers = (response.data.suppliers || []).map(supplier => ({
          id: supplier.supplier_id,
          value: supplier.supplier_id,
          label: supplier.name,
          name: supplier.name,
          contactPerson: supplier.contact_person,
          phone: supplier.phone,
          email: supplier.email,
          address: supplier.address,
          paymentTerms: supplier.payment_terms,
          isActive: supplier.is_active
        }));
        setSuppliers(transformedSuppliers);
        console.log('🔍 ApiDataContext - Suppliers fetched and transformed:', {
          rawCount: response.data.suppliers?.length || 0,
          transformedCount: transformedSuppliers.length,
          sampleSupplier: transformedSuppliers[0]
        });
      }
    } catch (error) {
      handleError('suppliers', error);
    } finally {
      setLoadingState('suppliers', false);
    }
  }, [setLoadingState, handleError]);

  const fetchCategories = useCallback(async (params = {}) => {
    try {
      setLoadingState('categories', true);
      const response = await categoriesAPI.getAll(params);
      if (response.success) {
        // Transform backend data to match component expectations
        const transformedCategories = (response.data.categories || []).map(category => ({
          id: category.category_id,
          value: category.name,
          label: category.name,
          name: category.name,
          description: category.description,
          isActive: category.is_active
        }));
        setCategories(transformedCategories);
        console.log('🔍 ApiDataContext - Categories fetched and transformed:', {
          rawCount: response.data.categories?.length || 0,
          transformedCount: transformedCategories.length,
          sampleCategory: transformedCategories[0]
        });
      }
    } catch (error) {
      handleError('categories', error);
    } finally {
      setLoadingState('categories', false);
    }
  }, [setLoadingState, handleError]);

  // ============================================================================
  // CRUD OPERATIONS
  // ============================================================================

  // Product operations
  const addProduct = useCallback(async (productData) => {
    try {
      console.log('📦 ApiDataContext - Creating product with data:', productData);
      setLoadingState('products', true);

      const response = await productsAPI.create(productData);
      console.log('📦 Backend response:', response);

      if (response.success) {
        await fetchProducts(); // Refresh products list
        await fetchInventoryTransactions(); // Refresh transactions
        console.log('✅ Product created successfully:', response.data.product);
        return response.data.product;
      } else {
        throw new Error(response.message || 'Failed to create product');
      }
    } catch (error) {
      console.error('❌ ApiDataContext - Failed to create product:', error);
      handleError('products', error);
      throw error;
    } finally {
      setLoadingState('products', false);
    }
  }, [fetchProducts, fetchInventoryTransactions, setLoadingState, handleError]);

  const updateProduct = useCallback(async (id, productData) => {
    try {
      setLoadingState('products', true);
      const response = await productsAPI.update(id, productData);
      if (response.success) {
        await fetchProducts(); // Refresh products list
        return response.data.product;
      }
    } catch (error) {
      handleError('products', error);
      throw error;
    } finally {
      setLoadingState('products', false);
    }
  }, [fetchProducts, setLoadingState, handleError]);

  // Customer operations
  const addCustomer = useCallback(async (customerData) => {
    try {
      setLoadingState('customers', true);
      const response = await customersAPI.create(customerData);
      if (response.success) {
        await fetchCustomers(); // Refresh customers list
        return response.data.customer;
      }
    } catch (error) {
      handleError('customers', error);
      throw error;
    } finally {
      setLoadingState('customers', false);
    }
  }, [fetchCustomers, setLoadingState, handleError]);

  const updateCustomer = useCallback(async (id, customerData) => {
    try {
      setLoadingState('customers', true);
      const response = await customersAPI.update(id, customerData);
      if (response.success) {
        await fetchCustomers(); // Refresh customers list
        return response.data.customer;
      }
    } catch (error) {
      handleError('customers', error);
      throw error;
    } finally {
      setLoadingState('customers', false);
    }
  }, [fetchCustomers, setLoadingState, handleError]);

  // Order operations
  const addOrder = useCallback(async (orderData) => {
    try {
      setLoadingState('orders', true);
      const response = await ordersAPI.create(orderData);
      if (response.success) {
        await fetchOrders(); // Refresh orders list
        await fetchProducts(); // Refresh products (stock may have changed)
        await fetchInventoryTransactions(); // Refresh transactions
        return response.data.order;
      }
    } catch (error) {
      handleError('orders', error);
      throw error;
    } finally {
      setLoadingState('orders', false);
    }
  }, [fetchOrders, fetchProducts, fetchInventoryTransactions, setLoadingState, handleError]);

  const updateOrderStatus = useCallback(async (id, status) => {
    try {
      console.log('📋 ApiDataContext - Updating order status:', { id, status });
      setLoadingState('orders', true);

      const response = await ordersAPI.updateStatus(id, status);
      console.log('📋 Backend response:', response);

      if (response.success) {
        await fetchOrders(); // Refresh orders list
        await fetchProducts(); // Refresh products (stock may have changed)
        await fetchInventoryTransactions(); // Refresh transactions

        // ENHANCEMENT: Log payment reset for cancelled orders
        if (status === 'cancelled') {
          console.log('🚫 Order cancelled - payment information has been reset by backend');
        }

        console.log('✅ Order status updated successfully:', response.data.order);
        return response.data.order;
      } else {
        throw new Error(response.message || 'Failed to update order status');
      }
    } catch (error) {
      console.error('❌ ApiDataContext - Failed to update order status:', error);
      handleError('orders', error);
      throw error;
    } finally {
      setLoadingState('orders', false);
    }
  }, [fetchOrders, fetchProducts, fetchInventoryTransactions, setLoadingState, handleError]);

  // Inventory operations
  const updateProductStock = useCallback(async (productId, newStock, transactionType = 'adjustment', additionalData = {}) => {
    try {
      setLoadingState('products', true);
      const response = await inventoryAPI.updateStock(productId, {
        new_stock: newStock,
        transaction_type: transactionType,
        ...additionalData
      });
      if (response.success) {
        await fetchProducts(); // Refresh products list
        await fetchInventoryTransactions(); // Refresh transactions
        return response.data;
      }
    } catch (error) {
      handleError('products', error);
      throw error;
    } finally {
      setLoadingState('products', false);
    }
  }, [fetchProducts, fetchInventoryTransactions, setLoadingState, handleError]);

  // Supplier operations
  const addSupplier = useCallback(async (supplierName) => {
    try {
      console.log('🏭 ApiDataContext - Creating supplier:', supplierName);
      setLoadingState('suppliers', true);

      const supplierData = {
        name: supplierName.trim(),
        is_active: true
      };

      console.log('🏭 Sending supplier data to API:', supplierData);
      const response = await suppliersAPI.create(supplierData);
      console.log('🏭 API response:', response);

      if (response.success) {
        await fetchSuppliers(); // Refresh suppliers list
        console.log('✅ Supplier created successfully:', response.data);
        return {
          id: response.data.supplier_id,
          name: response.data.name
        };
      } else {
        throw new Error(response.message || 'Failed to create supplier');
      }
    } catch (error) {
      console.error('❌ ApiDataContext - Failed to create supplier:', error);
      handleError('suppliers', error);
      throw error;
    } finally {
      setLoadingState('suppliers', false);
    }
  }, [fetchSuppliers, setLoadingState, handleError]);

  // Category operations
  const addCategory = useCallback(async (categoryName) => {
    try {
      console.log('📂 ApiDataContext - Creating category:', categoryName);
      setLoadingState('categories', true);

      const categoryData = {
        name: categoryName.trim()
      };

      console.log('📂 Sending category data to API:', categoryData);
      const response = await categoriesAPI.create(categoryData);
      console.log('📂 API response:', response);

      if (response.success) {
        await fetchCategories(); // Refresh categories list
        console.log('✅ Category created successfully:', response.data);
        return {
          id: response.data.category_id,
          name: response.data.name
        };
      } else {
        throw new Error(response.message || 'Failed to create category');
      }
    } catch (error) {
      console.error('❌ ApiDataContext - Failed to create category:', error);
      handleError('categories', error);
      throw error;
    } finally {
      setLoadingState('categories', false);
    }
  }, [fetchCategories, setLoadingState, handleError]);

  // ============================================================================
  // COMPUTED VALUES (MEMOIZED)
  // ============================================================================

  // ENHANCEMENT: Company Losses Calculation (moved before totalRevenue)
  const companyLosses = useMemo(() => {
    return inventoryTransactions
      .filter(transaction =>
        transaction.type === 'loss' ||
        (transaction.type === 'out' && transaction.notes?.includes('Company Loss'))
      )
      .reduce((sum, transaction) => sum + (transaction.totalCost || 0), 0);
  }, [inventoryTransactions]);

  // Calculate original sales revenue (needed for both adjusted revenue and profit)
  const salesRevenue = useMemo(() => {
    // FIXED: Exclude cancelled orders from revenue calculations
    return orders
      .filter(order => order.status !== 'cancelled')
      .reduce((sum, order) => sum + (order.total_amount || 0), 0);
  }, [orders]);

  const totalRevenue = useMemo(() => {
    // ENHANCEMENT: Adjust revenue calculation to account for company losses
    // Formula: Adjusted Revenue = Total Sales Revenue - Company Losses
    return salesRevenue - companyLosses;
  }, [salesRevenue, companyLosses]);

  const totalExpenses = useMemo(() => {
    return expenses.reduce((sum, expense) => sum + (expense.amount || 0), 0);
  }, [expenses]);

  const profit = useMemo(() => {
    // FIXED: Profit calculation should use original sales revenue, not adjusted revenue
    // Company losses are asset write-downs, not operational expenses
    // Formula: Profit = Total Sales Revenue - Total Expenses
    return salesRevenue - totalExpenses;
  }, [salesRevenue, totalExpenses]);

  const lowStockProducts = useMemo(() => {
    return products.filter(product =>
      product.stock <= (product.minStock || 10)
    );
  }, [products]);

  const inventoryValue = useMemo(() => {
    return products.reduce((sum, product) =>
      sum + ((product.price || 0) * (product.stock || 0)), 0
    );
  }, [products]);

  const pendingPayments = useMemo(() => {
    // FIXED: Exclude cancelled orders from pending payments calculation
    return orders.reduce((sum, order) => {
      if (order.payment_status !== 'paid' && order.status !== 'cancelled') {
        return sum + ((order.total_amount || 0) - (order.paid_amount || 0));
      }
      return sum;
    }, 0);
  }, [orders]);

  const recentOrders = useMemo(() => {
    return orders
      .sort((a, b) => new Date(b.order_date) - new Date(a.order_date))
      .slice(0, 5);
  }, [orders]);

  // ============================================================================
  // INITIAL DATA LOADING
  // ============================================================================

  useEffect(() => {
    // Load initial data when component mounts (FIXED - No dependencies to prevent infinite loop)
    fetchProducts();
    fetchCustomers();
    fetchOrders();
    fetchInventoryTransactions();
    fetchSuppliers();
    fetchCategories();
  }, []); // Empty dependency array - run only once on mount

  // ============================================================================
  // CONTEXT VALUE
  // ============================================================================

  const value = {
    // Data
    products,
    customers,
    orders,
    payments,
    expenses,
    inventoryTransactions,
    suppliers,
    categories,

    // Loading states
    loading,

    // Error states
    errors,

    // Fetch functions
    fetchProducts,
    fetchCustomers,
    fetchOrders,
    fetchInventoryTransactions,
    fetchSuppliers,
    fetchCategories,

    // CRUD operations
    addProduct,
    updateProduct,
    addCustomer,
    updateCustomer,
    addOrder,
    updateOrderStatus,
    updateProductStock,
    addSupplier,
    addCategory,

    // Computed values
    getTotalRevenue: () => totalRevenue,
    getTotalExpenses: () => totalExpenses,
    getProfit: () => profit,
    getTotalProfit: () => profit,
    getPendingPayments: () => pendingPayments,
    getInventoryValue: () => inventoryValue,
    getRecentOrders: (limit = 5) => recentOrders.slice(0, limit),
    getLowStockProducts: () => lowStockProducts,
    // ENHANCEMENT: Company Losses
    getCompanyLosses: () => companyLosses,

    // Legacy compatibility functions for inventory pages
    addInventoryTransaction: async (transactionData) => {
      console.log('Legacy addInventoryTransaction called:', transactionData);
      // This is handled automatically by updateProductStock in ApiDataContext
      return { success: true };
    },

    // Dynamic supplier and category options from API data
    supplierOptions: [
      { value: '', label: 'Select supplier' },
      ...suppliers.map(supplier => ({
        value: supplier.id,
        label: supplier.name
      }))
    ],
    categoryOptions: [
      { value: '', label: 'Select category' },
      ...categories.map(category => ({
        value: category.value,
        label: category.label
      }))
    ],
    recentDeliveries: inventoryTransactions
      .filter(t => t.type === 'in')
      .sort((a, b) => new Date(b.transaction_date) - new Date(a.transaction_date))
      .slice(0, 10)
      .map(t => ({
        date: t.transaction_date?.split('T')[0] || t.date,
        supplier: t.supplier?.name || 'Unknown Supplier',
        product: t.product?.name || t.product_name,
        quantity: t.quantity,
        totalCost: t.total_cost,
        status: t.status === 'completed' ? 'Received' : 'Pending'
      }))
  };

  return <ApiDataContext.Provider value={value}>{children}</ApiDataContext.Provider>;
};

export const useApiData = () => {
  const context = useContext(ApiDataContext);
  if (context === undefined) {
    throw new Error('useApiData must be used within an ApiDataProvider');
  }
  return context;
};
