import { useState, useEffect } from 'react';
import AuthDebug from './AuthDebug';

function SimpleMonitor() {
  console.log('🚀 SimpleMonitor component loaded');

  const [data, setData] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    console.log('🔄 SimpleMonitor useEffect triggered');

    const fetchData = async () => {
      try {
        console.log('📡 Fetching data...');
        const response = await fetch('http://localhost:3001/api/monitor/database');
        console.log('📥 Response received:', response.status);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.log('✅ Data parsed:', result);
        setData(result);
      } catch (err) {
        console.error('❌ Error:', err);
        setError(err.message);
      }
    };

    fetchData();
  }, []);

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Simple Database Monitor</h1>

      <AuthDebug />

      <div className="bg-blue-100 p-4 rounded mb-4 mt-4">
        <p><strong>Component Status:</strong> Loaded Successfully</p>
        <p><strong>Current Time:</strong> {new Date().toLocaleString()}</p>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <strong>Error:</strong> {error}
        </div>
      )}

      {data ? (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
          <h2 className="font-bold mb-2">Database Stats:</h2>
          <pre className="text-sm">{JSON.stringify(data, null, 2)}</pre>
        </div>
      ) : (
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">
          Loading database statistics...
        </div>
      )}
    </div>
  );
}

export default SimpleMonitor;
