const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Salesman = sequelize.define('Salesman', {
  salesman_id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'user_id'
    }
  },
  full_name: {
    type: DataTypes.STRING,
    allowNull: false
  },
  contact_info: {
    type: DataTypes.JSONB,
    defaultValue: {}
  },
  commission_rate: {
    type: DataTypes.DECIMAL(5, 4),
    defaultValue: 0.05 // 5% default commission
  },
  monthly_target: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0
  },
  current_month_sales: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0
  },
  total_sales: {
    type: DataTypes.DECIMAL(15, 2),
    defaultValue: 0
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  }
}, {
  tableName: 'salesmen'
});

module.exports = Salesman;
