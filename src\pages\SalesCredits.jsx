import { useState, Fragment } from 'react';
import { DollarSignIcon, FilterIcon, ChevronLeftIcon, ChevronRightIcon, EyeIcon, CheckIcon, ClockIcon, CalendarIcon } from 'lucide-react';
import { useData } from '../contexts/DataContext';
import RefreshButton from '../components/RefreshButton';
import useAutoRefresh from '../hooks/useAutoRefresh';
import AutoRefreshControl from '../components/AutoRefreshControl';
import useDataRefresh from '../hooks/useDataRefresh';

function SalesCredits() {
  const {
    orders,
    customers,
    addPayment,
    updateOrderPaymentStatus,
    refreshOrders,
    refreshCustomers,
    refreshAllData
  } = useData();
  const [filters, setFilters] = useState({ dateFrom: '', dateTo: '', customer: '', status: '' });
  const [showRecordPayment, setShowRecordPayment] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [partialPaymentAmount, setPartialPaymentAmount] = useState('');
  const [paymentDate, setPaymentDate] = useState(new Date().toISOString().split('T')[0]);
  const [paymentMethod, setPaymentMethod] = useState('cash');
  const [currentInvoiceId, setCurrentInvoiceId] = useState(null);

  // ENHANCEMENT: Comprehensive refresh function for Sales Credits
  const handleRefreshData = async () => {
    try {
      console.log('🔄 Refreshing Sales Credits data...');
      // Refresh all relevant data for sales credits page
      await Promise.all([
        refreshOrders(),
        refreshCustomers()
      ]);
      console.log('✅ Sales Credits data refreshed successfully');
    } catch (error) {
      console.error('❌ Failed to refresh Sales Credits data:', error);
      throw error; // Re-throw to let RefreshButton handle the error
    }
  };

  // ENHANCEMENT: Auto-refresh functionality
  const {
    isAutoRefreshEnabled,
    isPaused,
    lastRefresh,
    toggleAutoRefresh,
    manualRefresh
  } = useAutoRefresh(handleRefreshData, {
    interval: 3000, // 3 seconds
    enabled: true,
    pauseOnInteraction: true,
    interactionTimeout: 5000 // 5 seconds
  });

  // ENHANCEMENT: Auto-refresh after CRUD operations
  const { refreshAfterPaymentCreate, refreshAfterPaymentUpdate } = useDataRefresh();

  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({ ...prev, [name]: value }));
  };

  const handleMarkAsPaid = async (id) => {
    try {
      const order = orders.find(o => o.id === id);
      if (!order) return;

      // ENHANCEMENT: Validate order status before payment collection
      if (order.status !== 'delivered') {
        alert(`Cannot collect payment for order with status '${order.status}'. Only delivered orders can receive payments.`);
        return;
      }

      // ENHANCEMENT: Prevent payment collection for cancelled orders
      if (order.status === 'cancelled') {
        alert('Cannot collect payment for cancelled orders.');
        return;
      }

      console.log('💳 Marking order as fully paid:', id);

      // Update payment status to 'paid' and set paid amount to total amount
      await updateOrderPaymentStatus(id, 'paid', order.totalAmount);

      // Also add payment record for tracking
      addPayment({
        orderId: id,
        orderNumber: order.orderNumber,
        amount: order.totalAmount - order.paidAmount,
        date: new Date().toISOString().split('T')[0],
        method: 'Full Payment'
      });

      // ENHANCEMENT: Trigger automatic data refresh after payment update
      await refreshAfterPaymentUpdate();

      console.log('✅ Order marked as fully paid');
    } catch (error) {
      console.error('❌ Failed to mark order as paid:', error);
      alert(`Failed to update payment status: ${error.message}`);
    }
  };

  const handleRecordPartialPayment = (id) => {
    // ENHANCEMENT: Validate order status before allowing partial payment
    const order = orders.find(o => o.id === id);
    if (!order) return;

    // Validate order status
    if (order.status !== 'delivered') {
      alert(`Cannot collect payment for order with status '${order.status}'. Only delivered orders can receive payments.`);
      return;
    }

    // Prevent payment collection for cancelled orders
    if (order.status === 'cancelled') {
      alert('Cannot collect payment for cancelled orders.');
      return;
    }

    setCurrentInvoiceId(id);
    setShowRecordPayment(true);
    setPartialPaymentAmount('');
    setPaymentDate(new Date().toISOString().split('T')[0]);
  };

  const handleSubmitPayment = async () => {
    try {
      if (!currentInvoiceId || !partialPaymentAmount) return;

      const order = orders.find(o => o.id === currentInvoiceId);
      if (!order) return;

      const paymentAmount = parseFloat(partialPaymentAmount);
      const newPaidAmount = order.paidAmount + paymentAmount;

      console.log('💳 Recording partial payment:', {
        orderId: currentInvoiceId,
        paymentAmount,
        newPaidAmount,
        totalAmount: order.totalAmount
      });

      // Determine new payment status
      let newPaymentStatus = 'partial';
      if (newPaidAmount >= order.totalAmount) {
        newPaymentStatus = 'paid';
      }

      // Update payment status in database
      await updateOrderPaymentStatus(currentInvoiceId, newPaymentStatus, newPaidAmount);

      // Also add payment record for tracking
      addPayment({
        orderId: currentInvoiceId,
        orderNumber: order.orderNumber,
        amount: paymentAmount,
        date: paymentDate,
        method: paymentMethod
      });

      // ENHANCEMENT: Trigger automatic data refresh after payment creation
      await refreshAfterPaymentCreate();

      setShowRecordPayment(false);
      setCurrentInvoiceId(null);

      console.log('✅ Partial payment recorded successfully');
    } catch (error) {
      console.error('❌ Failed to record payment:', error);
      alert(`Failed to record payment: ${error.message}`);
    }
  };

  const handleView = (id) => {
    setSelectedInvoice(id === selectedInvoice ? null : id);
  };

  const pendingPaymentsData = orders.map(order => {
    const customer = customers.find(c => c.id === order.customerId);

    // FIXED: Use actual database payment status and handle cancelled orders
    // For cancelled orders, show 'cancelled' as payment status and $0.00 remaining
    const isCancelledOrder = order.status === 'cancelled';
    const actualPaymentStatus = isCancelledOrder ? 'cancelled' : order.paymentStatus;
    const actualPaidAmount = order.paidAmount || 0;
    const actualRemainingAmount = isCancelledOrder ? 0 : (order.totalAmount - actualPaidAmount);



    return {
      id: order.id,
      customer: customer ? customer.name : 'Unknown Customer',
      orderNumber: order.orderNumber,
      totalAmount: order.totalAmount,
      paidAmount: actualPaidAmount,
      remainingAmount: actualRemainingAmount,
      orderDate: order.date,
      dueDate: order.dueDate,
      status: actualPaymentStatus, // Use actual payment status from database
      lastPaymentDate: actualPaymentStatus !== 'pending' ? order.date : null,
      contact: customer ? customer.contact : 'N/A',
      phone: customer ? customer.phone : 'N/A',
      paymentHistory: actualPaymentStatus === 'paid' ? [{ date: order.date, amount: order.totalAmount, method: 'Full Payment' }] : actualPaymentStatus === 'partial' ? [{ date: order.date, amount: actualPaidAmount, method: 'Partial Payment' }] : []
    };
  });

  const filteredPayments = pendingPaymentsData.filter(payment => {
    if (filters.dateFrom && new Date(payment.orderDate) < new Date(filters.dateFrom)) return false;
    if (filters.dateTo && new Date(payment.orderDate) > new Date(filters.dateTo)) return false;
    if (filters.customer && !payment.customer.toLowerCase().includes(filters.customer.toLowerCase())) return false;
    if (filters.status && payment.status !== filters.status) return false;
    return true;
  });

  const customerOptions = [
    { value: '', label: 'All Customers' },
    ...Array.from(new Set(pendingPaymentsData.map(p => p.customer))).map(customer => ({ value: customer.toLowerCase(), label: customer }))
  ];

  const getStatusBadgeClass = (status) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'partial': return 'bg-blue-100 text-blue-800';
      case 'paid': return 'bg-green-100 text-green-800';
      case 'overdue': return 'bg-red-100 text-red-800';
      case 'cancelled': return 'bg-red-100 text-red-800'; // FIXED: Add cancelled status styling
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const totalPending = filteredPayments.reduce((sum, invoice) => sum + invoice.remainingAmount, 0);
  const totalPartiallyPaid = filteredPayments.filter(i => i.status === 'partial').length;
  const totalOverdue = filteredPayments.filter(i => new Date(i.dueDate) < new Date() && i.status !== 'paid').length;

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Pending Payments</h1>
          <p className="text-gray-600">Track customer payments and manage outstanding balances</p>
        </div>
        <div className="flex items-center space-x-4">
          <AutoRefreshControl
            isAutoRefreshEnabled={isAutoRefreshEnabled}
            isPaused={isPaused}
            lastRefresh={lastRefresh}
            toggleAutoRefresh={toggleAutoRefresh}
            interval={3000}
            size="sm"
          />
          <RefreshButton
            onRefresh={manualRefresh}
            variant="outline"
            size="md"
            label="Refresh Now"
          />
        </div>
      </div>
      {/* Payment Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-sm font-medium text-gray-500">TOTAL OUTSTANDING</h3>
            <DollarSignIcon size={20} className="text-yellow-600" />
          </div>
          <p className="text-2xl font-bold text-gray-900">${totalPending.toFixed(2)}</p>
          <div className="mt-2 flex items-center text-sm">
            <span className="text-gray-600">{filteredPayments.filter(i => i.status !== 'paid').length} invoices pending</span>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-sm font-medium text-gray-500">PARTIALLY PAID</h3>
            <ClockIcon size={20} className="text-blue-600" />
          </div>
          <p className="text-2xl font-bold text-gray-900">{totalPartiallyPaid}</p>
          <div className="mt-2 flex items-center text-sm">
            <span className="text-blue-600">Invoices with partial payments</span>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-sm font-medium text-gray-500">OVERDUE</h3>
            <CalendarIcon size={20} className="text-red-600" />
          </div>
          <p className="text-2xl font-bold text-gray-900">{totalOverdue}</p>
          <div className="mt-2 flex items-center text-sm">
            <span className="text-red-600">Past due date</span>
          </div>
        </div>
      </div>
      {/* Record Payment Modal (conditionally rendered) */}
      {showRecordPayment && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">Record Payment</h3>
              <button onClick={() => setShowRecordPayment(false)} className="text-gray-400 hover:text-gray-500">
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="mb-4">
              <p className="text-gray-700">Recording payment for invoice #{currentInvoiceId}</p>
              <p className="text-gray-700 mb-4">{pendingPaymentsData.find(i => i.id === currentInvoiceId)?.customer} - {pendingPaymentsData.find(i => i.id === currentInvoiceId)?.orderNumber}</p>
              <div className="mb-4">
                <label htmlFor="paymentAmount" className="block text-sm font-medium text-gray-700 mb-1">Payment Amount ($)</label>
                <input type="number" id="paymentAmount" name="paymentAmount" value={partialPaymentAmount} onChange={e => setPartialPaymentAmount(e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Enter amount" min="0" step="0.01" required />
              </div>
              <div className="mb-4">
                <label htmlFor="paymentDate" className="block text-sm font-medium text-gray-700 mb-1">Payment Date</label>
                <input type="date" id="paymentDate" name="paymentDate" value={paymentDate} onChange={e => setPaymentDate(e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required />
              </div>
              <div className="mb-4">
                <label htmlFor="paymentMethod" className="block text-sm font-medium text-gray-700 mb-1">Payment Method</label>
                <select id="paymentMethod" name="paymentMethod" value={paymentMethod} onChange={e => setPaymentMethod(e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                  <option value="cash">Cash</option>
                  <option value="bank-transfer">Bank Transfer</option>
                  <option value="check">Check</option>
                  <option value="other">Other</option>
                </select>
              </div>
            </div>
            <div className="flex justify-end">
              <button type="button" className="px-4 py-2 mr-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50" onClick={() => setShowRecordPayment(false)}>Cancel</button>
              <button type="button" className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700" onClick={handleSubmitPayment}>Record Payment</button>
            </div>
          </div>
        </div>
      )}
      {/* Filters */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="flex items-center mb-4">
          <FilterIcon size={20} className="text-blue-600 mr-2" />
          <h2 className="text-lg font-medium text-gray-800">Filters</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <label htmlFor="dateFrom" className="block text-sm font-medium text-gray-700 mb-1">Date From</label>
            <input type="date" id="dateFrom" name="dateFrom" value={filters.dateFrom} onChange={handleFilterChange} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" />
          </div>
          <div>
            <label htmlFor="dateTo" className="block text-sm font-medium text-gray-700 mb-1">Date To</label>
            <input type="date" id="dateTo" name="dateTo" value={filters.dateTo} onChange={handleFilterChange} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" />
          </div>
          <div>
            <label htmlFor="customer" className="block text-sm font-medium text-gray-700 mb-1">Customer</label>
            <select id="customer" name="customer" value={filters.customer} onChange={handleFilterChange} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              {customerOptions.map(option => (
                <option key={option.value} value={option.value}>{option.label}</option>
              ))}
            </select>
          </div>
          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <select id="status" name="status" value={filters.status} onChange={handleFilterChange} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="">All Statuses</option>
              <option value="pending">Pending</option>
              <option value="partial">Partially Paid</option>
              <option value="paid">Fully Paid</option>
              <option value="overdue">Overdue</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
        </div>
        <div className="flex justify-end mt-4">
          <button className="px-4 py-2 mr-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50" onClick={() => setFilters({ dateFrom: '', dateTo: '', customer: '', status: '' })}>Clear Filters</button>
          <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Apply Filters</button>
        </div>
      </div>
      {/* Pending Payments Table */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order #</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Amount</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Paid</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remaining</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredPayments.map(payment => {
                // ENHANCEMENT: Get order details for status validation
                const order = orders.find(o => o.id === payment.id);
                const orderStatus = order?.status || 'unknown';
                const canCollectPayment = orderStatus === 'delivered';

                // ENHANCEMENT: Get order status badge styling
                const getOrderStatusBadgeClass = (status) => {
                  switch (status) {
                    case 'pending': return 'bg-yellow-100 text-yellow-800';
                    case 'processing': return 'bg-blue-100 text-blue-800';
                    case 'delivered': return 'bg-green-100 text-green-800';
                    case 'cancelled': return 'bg-red-100 text-red-800';
                    default: return 'bg-gray-100 text-gray-800';
                  }
                };

                return (
                <Fragment key={payment.id}>
                  <tr>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">{payment.orderNumber}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{payment.customer}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getOrderStatusBadgeClass(orderStatus)}`}>
                        {orderStatus.charAt(0).toUpperCase() + orderStatus.slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${payment.totalAmount.toFixed(2)}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600 font-medium">${payment.paidAmount.toFixed(2)}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600 font-medium">${payment.remainingAmount.toFixed(2)}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{payment.dueDate}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(payment.status)}`}>
                        {payment.status === 'partial' ? 'Partially Paid' :
                         payment.status === 'cancelled' ? 'Cancelled' :
                         payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button className="text-blue-600 hover:text-blue-900" onClick={() => handleView(payment.id)}><EyeIcon size={18} /></button>
                        {payment.status !== 'paid' && payment.status !== 'cancelled' && canCollectPayment && (
                          <>
                            <button className="text-green-600 hover:text-green-900" onClick={() => handleMarkAsPaid(payment.id)} title="Mark as fully paid"><CheckIcon size={18} /></button>
                            <button className="text-blue-600 hover:text-blue-900" onClick={() => handleRecordPartialPayment(payment.id)} title="Record partial payment"><DollarSignIcon size={18} /></button>
                          </>
                        )}
                        {(payment.status !== 'paid' && payment.status !== 'cancelled' && !canCollectPayment) && (
                          <span className="text-gray-400 text-xs" title={`Cannot collect payment for ${orderStatus} orders`}>
                            Payment disabled
                          </span>
                        )}
                        {payment.status === 'cancelled' && (
                          <span className="text-red-400 text-xs" title="Order payment has been cancelled">
                            Payment cancelled
                          </span>
                        )}
                      </div>
                    </td>
                  </tr>
                  {selectedInvoice === payment.id && (
                    <tr>
                      <td colSpan={9} className="px-6 py-4 bg-gray-50">
                        <div className="border rounded-md overflow-hidden">
                          <div className="bg-gray-100 px-4 py-2 font-medium">Payment Details</div>
                          <div className="p-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                              <div>
                                <p className="text-sm text-gray-500 mb-1">Customer Information</p>
                                <p className="text-sm mb-1"><span className="font-medium">Contact Person:</span> {payment.contact}</p>
                                <p className="text-sm"><span className="font-medium">Phone:</span> {payment.phone}</p>
                              </div>
                              <div>
                                <p className="text-sm text-gray-500 mb-1">Order Information</p>
                                <p className="text-sm mb-1"><span className="font-medium">Order Date:</span> {payment.orderDate}</p>
                                <p className="text-sm mb-1"><span className="font-medium">Due Date:</span> {payment.dueDate}</p>
                                <p className="text-sm"><span className="font-medium">Salesman:</span> {(() => {
                                  const order = orders.find(o => o.id === payment.id);
                                  return order?.salesman ? order.salesman.name : 'No salesman assigned';
                                })()}</p>
                              </div>
                            </div>
                            <div className="mb-4">
                              <p className="text-sm text-gray-500 mb-1">Payment Summary</p>
                              <div className="bg-gray-50 p-3 rounded-md border border-gray-200">
                                <div className="flex justify-between mb-1"><span className="text-sm">Total Amount:</span><span className="text-sm font-medium">${payment.totalAmount.toFixed(2)}</span></div>
                                <div className="flex justify-between mb-1"><span className="text-sm">Amount Paid:</span><span className="text-sm font-medium text-green-600">${payment.paidAmount.toFixed(2)}</span></div>
                                <div className="flex justify-between"><span className="text-sm">Remaining Balance:</span><span className="text-sm font-medium text-red-600">${payment.remainingAmount.toFixed(2)}</span></div>
                              </div>
                            </div>
                            {payment.paymentHistory.length > 0 && (
                              <div>
                                <p className="text-sm text-gray-500 mb-1">Payment History</p>
                                <table className="min-w-full divide-y divide-gray-200 border rounded-md">
                                  <thead className="bg-gray-50">
                                    <tr>
                                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Method</th>
                                    </tr>
                                  </thead>
                                  <tbody className="bg-white divide-y divide-gray-200">
                                    {payment.paymentHistory.map((record, index) => (
                                      <tr key={index}>
                                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{record.date}</td>
                                        <td className="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">${record.amount.toFixed(2)}</td>
                                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{record.method}</td>
                                      </tr>
                                    ))}
                                  </tbody>
                                </table>
                              </div>
                            )}
                            {payment.status !== 'paid' && payment.status !== 'cancelled' && (
                              <div className="flex justify-end mt-4">
                                <button className="px-3 py-1 mr-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50" onClick={() => setSelectedInvoice(null)}>Close</button>
                                <button className="px-3 py-1 mr-2 bg-blue-600 text-white rounded-md hover:bg-blue-700" onClick={() => handleRecordPartialPayment(payment.id)}>Record Payment</button>
                                <button className="px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700" onClick={() => handleMarkAsPaid(payment.id)}>Mark as Paid</button>
                              </div>
                            )}
                            {payment.status === 'cancelled' && (
                              <div className="flex justify-end mt-4">
                                <button className="px-3 py-1 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50" onClick={() => setSelectedInvoice(null)}>Close</button>
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                    </tr>
                  )}
                </Fragment>
                );
              })}
            </tbody>
          </table>
        </div>
        {/* Pagination */}
        <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">Showing <span className="font-medium">1</span> to <span className="font-medium">{filteredPayments.length}</span> of <span className="font-medium">{pendingPaymentsData.length}</span> payments</p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50" disabled={currentPage === 1} onClick={() => setCurrentPage(currentPage - 1)}>
                  <span className="sr-only">Previous</span>
                  <ChevronLeftIcon size={16} />
                </button>
                <button className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-blue-600 hover:bg-gray-50">1</button>
                <button className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50" onClick={() => setCurrentPage(currentPage + 1)}>
                  <span className="sr-only">Next</span>
                  <ChevronRightIcon size={16} />
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default SalesCredits;
