# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=inventory_management
DB_USER=your_username
DB_PASSWORD=your_password
DB_URL=postgresql://your_username:your_password@localhost:5432/inventory_management

# Server Configuration
PORT=3001
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_random
JWT_EXPIRES_IN=7d

# CORS Configuration
FRONTEND_URL=http://localhost:5177

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
