import { useState } from 'react';
import { BarChart2Icon, FileTextIcon, DownloadIcon, CalendarIcon, FilterIcon, PieChartIcon, TrendingUpIcon, PackageIcon, DollarSignIcon, UsersIcon, AlertCircleIcon } from 'lucide-react';
import { useData } from '../contexts/DataContext';

function Reports() {
  const { salesData, inventoryData, expensesData, salesmenPerformance } = useData();
  const [reportType, setReportType] = useState('sales');
  const [dateRange, setDateRange] = useState({
    from: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
    to: new Date().toISOString().split('T')[0]
  });
  const [format, setFormat] = useState('pdf');
  const [isGenerating, setIsGenerating] = useState(false);

  const handleGenerateReport = () => {
    setIsGenerating(true);
    setTimeout(() => {
      setIsGenerating(false);
    }, 2000);
  };

  // Use fallback data if context data is not available
  const currentSalesData = salesData || [
    { month: 'Jan', revenue: 45000 },
    { month: 'Feb', revenue: 52000 },
    { month: 'Mar', revenue: 48000 },
    { month: 'Apr', revenue: 61000 },
    { month: 'May', revenue: 55000 },
    { month: 'Jun', revenue: 67000 }
  ];
  const currentInventoryData = inventoryData || [
    { category: 'Beverages', stock: 12500 },
    { category: 'Snacks', stock: 8700 },
    { category: 'Dairy', stock: 4200 },
    { category: 'Cleaning', stock: 3100 },
    { category: 'Personal Care', stock: 2800 }
  ];
  const currentExpensesData = expensesData || [
    { category: 'Rent', amount: 3500 },
    { category: 'Salaries', amount: 12000 },
    { category: 'Transportation', amount: 2800 },
    { category: 'Utilities', amount: 1700 },
    { category: 'Maintenance', amount: 950 },
    { category: 'Office Supplies', amount: 650 },
    { category: 'Marketing', amount: 1200 },
    { category: 'Miscellaneous', amount: 850 }
  ];
  const currentSalesmenPerformance = salesmenPerformance || [
    { name: 'John Doe', sales: 67000, commission: 3350 },
    { name: 'Jane Smith', sales: 85000, commission: 4250 },
    { name: 'Ahmed Hassan', sales: 42500, commission: 2125 },
    { name: 'Ali Khan', sales: 72500, commission: 3625 },
    { name: 'Sarah Johnson', sales: 37000, commission: 1850 }
  ];

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  const totalRevenue = currentSalesData.reduce((sum, item) => sum + item.revenue, 0);
  const totalExpenses = currentExpensesData.reduce((sum, item) => sum + item.amount, 0);
  const profit = totalRevenue - totalExpenses;
  const profitMargin = profit / totalRevenue * 100;

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Reports</h1>
          <p className="text-gray-600">Generate and download business reports</p>
        </div>
        <div>
          <button className="flex items-center bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700" onClick={handleGenerateReport} disabled={isGenerating}>
            {isGenerating ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Generating...
              </>
            ) : (
              <>
                <FileTextIcon size={16} className="mr-2" />
                Generate Report
              </>
            )}
          </button>
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div className="bg-white shadow-md rounded-md p-4">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">Sales Overview</h2>
          <div className="flex flex-col md:flex-row justify-between items-center mb-4">
            <div className="flex items-center mb-2 md:mb-0">
              <label className="text-gray-700 mr-2">Report Type:</label>
              <select value={reportType} onChange={(e) => setReportType(e.target.value)} className="border rounded-md p-2">
                <option value="sales">Sales</option>
                <option value="inventory">Inventory</option>
                <option value="expenses">Expenses</option>
                <option value="salesmen">Salesmen Performance</option>
              </select>
            </div>
            <div className="flex items-center">
              <label className="text-gray-700 mr-2">Date Range:</label>
              <input type="date" value={dateRange.from} onChange={(e) => setDateRange({ ...dateRange, from: e.target.value })} className="border rounded-md p-2 mr-2" />
              <span className="text-gray-700">to</span>
              <input type="date" value={dateRange.to} onChange={(e) => setDateRange({ ...dateRange, to: e.target.value })} className="border rounded-md p-2 ml-2" />
            </div>
          </div>
          <div className="mb-4">
            <label className="text-gray-700 mr-2">Format:</label>
            <select value={format} onChange={(e) => setFormat(e.target.value)} className="border rounded-md p-2">
              <option value="pdf">PDF</option>
              <option value="excel">Excel</option>
              <option value="csv">CSV</option>
            </select>
          </div>
          <div>
            <h3 className="text-md font-semibold text-gray-800 mb-2">Total Revenue: {formatCurrency(totalRevenue)}</h3>
            <h3 className="text-md font-semibold text-gray-800 mb-2">Total Expenses: {formatCurrency(totalExpenses)}</h3>
            <h3 className="text-md font-semibold text-gray-800">Profit: {formatCurrency(profit)} ({profitMargin.toFixed(2)}%)</h3>
          </div>
        </div>
        <div className="bg-white shadow-md rounded-md p-4">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">Top Products</h2>
          <div className="grid grid-cols-2 gap-4">
            {currentInventoryData.map((item) => (
              <div key={item.category} className="bg-gray-100 rounded-md p-4">
                <h3 className="text-md font-semibold text-gray-800 mb-2">{item.category}</h3>
                <p className="text-gray-600">Stock: {item.stock}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
      <div className="bg-white shadow-md rounded-md p-4 mb-6">
        <h2 className="text-lg font-semibold text-gray-800 mb-4">Sales Data</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h3 className="text-md font-semibold text-gray-800 mb-2">Monthly Sales</h3>
            <BarChart2Icon className="text-blue-600 h-6 w-6 mb-2" />
            <div className="grid grid-cols-2 gap-2">
              {currentSalesData.map((data) => (
                <div key={data.month} className="flex justify-between bg-gray-100 rounded-md p-2">
                  <span className="text-gray-700">{data.month}</span>
                  <span className="text-gray-800 font-semibold">{formatCurrency(data.revenue)}</span>
                </div>
              ))}
            </div>
          </div>
          <div>
            <h3 className="text-md font-semibold text-gray-800 mb-2">Top Selling Products</h3>
            <PieChartIcon className="text-blue-600 h-6 w-6 mb-2" />
            <div className="grid grid-cols-2 gap-2">
              {currentSalesmenPerformance.map((salesman) => (
                <div key={salesman.name} className="flex justify-between bg-gray-100 rounded-md p-2">
                  <span className="text-gray-700">{salesman.name}</span>
                  <span className="text-gray-800 font-semibold">{formatCurrency(salesman.sales)}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
      <div className="bg-white shadow-md rounded-md p-4 mb-6">
        <h2 className="text-lg font-semibold text-gray-800 mb-4">Expenses Overview</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h3 className="text-md font-semibold text-gray-800 mb-2">Monthly Expenses</h3>
            <div className="grid grid-cols-2 gap-2">
              {currentExpensesData.map((data) => (
                <div key={data.category} className="flex justify-between bg-gray-100 rounded-md p-2">
                  <span className="text-gray-700">{data.category}</span>
                  <span className="text-gray-800 font-semibold">{formatCurrency(data.amount)}</span>
                </div>
              ))}
            </div>
          </div>
          <div>
            <h3 className="text-md font-semibold text-gray-800 mb-2">Expense Distribution</h3>
            <div className="flex">
              {currentExpensesData.map((data) => (
                <div key={data.category} className="flex-1 bg-gray-100 rounded-md p-2 mr-2 last:mr-0">
                  <div className="text-center">
                    <span className="text-gray-700">{data.category}</span>
                  </div>
                  <div className="h-2 bg-blue-600 rounded-md" style={{ width: `${(data.amount / totalExpenses) * 100}%` }}></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
      <div className="bg-white shadow-md rounded-md p-4 mb-6">
        <h2 className="text-lg font-semibold text-gray-800 mb-4">Salesmen Performance</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {currentSalesmenPerformance.map((salesman) => (
            <div key={salesman.name} className="bg-gray-100 rounded-md p-4">
              <h3 className="text-md font-semibold text-gray-800 mb-2">{salesman.name}</h3>
              <p className="text-gray-600">Sales: {formatCurrency(salesman.sales)}</p>
              <p className="text-gray-600">Commission: {formatCurrency(salesman.commission)}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default Reports;