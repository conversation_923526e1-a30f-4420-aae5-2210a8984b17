const { Order, OrderItem, Customer, Product, Salesman, User, InventoryTransaction } = require('../models');
const { Op } = require('sequelize');

// Generate order number
const generateOrderNumber = () => {
  const timestamp = Date.now().toString();
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `ORD-${timestamp.slice(-6)}${random}`;
};

// Get all orders
const getOrders = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 50,
      status,
      customer_id,
      salesman_id,
      date_from,
      date_to
    } = req.query;

    const offset = (page - 1) * limit;
    const where = {};

    // Filter by status
    if (status) {
      where.status = status;
    }

    // Filter by customer
    if (customer_id) {
      where.customer_id = customer_id;
    }

    // Filter by salesman (for salesman role or admin filtering)
    if (req.user.role === 'salesman') {
      const salesman = await Salesman.findOne({ where: { user_id: req.user.user_id } });
      if (salesman) {
        where.salesman_id = salesman.salesman_id;
      }
    } else if (salesman_id) {
      where.salesman_id = salesman_id;
    }

    // Date range filter
    if (date_from || date_to) {
      where.order_date = {};
      if (date_from) where.order_date[Op.gte] = new Date(date_from);
      if (date_to) where.order_date[Op.lte] = new Date(date_to);
    }

    const { count, rows: orders } = await Order.findAndCountAll({
      where,
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['customer_id', 'name', 'type']
        },
        {
          model: Salesman,
          as: 'salesman',
          attributes: ['salesman_id', 'full_name']
        },
        {
          model: OrderItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product',
              attributes: ['product_id', 'name', 'unit_price']
            }
          ]
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['order_date', 'DESC']]
    });

    res.json({
      success: true,
      data: {
        orders,
        pagination: {
          current_page: parseInt(page),
          total_pages: Math.ceil(count / limit),
          total_items: count,
          items_per_page: parseInt(limit)
        }
      }
    });
  } catch (error) {
    next(error);
  }
};

// Get single order
const getOrder = async (req, res, next) => {
  try {
    const { id } = req.params;

    const order = await Order.findByPk(id, {
      include: [
        {
          model: Customer,
          as: 'customer'
        },
        {
          model: Salesman,
          as: 'salesman',
          include: [
            {
              model: User,
              as: 'user',
              attributes: ['email']
            }
          ]
        },
        {
          model: OrderItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product'
            }
          ]
        }
      ]
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Check if user has permission to view this order
    if (req.user.role === 'salesman') {
      const salesman = await Salesman.findOne({ where: { user_id: req.user.user_id } });
      if (salesman && order.salesman_id !== salesman.salesman_id) {
        return res.status(403).json({
          success: false,
          message: 'Access denied'
        });
      }
    }

    res.json({
      success: true,
      data: { order }
    });
  } catch (error) {
    next(error);
  }
};

// Create new order
const createOrder = async (req, res, next) => {
  const transaction = await require('../models').sequelize.transaction({
    isolationLevel: null // Remove isolation level for SQLite compatibility
  });

  try {
    console.log('📋 Creating new order...');
    console.log('📋 Request body:', JSON.stringify(req.body, null, 2));
    console.log('📋 User info:', { user_id: req.user.user_id, role: req.user.role });

    const {
      customer_id,
      salesman_id: requestSalesmanId,
      items,
      due_date,
      payment_method = 'cash',
      notes,
      discount_amount = 0
    } = req.body;

    console.log('📋 Extracted fields:', { customer_id, requestSalesmanId, items, due_date, payment_method, notes, discount_amount });

    // Determine salesman assignment based on user role and request
    let salesman_id = null;
    if (req.user.role === 'salesman') {
      // For salesman users, auto-assign their salesman ID
      const salesman = await Salesman.findOne({ where: { user_id: req.user.user_id } });
      if (salesman) {
        salesman_id = salesman.salesman_id;
        console.log('👤 Auto-assigning salesman (salesman user):', salesman_id);
      }
    } else if (req.user.role === 'admin' && requestSalesmanId) {
      // For admin users, use the salesman_id from request if provided
      salesman_id = requestSalesmanId;
      console.log('👤 Admin assigned salesman:', salesman_id);
    }

    console.log('📋 Final salesman assignment:', salesman_id);

    // Calculate totals
    let subtotal = 0;
    const orderItems = [];

    console.log('📋 Processing order items:', items.length, 'items');
    for (const item of items) {
      console.log('📋 Processing item:', item);
      const product = await Product.findByPk(item.product_id);
      console.log('📋 Found product:', product ? { id: product.product_id, name: product.name, stock: product.current_stock, price: product.unit_price } : 'NOT FOUND');

      if (!product) {
        throw new Error(`Product with ID ${item.product_id} not found`);
      }

      if (product.current_stock < item.quantity) {
        throw new Error(`Insufficient stock for ${product.name}. Available: ${product.current_stock}, Requested: ${item.quantity}`);
      }

      const itemTotal = item.quantity * item.unit_price;
      subtotal += itemTotal;
      console.log('📋 Item calculation:', { quantity: item.quantity, unit_price: item.unit_price, itemTotal, subtotal });

      orderItems.push({
        product_id: item.product_id,
        quantity: item.quantity,
        unit_price: item.unit_price,
        total_price: itemTotal,
        discount_amount: item.discount_amount || 0
      });
    }

    const total_amount = subtotal - discount_amount;

    // Create order
    const order = await Order.create({
      order_number: generateOrderNumber(),
      customer_id,
      salesman_id,
      due_date,
      payment_method,
      subtotal,
      discount_amount,
      total_amount,
      notes,
      created_by: req.user.user_id
    }, { transaction });

    // Create order items
    for (const itemData of orderItems) {
      await OrderItem.create({
        order_id: order.order_id,
        ...itemData
      }, { transaction });
    }

    await transaction.commit();

    // Get created order with all relations
    const createdOrder = await Order.findByPk(order.order_id, {
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['customer_id', 'name', 'type']
        },
        {
          model: Salesman,
          as: 'salesman',
          attributes: ['salesman_id', 'full_name']
        },
        {
          model: OrderItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product',
              attributes: ['product_id', 'name', 'unit_price']
            }
          ]
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: 'Order created successfully',
      data: { order: createdOrder }
    });
  } catch (error) {
    await transaction.rollback();
    next(error);
  }
};

// Update order payment status
const updateOrderPaymentStatus = async (req, res, next) => {
  const transaction = await require('../models').sequelize.transaction({
    isolationLevel: null // Remove isolation level for SQLite compatibility
  });

  try {
    const { id } = req.params;
    const { payment_status, paid_amount } = req.body;

    console.log('💳 Updating order payment status:', {
      orderId: id,
      payment_status,
      paid_amount
    });

    const order = await Order.findByPk(id);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // ENHANCEMENT: Validate order status for payment collection
    if (order.status !== 'delivered') {
      return res.status(400).json({
        success: false,
        message: `Cannot collect payment for order with status '${order.status}'. Only delivered orders can receive payments.`
      });
    }

    // ENHANCEMENT: Prevent payment collection for cancelled orders
    if (order.status === 'cancelled') {
      return res.status(400).json({
        success: false,
        message: 'Cannot collect payment for cancelled orders'
      });
    }

    // Validate paid amount doesn't exceed total amount
    if (paid_amount && paid_amount > order.total_amount) {
      return res.status(400).json({
        success: false,
        message: 'Paid amount cannot exceed total amount'
      });
    }

    // Update payment status and paid amount
    const updateData = {};
    if (payment_status) updateData.payment_status = payment_status;
    if (paid_amount !== undefined) updateData.paid_amount = paid_amount;

    await order.update(updateData, { transaction });

    await transaction.commit();

    // Get updated order with all relations
    const updatedOrder = await Order.findByPk(id, {
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['customer_id', 'name', 'type']
        },
        {
          model: Salesman,
          as: 'salesman',
          attributes: ['salesman_id', 'full_name']
        },
        {
          model: OrderItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product',
              attributes: ['product_id', 'name', 'unit_price']
            }
          ]
        }
      ]
    });

    console.log('✅ Order payment status updated successfully');

    res.json({
      success: true,
      message: 'Order payment status updated successfully',
      data: { order: updatedOrder }
    });
  } catch (error) {
    await transaction.rollback();
    console.error('❌ Failed to update order payment status:', error);
    next(error);
  }
};

// Update order status
const updateOrderStatus = async (req, res, next) => {
  const transaction = await require('../models').sequelize.transaction({
    isolationLevel: null // Remove isolation level for SQLite compatibility
  });

  try {
    const { id } = req.params;
    const { status } = req.body;

    console.log('📋 updateOrderStatus called with:', {
      orderId: id,
      status,
      body: req.body,
      params: req.params
    });

    const order = await Order.findByPk(id, {
      include: [
        {
          model: OrderItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product'
            }
          ]
        }
      ]
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // If marking as delivered, update inventory
    if (status === 'delivered' && order.status !== 'delivered') {
      for (const item of order.items) {
        const product = item.product;
        const newStock = product.current_stock - item.quantity;

        if (newStock < 0) {
          throw new Error(`Insufficient stock for ${product.name}`);
        }

        // Update product stock
        await product.update({ current_stock: newStock }, { transaction });

        // Create inventory transaction
        await InventoryTransaction.create({
          type: 'out',
          product_id: product.product_id,
          quantity: item.quantity,
          unit_price: item.unit_price,
          total_cost: item.total_price,
          customer_id: order.customer_id,
          order_id: order.order_id,
          notes: `Order delivery: ${order.order_number}`,
          recorded_by: req.user.user_id,
          transaction_date: new Date()
        }, { transaction });
      }
    }

    // ENHANCEMENT: Handle payment reset for cancelled orders
    const updateData = { status };

    // If order is being cancelled, reset payment information
    if (status === 'cancelled') {
      updateData.paid_amount = 0.00;
      updateData.payment_status = 'pending';
    }

    // Update order status and payment info if cancelled
    await order.update(updateData, { transaction });

    // ENHANCEMENT: Automated commission calculation when order is delivered
    if (status === 'delivered' && order.status !== 'delivered' && order.salesman_id) {
      try {
        console.log(chalk.green(`💰 Auto-calculating commission for delivered order: ${order.order_number}`));

        // Import commission models
        const { CommissionStage, CommissionAssignment, CommissionHistory } = require('../models');
        const moment = require('moment');

        // Check if commission already exists
        const existingAssignment = await CommissionAssignment.findOne({
          where: { order_id: order.order_id, salesman_id: order.salesman_id },
          transaction
        });

        if (!existingAssignment) {
          // Get appropriate commission stage
          const salesAmount = parseFloat(order.total_amount);
          const commissionStage = await CommissionStage.findOne({
            where: {
              is_active: true,
              min_sales_amount: { [Op.lte]: salesAmount },
              [Op.or]: [
                { max_sales_amount: { [Op.gte]: salesAmount } },
                { max_sales_amount: null }
              ]
            },
            order: [['min_sales_amount', 'DESC']],
            transaction
          });

          if (commissionStage) {
            // Check for per-salesman override first
            let commissionPercentage = parseFloat(commissionStage.commission_percentage);

            const override = await CommissionStageOverride.findOne({
              where: {
                stage_id: commissionStage.stage_id,
                salesman_id: order.salesman_id,
                is_active: true
              },
              transaction
            });

            if (override) {
              commissionPercentage = parseFloat(override.commission_percentage);
              console.log(`🎯 Auto-commission using override: ${(commissionPercentage * 100).toFixed(2)}%`);
            } else {
              console.log(`📊 Auto-commission using default: ${(commissionPercentage * 100).toFixed(2)}%`);
            }

            // Calculate commission
            const commissionAmount = salesAmount * commissionPercentage;
            const bonusAmount = parseFloat(commissionStage.bonus_amount) || 0;
            const totalCommission = commissionAmount + bonusAmount;

            // Create commission assignment
            const assignment = await CommissionAssignment.create({
              salesman_id: order.salesman_id,
              order_id: order.order_id,
              stage_id: commissionStage.stage_id,
              sales_amount: salesAmount,
              commission_percentage: commissionPercentage,
              commission_amount: commissionAmount,
              bonus_amount: bonusAmount,
              total_commission: totalCommission,
              period_start: moment().startOf('month').toDate(),
              period_end: moment().endOf('month').toDate(),
              status: 'calculated',
              created_by: req.user.user_id
            }, { transaction });

            // Create history entry
            await CommissionHistory.create({
              salesman_id: order.salesman_id,
              assignment_id: assignment.assignment_id,
              stage_id: commissionStage.stage_id,
              action_type: 'commission_calculated',
              new_values: {
                sales_amount: salesAmount,
                commission_amount: commissionAmount,
                bonus_amount: bonusAmount,
                total_commission: totalCommission
              },
              amount_involved: totalCommission,
              description: `Auto-calculated commission for delivered order ${order.order_number}`,
              created_by: req.user.user_id
            }, { transaction });

            console.log(chalk.green(`✅ Commission auto-calculated: $${totalCommission.toFixed(2)} for order ${order.order_number}`));
          } else {
            console.log(chalk.yellow(`⚠️ No applicable commission stage found for order ${order.order_number} (amount: $${salesAmount})`));
          }
        } else {
          console.log(chalk.blue(`ℹ️ Commission already exists for order ${order.order_number}`));
        }
      } catch (commissionError) {
        console.error(chalk.red(`❌ Error calculating commission for order ${order.order_number}:`), commissionError);
        // Don't fail the order update if commission calculation fails
      }
    }

    await transaction.commit();

    // Get updated order
    const updatedOrder = await Order.findByPk(id, {
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['customer_id', 'name', 'type']
        },
        {
          model: Salesman,
          as: 'salesman',
          attributes: ['salesman_id', 'full_name']
        },
        {
          model: OrderItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product',
              attributes: ['product_id', 'name', 'unit_price']
            }
          ]
        }
      ]
    });



    res.json({
      success: true,
      message: 'Order status updated successfully',
      data: { order: updatedOrder }
    });
  } catch (error) {
    await transaction.rollback();
    next(error);
  }
};

module.exports = {
  getOrders,
  getOrder,
  createOrder,
  updateOrderStatus,
  updateOrderPaymentStatus
};
