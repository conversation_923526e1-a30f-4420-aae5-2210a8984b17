const express = require('express');
const router = express.Router();
const {
  getEmployees,
  getEmployee,
  createEmployee,
  updateEmployee,
  deleteEmployee,
  getDepartments,
  getPositions
} = require('../controllers/employeeController');
const {
  getSalaryAssignments,
  createSalaryAssignment,
  updateSalaryAssignment,
  processSalaryPayment,
  getSalaryPayments
} = require('../controllers/salaryController');
const { validateUUID } = require('../middleware/validation');
const { authenticateToken, adminOnly } = require('../middleware/auth');
const { body } = require('express-validator');

// All routes require authentication
router.use(authenticateToken);

// Validation middleware for employee creation/update
const validateEmployee = [
  body('full_name')
    .notEmpty()
    .withMessage('Full name is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('Full name must be between 2 and 100 characters'),
  body('email')
    .isEmail()
    .withMessage('Valid email is required'),
  body('position')
    .notEmpty()
    .withMessage('Position is required'),
  body('department')
    .notEmpty()
    .withMessage('Department is required'),
  body('hire_date')
    .optional()
    .isISO8601()
    .withMessage('Hire date must be a valid date'),
  body('phone')
    .optional()
    .isMobilePhone()
    .withMessage('Valid phone number is required'),
  body('employee_type')
    .optional()
    .isIn(['full_time', 'part_time', 'contract', 'intern'])
    .withMessage('Invalid employee type'),
  body('manager_id')
    .optional()
    .isUUID()
    .withMessage('Manager ID must be a valid UUID')
];

// Validation middleware for salary assignment
const validateSalaryAssignment = [
  body('employee_id')
    .isUUID()
    .withMessage('Employee ID must be a valid UUID'),
  body('base_salary')
    .isFloat({ min: 0 })
    .withMessage('Base salary must be a positive number'),
  body('currency')
    .optional()
    .isLength({ min: 3, max: 3 })
    .withMessage('Currency must be a 3-letter code'),
  body('pay_frequency')
    .optional()
    .isIn(['weekly', 'bi_weekly', 'monthly', 'quarterly', 'annually'])
    .withMessage('Invalid pay frequency'),
  body('effective_date')
    .optional()
    .isISO8601()
    .withMessage('Effective date must be a valid date')
];

// Employee routes (admin only)
router.get('/', adminOnly, getEmployees);
router.get('/departments', adminOnly, getDepartments);
router.get('/positions', adminOnly, getPositions);
router.get('/:id', adminOnly, validateUUID('id'), getEmployee);
router.post('/', adminOnly, validateEmployee, createEmployee);
router.put('/:id', adminOnly, validateUUID('id'), validateEmployee, updateEmployee);
router.delete('/:id', adminOnly, validateUUID('id'), deleteEmployee);

// Salary assignment routes (admin only)
router.get('/:employeeId/salary-assignments', adminOnly, validateUUID('employeeId'), getSalaryAssignments);
router.post('/salary-assignments', adminOnly, validateSalaryAssignment, createSalaryAssignment);
router.put('/salary-assignments/:id', adminOnly, validateUUID('id'), validateSalaryAssignment, updateSalaryAssignment);

// Salary payment routes (admin only)
router.get('/salary-payments', adminOnly, getSalaryPayments);
router.post('/salary-payments', adminOnly, processSalaryPayment);

module.exports = router;
