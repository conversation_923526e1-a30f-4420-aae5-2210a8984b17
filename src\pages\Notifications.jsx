
import { BellIcon, AlertCircleIcon, CheckCircleIcon, InfoIcon, ClockIcon, XIcon } from 'lucide-react';
import { useData } from '../contexts/DataContext';

function Notifications() {
  const { orders, customers, getLowStockProducts } = useData();
  const lowStockProducts = getLowStockProducts();

  const notifications = [
    ...lowStockProducts.map(product => ({
      id: `stock-${product.id}`,
      type: 'warning',
      title: 'Low Stock Alert',
      message: `${product.name} is running low with only ${product.stock} units remaining.`,
      date: new Date().toISOString(),
      read: false
    })),
    ...orders.filter(order => order.paymentStatus !== 'paid' && new Date(order.dueDate) < new Date()).map(order => {
      const customer = customers.find(c => c.id === order.customerId);
      return {
        id: `payment-${order.id}`,
        type: 'danger',
        title: 'Overdue Payment',
        message: `Payment for order ${order.orderNumber} from ${customer ? customer.name : 'Unknown Customer'} is overdue.`,
        date: new Date().toISOString(),
        read: false
      };
    }),
    {
      id: 'system-1',
      type: 'info',
      title: 'System Update',
      message: 'The system will undergo maintenance tonight from 2 AM to 4 AM.',
      date: '2023-07-01T10:30:00Z',
      read: true
    },
    {
      id: 'system-2',
      type: 'success',
      title: 'Report Generated',
      message: 'June 2023 Sales Report has been generated and is ready for review.',
      date: '2023-06-30T16:45:00Z',
      read: true
    }
  ];

  const sortedNotifications = [...notifications].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  const unreadNotifications = sortedNotifications.filter(n => !n.read);
  const readNotifications = sortedNotifications.filter(n => n.read);

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'warning':
        return <AlertCircleIcon size={20} className="text-yellow-500" />;
      case 'danger':
        return <AlertCircleIcon size={20} className="text-red-500" />;
      case 'success':
        return <CheckCircleIcon size={20} className="text-green-500" />;
      case 'info':
      default:
        return <InfoIcon size={20} className="text-blue-500" />;
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Notifications</h1>
          <p className="text-gray-600">Stay updated with important alerts and messages</p>
        </div>
        <div className="flex space-x-2">
          <button className="flex items-center bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
            <CheckCircleIcon size={16} className="mr-2" />
            Mark All as Read
          </button>
        </div>
      </div>
      {/* Notification Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-white rounded-lg shadow-md p-4">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-sm font-medium text-gray-500">ALL NOTIFICATIONS</h3>
            <BellIcon size={18} className="text-blue-600" />
          </div>
          <p className="text-xl font-bold text-gray-900">{notifications.length}</p>
        </div>
        <div className="bg-white rounded-lg shadow-md p-4">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-sm font-medium text-gray-500">UNREAD</h3>
            <BellIcon size={18} className="text-yellow-600" />
          </div>
          <p className="text-xl font-bold text-gray-900">{unreadNotifications.length}</p>
        </div>
        <div className="bg-white rounded-lg shadow-md p-4">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-sm font-medium text-gray-500">ALERTS</h3>
            <AlertCircleIcon size={18} className="text-red-600" />
          </div>
          <p className="text-xl font-bold text-gray-900">{notifications.filter(n => n.type === 'warning' || n.type === 'danger').length}</p>
        </div>
        <div className="bg-white rounded-lg shadow-md p-4">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-sm font-medium text-gray-500">SYSTEM</h3>
            <InfoIcon size={18} className="text-indigo-600" />
          </div>
          <p className="text-xl font-bold text-gray-900">{notifications.filter(n => n.type === 'info' || n.type === 'success').length}</p>
        </div>
      </div>
      {/* Unread Notifications */}
      {unreadNotifications.length > 0 && (
        <div className="bg-white rounded-lg shadow-md overflow-hidden mb-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-800">Unread Notifications</h2>
          </div>
          <div className="divide-y divide-gray-200">
            {unreadNotifications.map(notification => (
              <div key={notification.id} className="p-4 hover:bg-gray-50">
                <div className="flex items-start">
                  <div className="flex-shrink-0 mt-0.5">{getNotificationIcon(notification.type)}</div>
                  <div className="ml-3 flex-1">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium text-gray-900">{notification.title}</p>
                      <div className="flex items-center">
                        <p className="text-xs text-gray-500">{formatDate(notification.date)}</p>
                        <button className="ml-2 text-gray-400 hover:text-gray-500">
                          <XIcon size={16} />
                        </button>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{notification.message}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
      {/* Read Notifications */}
      {readNotifications.length > 0 && (
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-800">Earlier Notifications</h2>
          </div>
          <div className="divide-y divide-gray-200">
            {readNotifications.map(notification => (
              <div key={notification.id} className="p-4 hover:bg-gray-50 opacity-75">
                <div className="flex items-start">
                  <div className="flex-shrink-0 mt-0.5">{getNotificationIcon(notification.type)}</div>
                  <div className="ml-3 flex-1">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium text-gray-900">{notification.title}</p>
                      <div className="flex items-center">
                        <p className="text-xs text-gray-500">{formatDate(notification.date)}</p>
                        <button className="ml-2 text-gray-400 hover:text-gray-500">
                          <XIcon size={16} />
                        </button>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{notification.message}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
      {/* No Notifications */}
      {notifications.length === 0 && (
        <div className="bg-white rounded-lg shadow-md p-10 text-center">
          <BellIcon size={40} className="mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Notifications</h3>
          <p className="text-gray-600">You're all caught up! There are no notifications at the moment.</p>
        </div>
      )}
    </div>
  );
}

export default Notifications;
