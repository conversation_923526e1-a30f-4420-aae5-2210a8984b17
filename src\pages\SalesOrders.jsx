import React, { useState, Fragment } from 'react';
import { Link } from 'react-router-dom';
import { ShoppingCartIcon, PlusIcon, FilterIcon, SearchIcon, EyeIcon, CheckIcon, XIcon, ChevronLeftIcon, ChevronRightIcon, DollarSignIcon, PackageIcon } from 'lucide-react';
import { useData } from '../contexts/DataContext';
import { useApiData } from '../contexts/ApiDataContext';
import { useAuth } from '../contexts/AuthContext';
import RefreshButton from '../components/RefreshButton';
import useAutoRefresh from '../hooks/useAutoRefresh';
import AutoRefreshControl from '../components/AutoRefreshControl';
import useDataRefresh from '../hooks/useDataRefresh';

const SalesOrders = () => {
  console.log('🚀 SalesOrders component rendering...');

  try {
    const {
      orders,
      customers,
      products,
      salesmen,
      refreshOrders,
      refreshAllData
    } = useData();

    console.log('📊 Data from useData:', { orders: orders?.length, customers: customers?.length, products: products?.length, salesmen: salesmen?.length });

    // Import backend API function
    const { addOrder } = useApiData();
    console.log('✅ useApiData hook working, addOrder function available:', typeof addOrder);

    // Get current user info for role-based salesman assignment
    const { user, isAdmin, isSalesman } = useAuth();
    console.log('👤 Current user:', { role: user?.role, isAdmin, isSalesman });

    const [filters, setFilters] = useState({
    dateFrom: '',
    dateTo: '',
    customer: '',
    status: ''
  });
  const [showOrderDetails, setShowOrderDetails] = useState(null);
  const [showNewOrder, setShowNewOrder] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  // New order form state
  // FIXED: Direct sales order creation - Initialize salesmanId as empty string instead of hardcoded '1'
  // This allows admin users to create orders without assigned salesmen (direct sales)
  const [newOrder, setNewOrder] = useState({
    customerId: '',
    salesmanId: '', // Empty string for "No Salesman Assigned" by default
    items: [{
      productId: '',
      quantity: 1
    }],
    orderDate: new Date().toISOString().split('T')[0],
    dueDate: new Date(new Date().setDate(new Date().getDate() + 30)).toISOString().split('T')[0],
    status: 'pending',
    paymentStatus: 'pending'
  });

  const handleFilterChange = (e) => {
    const {
      name,
      value
    } = e.target;
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleViewOrder = (id) => {
    setShowOrderDetails(id === showOrderDetails ? null : id);
  };

  const handleNewOrderChange = (e) => {
    const {
      name,
      value
    } = e.target;
    setNewOrder(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleItemChange = (index, field, value) => {
    const updatedItems = [...newOrder.items];
    updatedItems[index] = {
      ...updatedItems[index],
      [field]: field === 'quantity' ? parseInt(value) || 0 : value
    };
    setNewOrder(prev => ({
      ...prev,
      items: updatedItems
    }));
  };

  const addItem = () => {
    setNewOrder(prev => ({
      ...prev,
      items: [...prev.items, {
        productId: '',
        quantity: 1
      }]
    }));
  };

  const removeItem = (index) => {
    const updatedItems = [...newOrder.items];
    updatedItems.splice(index, 1);
    setNewOrder(prev => ({
      ...prev,
      items: updatedItems
    }));
  };

  const handleCreateOrder = async () => {
    try {
      // Validate form
      if (!newOrder.customerId || newOrder.items.some(item => !item.productId || item.quantity <= 0)) {
        alert('Please fill in all required fields');
        return;
      }

      // Prepare order items for backend API
      const orderItems = newOrder.items.map(item => {
        // Note: item.productId is already the UUID string from the dropdown value
        const product = products.find(p => p.id === item.productId);
        console.log('🔍 Looking for product:', {
          searchingFor: item.productId,
          found: product ? { id: product.id, name: product.name, price: product.unit_price || product.price } : 'NOT FOUND'
        });

        if (!product) return null;

        const unitPrice = product.unit_price || product.price || 0;
        return {
          product_id: product.id, // Use the actual product ID (UUID from backend)
          quantity: parseInt(item.quantity),
          unit_price: parseFloat(unitPrice),
          discount_amount: 0
        };
      }).filter(item => item !== null);

      console.log('📋 Order items prepared:', orderItems);

      // Calculate total amount
      const totalAmount = orderItems.reduce((sum, item) => sum + (item.unit_price * item.quantity), 0);

      // Find the selected customer to get the UUID
      // Note: newOrder.customerId is already the UUID string from the dropdown value
      const selectedCustomer = customers.find(c => c.id === newOrder.customerId);
      console.log('🔍 Looking for customer:', {
        searchingFor: newOrder.customerId,
        availableCustomers: customers.map(c => ({ id: c.id, name: c.name }))
      });

      if (!selectedCustomer) {
        alert('Selected customer not found');
        return;
      }

      console.log('✅ Found customer:', selectedCustomer);

      // Handle salesman assignment based on user role
      let salesmanId = null;
      console.log('👤 Salesman assignment logic:', {
        isSalesman,
        isAdmin,
        userSalesmanProfile: user?.salesmanProfile,
        formSalesmanId: newOrder.salesmanId,
        formSalesmanIdType: typeof newOrder.salesmanId
      });

      if (isSalesman && user?.salesmanProfile) {
        // For salesman users, auto-assign their salesman ID
        salesmanId = user.salesmanProfile.salesman_id;
        console.log('👤 Auto-assigning salesman (salesman user):', salesmanId);
      } else if (isAdmin && newOrder.salesmanId && newOrder.salesmanId.trim() !== '') {
        // For admin users, use selected salesman if any (and not empty string)
        salesmanId = newOrder.salesmanId;
        console.log('👤 Admin selected salesman:', salesmanId);
      } else {
        console.log('👤 No salesman assigned (direct sale)');
      }

      // Prepare order data for backend API
      const orderData = {
        customer_id: selectedCustomer.id, // Use the actual customer UUID from backend
        salesman_id: salesmanId, // Include salesman assignment
        items: orderItems,
        due_date: newOrder.dueDate,
        payment_method: 'cash',
        notes: '',
        discount_amount: 0,
        total_amount: totalAmount
      };

      console.log('🚀 Creating order with data:', orderData);

      // Call backend API
      const createdOrder = await addOrder(orderData);
      console.log('✅ Order created successfully:', createdOrder);

      // ENHANCEMENT: Trigger automatic data refresh after order creation
      await refreshAfterOrderCreate();

      // Reset form and close modal
      setNewOrder({
        customerId: '',
        salesmanId: '', // Empty string for "No Salesman Assigned" by default
        items: [{
          productId: '',
          quantity: 1
        }],
        orderDate: new Date().toISOString().split('T')[0],
        dueDate: new Date(new Date().setDate(new Date().getDate() + 30)).toISOString().split('T')[0],
        status: 'pending',
        paymentStatus: 'pending'
      });
      setShowNewOrder(false);

      alert('Order created successfully!');
    } catch (error) {
      console.error('❌ Failed to create order:', error);
      alert(`Failed to create order: ${error.message}`);
    }
  };

  // Apply filters
  const filteredOrders = orders.filter(order => {
    if (filters.dateFrom && new Date(order.date) < new Date(filters.dateFrom)) {
      return false;
    }
    if (filters.dateTo && new Date(order.date) > new Date(filters.dateTo)) {
      return false;
    }
    if (filters.customer && order.customerId !== parseInt(filters.customer)) {
      return false;
    }
    if (filters.status && order.paymentStatus !== filters.status) {
      return false;
    }
    return true;
  });

  // Sort by date, newest first
  const sortedOrders = [...filteredOrders].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  // ENHANCEMENT: Comprehensive refresh function for Sales Orders
  const handleRefreshData = async () => {
    try {
      console.log('🔄 Refreshing Sales Orders data...');
      // Refresh all relevant data for sales orders page
      await Promise.all([
        refreshOrders()
      ]);
      console.log('✅ Sales Orders data refreshed successfully');
    } catch (error) {
      console.error('❌ Failed to refresh Sales Orders data:', error);
      throw error; // Re-throw to let RefreshButton handle the error
    }
  };

  // ENHANCEMENT: Auto-refresh functionality
  const {
    isAutoRefreshEnabled,
    isPaused,
    lastRefresh,
    toggleAutoRefresh,
    manualRefresh
  } = useAutoRefresh(handleRefreshData, {
    interval: 3000, // 3 seconds
    enabled: true,
    pauseOnInteraction: true,
    interactionTimeout: 5000 // 5 seconds
  });

  // ENHANCEMENT: Auto-refresh after CRUD operations
  const { refreshAfterOrderCreate } = useDataRefresh();

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Sales Orders</h1>
          <p className="text-gray-600">Manage customer orders and deliveries</p>
        </div>
        <div className="flex items-center space-x-4">
          <AutoRefreshControl
            isAutoRefreshEnabled={isAutoRefreshEnabled}
            isPaused={isPaused}
            lastRefresh={lastRefresh}
            toggleAutoRefresh={toggleAutoRefresh}
            interval={3000}
            size="sm"
          />
          <RefreshButton
            onRefresh={manualRefresh}
            variant="outline"
            size="md"
            label="Refresh Now"
          />
          <button className="flex items-center bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700" onClick={() => setShowNewOrder(true)}>
            <PlusIcon size={16} className="mr-2" />
            New Order
          </button>
        </div>
      </div>

      {/* New Order Modal */}
      {showNewOrder && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full p-6 max-h-screen overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">
                Create New Order
              </h3>
              <button onClick={() => setShowNewOrder(false)} className="text-gray-400 hover:text-gray-500">
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label htmlFor="customerId" className="block text-sm font-medium text-gray-700 mb-1">
                  Customer
                </label>
                <select id="customerId" name="customerId" value={newOrder.customerId} onChange={handleNewOrderChange} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                  <option  value="">Select Customer</option>
                  {customers.map(customer => (
                    <option key={customer.id} value={customer.id}>
                      {customer.name}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label htmlFor="orderDate" className="block text-sm font-medium text-gray-700 mb-1">
                  Order Date
                </label>
                <input type="date" id="orderDate" name="orderDate" value={newOrder.orderDate} onChange={handleNewOrderChange} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required />
              </div>
              <div>
                <label htmlFor="dueDate" className="block text-sm font-medium text-gray-700 mb-1">
                  Due Date
                </label>
                <input type="date" id="dueDate" name="dueDate" value={newOrder.dueDate} onChange={handleNewOrderChange} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required />
              </div>

              {/* Salesman Selection - Only for Admin Users */}
              {isAdmin && (
                <div>
                  <label htmlFor="salesmanId" className="block text-sm font-medium text-gray-700 mb-1">
                    Salesman (Optional)
                  </label>
                  <select id="salesmanId" name="salesmanId" value={newOrder.salesmanId} onChange={handleNewOrderChange} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">No Salesman Assigned (Direct Sale)</option>
                    {salesmen.map(salesman => (
                      <option key={salesman.id} value={salesman.id}>
                        {salesman.name}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {/* Salesman Info Display - For Salesman Users */}
              {isSalesman && user?.salesmanProfile && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Assigned Salesman
                  </label>
                  <div className="w-full px-3 py-2 border border-gray-200 rounded-md bg-gray-50 text-gray-700">
                    {user.salesmanProfile.full_name} (Auto-assigned)
                  </div>
                </div>
              )}
            </div>

            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                <h4 className="text-md font-medium text-gray-800">
                  Order Items
                </h4>
                <button type="button" onClick={addItem} className="flex items-center text-blue-600 hover:text-blue-800">
                  <PlusIcon size={16} className="mr-1" />
                  Add Item
                </button>
              </div>
              <div className="border rounded-md overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Product
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Quantity
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Selling Price
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Total
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Action
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {newOrder.items.map((item, index) => {
                      // Note: item.productId is already the UUID string from the dropdown value
                      const product = item.productId ? products.find(p => p.id === item.productId) : null;
                      const unitPrice = product ? (product.unit_price || product.price || 0) : 0;
                      const total = product ? unitPrice * item.quantity : 0;
                      return (
                        <tr key={index}>
                          <td className="px-4 py-2">
                            <select value={item.productId} onChange={e => handleItemChange(index, 'productId', e.target.value)} className="w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                              <option value="">Select Product</option>
                              {products.map(product => (
                                <option key={product.id} value={product.id}>
                                  {product.name} ({product.stock} in stock)
                                </option>
                              ))}
                            </select>
                          </td>
                          <td className="px-4 py-2">
                            <input type="number" min="1" value={item.quantity} onChange={e => handleItemChange(index, 'quantity', e.target.value)} className="w-20 px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required />
                          </td>
                          <td className="px-4 py-2 text-sm text-gray-900">
                            {formatCurrency(unitPrice)}
                          </td>
                          <td className="px-4 py-2 text-sm font-medium text-gray-900">
                            {formatCurrency(total)}
                          </td>
                          <td className="px-4 py-2">
                            {newOrder.items.length > 1 && (
                              <button type="button" onClick={() => removeItem(index)} className="text-red-600 hover:text-red-800">
                                <XIcon size={16} />
                              </button>
                            )}
                          </td>
                        </tr>
                      );
                    })}
                    <tr className="bg-gray-50">
                      <td colSpan={3} className="px-4 py-2 text-right text-sm font-medium text-gray-900">
                        Total:
                      </td>
                      <td className="px-4 py-2 text-sm font-bold text-gray-900">
                        {formatCurrency(newOrder.items.reduce((sum, item) => {
                          // Note: item.productId is already the UUID string from the dropdown value
                          const product = item.productId ? products.find(p => p.id === item.productId) : null;
                          const unitPrice = product ? (product.unit_price || product.price || 0) : 0;
                          return sum + (unitPrice * item.quantity);
                        }, 0))}
                      </td>
                      <td></td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <div className="flex justify-end">
              <button type="button" className="px-4 py-2 mr-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50" onClick={() => setShowNewOrder(false)}>
                Cancel
              </button>
              <button type="button" className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700" onClick={handleCreateOrder}>
                Create Order
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="flex items-center mb-4">
          <FilterIcon size={20} className="text-blue-600 mr-2" />
          <h2 className="text-lg font-medium text-gray-800">Filters</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <label htmlFor="dateFrom" className="block text-sm font-medium text-gray-700 mb-1">
              Date From
            </label>
            <input type="date" id="dateFrom" name="dateFrom" value={filters.dateFrom} onChange={handleFilterChange} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" />
          </div>
          <div>
            <label htmlFor="dateTo" className="block text-sm font-medium text-gray-700 mb-1">
              Date To
            </label>
            <input type="date" id="dateTo" name="dateTo" value={filters.dateTo} onChange={handleFilterChange} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" />
          </div>
          <div>
            <label htmlFor="customer" className="block text-sm font-medium text-gray-700 mb-1">
              Customer
            </label>
            <select id="customer" name="customer" value={filters.customer} onChange={handleFilterChange} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="">All Customers</option>
              {customers.map(customer => (
                <option key={customer.id} value={customer.id}>
                  {customer.name}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
              Payment Status
            </label>
            <select id="status" name="status" value={filters.status} onChange={handleFilterChange} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="">All Statuses</option>
              <option value="paid">Paid</option>
              <option value="partial">Partially Paid</option>
              <option value="pending">Pending</option>
            </select>
          </div>
        </div>
        <div className="flex justify-end mt-4">
          <button className="px-4 py-2 mr-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50" onClick={() => setFilters({
            dateFrom: '',
            dateTo: '',
            customer: '',
            status: ''
          })}>
            Clear Filters
          </button>
          <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
            Apply Filters
          </button>
        </div>
      </div>

      {/* Orders Summary */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-white rounded-lg shadow-md p-4">
            <div className="flex justify-between items-center mb-2">
          <h3 className="text-sm font-medium text-gray-500">TOTAL ORDERS</h3>
          <ShoppingCartIcon size={18} className="text-blue-600" />
            </div>
            <p className="text-xl font-bold text-gray-900">{orders.length}</p>
          </div>
          <div className="bg-white rounded-lg shadow-md p-4">
            <div className="flex justify-between items-center mb-2">
          <h3 className="text-sm font-medium text-gray-500">PAID ORDERS</h3>
          <CheckIcon size={18} className="text-green-600" />
            </div>
            <p className="text-xl font-bold text-gray-900">
          {orders.filter(o => o.paymentStatus === 'paid').length}
            </p>
          </div>
          <div className="bg-white rounded-lg shadow-md p-4">
            <div className="flex justify-between items-center mb-2">
          <h3 className="text-sm font-medium text-gray-500">
            PENDING PAYMENT
          </h3>
          <DollarSignIcon size={18} className="text-yellow-600" />
            </div>
            <p className="text-xl font-bold text-gray-900">
          {orders.filter(o => o.paymentStatus !== 'paid' && o.status !== 'cancelled').length}
            </p>
          </div>
          <div className="bg-white rounded-lg shadow-md p-4">
            <div className="flex justify-between items-center mb-2">
          <h3 className="text-sm font-medium text-gray-500">TOTAL SALES</h3>
          <DollarSignIcon size={18} className="text-indigo-600" />
            </div>
            <p className="text-xl font-bold text-gray-900">
          {formatCurrency(
            orders
              .filter(order => order.status === 'delivered')
              .reduce((sum, order) => sum + order.totalAmount, 0)
          )}
            </p>
          </div>
        </div>

        {/* Orders Table */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Order #
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Customer
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Payment Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {sortedOrders.map(order => {
                const customer = customers.find(c => c.id === order.customerId);
                return (
                <Fragment key={order.id}>
                  <tr>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">
                      {order.orderNumber}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {customer ? customer.name : 'Unknown Customer'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {order.date}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                      {formatCurrency(order.totalAmount)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${order.status === 'delivered' ? 'bg-green-100 text-green-800' : order.status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}`}>
                        {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {(() => {
                        // FIXED: Apply cancelled order payment status logic
                        const isCancelledOrder = order.status === 'cancelled';
                        const actualPaymentStatus = isCancelledOrder ? 'cancelled' : order.paymentStatus;

                        const getStatusBadgeClass = (status) => {
                          switch (status) {
                            case 'paid': return 'bg-green-100 text-green-800';
                            case 'partial': return 'bg-blue-100 text-blue-800';
                            case 'pending': return 'bg-yellow-100 text-yellow-800';
                            case 'cancelled': return 'bg-red-100 text-red-800';
                            default: return 'bg-gray-100 text-gray-800';
                          }
                        };

                        const getStatusText = (status) => {
                          switch (status) {
                            case 'paid': return 'Paid';
                            case 'partial': return 'Partially Paid';
                            case 'pending': return 'Pending';
                            case 'cancelled': return 'Cancelled';
                            default: return 'Unknown';
                          }
                        };

                        return (
                          <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(actualPaymentStatus)}`}>
                            {getStatusText(actualPaymentStatus)}
                          </span>
                        );
                      })()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button className="text-blue-600 hover:text-blue-900" onClick={() => handleViewOrder(order.id)}>
                        <EyeIcon size={18} />
                      </button>
                    </td>
                  </tr>
                  {showOrderDetails === order.id && (
                    <tr>
                      <td colSpan={7} className="px-6 py-4 bg-gray-50">
                        <div className="border rounded-md overflow-hidden">
                          <div className="bg-gray-100 px-4 py-2 font-medium">
                            Order Details
                          </div>
                          <div className="p-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                              <div>
                                <p className="text-sm text-gray-500 mb-1">
                                  Customer Information
                                </p>
                                <p className="text-sm mb-1">
                                  <span className="font-medium">Name:</span>{' '}
                                  {order.customer.name}
                                </p>
                                <p className="text-sm mb-1">
                                  <span className="font-medium">Contact:</span>{' '}
                                  {order.customer.contact}
                                </p>
                                <p className="text-sm">
                                  <span className="font-medium">Phone:</span>{' '}
                                  {order.customer.phone}
                                </p>
                              </div>
                              <div>
                                <p className="text-sm text-gray-500 mb-1">
                                  Order Information
                                </p>
                                <p className="text-sm mb-1">
                                  <span className="font-medium">
                                    Order Date:
                                  </span>{' '}
                                  {order.date}
                                </p>
                                <p className="text-sm mb-1">
                                  <span className="font-medium">Due Date:</span>{' '}
                                  {order.dueDate}
                                </p>
                                <p className="text-sm">
                                  <span className="font-medium">Salesman:</span>{' '}
                                  {order.salesman ? order.salesman.name : 'No salesman assigned'}
                                </p>
                              </div>
                            </div>
                            <div className="mb-4">
                              <p className="text-sm text-gray-500 mb-1">
                                Order Items
                              </p>
                              <table className="min-w-full divide-y divide-gray-200 border rounded-md">
                                <thead className="bg-gray-50">
                                  <tr>
                                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                      Product
                                    </th>
                                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                      Quantity
                                    </th>
                                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                      Selling Price
                                    </th>
                                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                      Total
                                    </th>
                                  </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                  {order.items.map((item, index) => (
                                    <tr key={index}>
                                      <td className="px-4 py-2 text-sm text-gray-900">
                                        {item.product.name}
                                      </td>
                                      <td className="px-4 py-2 text-sm text-gray-500">
                                        {item.quantity}
                                      </td>
                                      <td className="px-4 py-2 text-sm text-gray-500">
                                        {formatCurrency(item.unitPrice)}
                                      </td>
                                      <td className="px-4 py-2 text-sm font-medium text-gray-900">
                                        {formatCurrency(item.totalPrice)}
                                      </td>
                                    </tr>
                                  ))}
                                  <tr className="bg-gray-50">
                                    <td colSpan={3} className="px-4 py-2 text-right text-sm font-medium text-gray-900">
                                      Total:
                                    </td>
                                    <td className="px-4 py-2 text-sm font-bold text-gray-900">
                                      {formatCurrency(order.totalAmount)}
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </div>
                            <div className="mb-4">
                              <p className="text-sm text-gray-500 mb-1">
                                Payment Status
                              </p>
                              <div className="bg-gray-50 p-3 rounded-md border border-gray-200">
                                <div className="flex justify-between mb-1">
                                  <span className="text-sm">Total Amount:</span>
                                  <span className="text-sm font-medium">
                                    {formatCurrency(order.totalAmount)}
                                  </span>
                                </div>
                                <div className="flex justify-between mb-1">
                                  <span className="text-sm">Amount Paid:</span>
                                  <span className="text-sm font-medium text-green-600">
                                    {formatCurrency(order.paidAmount)}
                                  </span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-sm">
                                    Remaining Balance:
                                  </span>
                                  <span className="text-sm font-medium text-red-600">
                                    {(() => {
                                      // FIXED: Apply cancelled order remaining balance logic
                                      const isCancelledOrder = order.status === 'cancelled';
                                      const remainingAmount = isCancelledOrder ? 0 : (order.totalAmount - order.paidAmount);
                                      return formatCurrency(remainingAmount);
                                    })()}
                                  </span>
                                </div>
                              </div>
                            </div>
                            <div className="flex justify-end">
                              <button className="px-3 py-1 mr-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50" onClick={() => setShowOrderDetails(null)}>
                                Close
                              </button>
                              {order.paymentStatus !== 'paid' && order.status !== 'cancelled' && (
                                <Link to="/sales/credits" className="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                  Record Payment
                                </Link>
                              )}
                              {order.status === 'cancelled' && (
                                <span className="px-3 py-1 text-red-600 text-sm">
                                  Payment cancelled
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                  )}
                </Fragment>
                );
              })}
            </tbody>
          </table>
        </div>
        {/* Pagination */}
        <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing <span className="font-medium">1</span> to{' '}
                <span className="font-medium">{sortedOrders.length}</span> of{' '}
                <span className="font-medium">{orders.length}</span> orders
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50" disabled={currentPage === 1} onClick={() => setCurrentPage(currentPage - 1)}>
                  <span className="sr-only">Previous</span>
                  <ChevronLeftIcon size={16} />
                </button>
                <button className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-blue-600 hover:bg-gray-50">
                  1
                </button>
                <button className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50" onClick={() => setCurrentPage(currentPage + 1)}>
                  <span className="sr-only">Next</span>
                  <ChevronRightIcon size={16} />
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
  } catch (error) {
    console.error('❌ Error in SalesOrders component:', error);
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold text-red-600 mb-4">Error Loading Orders</h1>
        <p className="text-gray-600 mb-4">There was an error loading the orders page:</p>
        <pre className="bg-red-50 p-4 rounded-md text-sm text-red-800 overflow-auto">
          {error.message}
        </pre>
        <button
          onClick={() => window.location.reload()}
          className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
        >
          Reload Page
        </button>
      </div>
    );
  }
};

export default SalesOrders;