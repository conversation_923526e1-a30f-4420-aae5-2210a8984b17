const express = require('express');
const router = express.Router();
const { Payment, Order, Customer } = require('../models');
const { authenticateToken, adminOr<PERSON><PERSON><PERSON> } = require('../middleware/auth');
const { validateUUID } = require('../middleware/validation');
const chalk = require('chalk');
const moment = require('moment');

// All routes require authentication
router.use(authenticateToken);

// Get all payments with order and customer details
router.get('/', adminOrSalesman, async (req, res) => {
  try {
    console.log(chalk.blue(`\n💳 [${moment().format('YYYY-MM-DD HH:mm:ss.SSS')}] FETCHING ALL PAYMENTS`));

    const payments = await Payment.findAll({
      include: [
        {
          model: Order,
          as: 'order',
          include: [
            {
              model: Customer,
              as: 'customer',
              attributes: ['customer_id', 'name', 'email', 'phone']
            }
          ]
        }
      ],
      order: [['created_at', 'DESC']]
    });

    console.log(`✅ Found ${payments.length} payments`);

    // Transform data for frontend compatibility
    const transformedPayments = payments.map(payment => ({
      id: payment.payment_id,
      orderId: payment.order_id,
      orderNumber: payment.order?.order_number || 'N/A',
      customerName: payment.order?.customer?.name || 'Unknown Customer',
      amount: parseFloat(payment.amount),
      paymentMethod: payment.payment_method,
      paymentDate: payment.payment_date?.split('T')[0] || payment.created_at?.split('T')[0],
      status: payment.status,
      notes: payment.notes,
      createdAt: payment.created_at,
      updatedAt: payment.updated_at
    }));

    // Log data change for monitoring
    if (global.logDataChange) {
      global.logDataChange('READ', 'payments', { count: payments.length }, req.user?.user_id);
    }

    res.json({
      success: true,
      data: transformedPayments,
      count: payments.length
    });
  } catch (error) {
    console.error('❌ Error fetching payments:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch payments',
      error: error.message
    });
  }
});

// Get payment by ID
router.get('/:id', adminOrSalesman, validateUUID('id'), async (req, res) => {
  try {
    const { id } = req.params;
    console.log(chalk.blue(`💳 Fetching payment with ID: ${id}`));

    const payment = await Payment.findByPk(id, {
      include: [
        {
          model: Order,
          as: 'order',
          include: [
            {
              model: Customer,
              as: 'customer',
              attributes: ['customer_id', 'name', 'email', 'phone']
            }
          ]
        }
      ]
    });

    if (!payment) {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    console.log(`✅ Found payment: ${payment.payment_id}`);

    // Transform data for frontend compatibility
    const transformedPayment = {
      id: payment.payment_id,
      orderId: payment.order_id,
      orderNumber: payment.order?.order_number || 'N/A',
      customerName: payment.order?.customer?.name || 'Unknown Customer',
      amount: parseFloat(payment.amount),
      paymentMethod: payment.payment_method,
      paymentDate: payment.payment_date?.split('T')[0] || payment.created_at?.split('T')[0],
      status: payment.status,
      notes: payment.notes,
      createdAt: payment.created_at,
      updatedAt: payment.updated_at
    };

    // Log data change for monitoring
    if (global.logDataChange) {
      global.logDataChange('READ', 'payments', transformedPayment, req.user?.user_id);
    }

    res.json({
      success: true,
      data: transformedPayment
    });
  } catch (error) {
    console.error('❌ Error fetching payment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch payment',
      error: error.message
    });
  }
});

// Create new payment
router.post('/', adminOrSalesman, async (req, res) => {
  try {
    const {
      order_id,
      amount,
      payment_method,
      payment_date,
      notes
    } = req.body;

    console.log(chalk.green(`💳 Creating new payment for order: ${order_id}`));

    // Validate required fields
    if (!order_id || !amount || !payment_method) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: order_id, amount, payment_method'
      });
    }

    // Check if order exists
    const order = await Order.findByPk(order_id);
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Create payment
    const payment = await Payment.create({
      order_id,
      amount: parseFloat(amount),
      payment_method,
      payment_date: payment_date || new Date(),
      status: 'completed',
      notes: notes || null,
      recorded_by: req.user.user_id
    });

    console.log(`✅ Payment created successfully: ${payment.payment_id}`);

    // Log data change for monitoring
    if (global.logDataChange) {
      global.logDataChange('CREATE', 'payments', payment, req.user?.user_id);
    }

    res.status(201).json({
      success: true,
      data: {
        id: payment.payment_id,
        orderId: payment.order_id,
        amount: parseFloat(payment.amount),
        paymentMethod: payment.payment_method,
        paymentDate: payment.payment_date?.split('T')[0],
        status: payment.status,
        notes: payment.notes
      },
      message: 'Payment created successfully'
    });
  } catch (error) {
    console.error('❌ Error creating payment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create payment',
      error: error.message
    });
  }
});

module.exports = router;
