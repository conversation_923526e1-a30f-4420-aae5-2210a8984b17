const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Customer = sequelize.define('Customer', {
  customer_id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false
  },
  type: {
    type: DataTypes.ENUM('Convenience Store', 'Supermarket', 'Wholesaler', 'Restaurant', 'Other'),
    allowNull: false,
    defaultValue: 'Convenience Store'
  },
  contact: {
    type: DataTypes.STRING,
    allowNull: false
  },
  phone: {
    type: DataTypes.STRING
  },
  email: {
    type: DataTypes.STRING,
    validate: {
      isEmail: true
    }
  },
  address: {
    type: DataTypes.TEXT
  },
  credit_limit: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0
  },
  current_balance: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0
  },
  payment_terms: {
    type: DataTypes.STRING,
    defaultValue: 'Net 30'
  },
  payment_method: {
    type: DataTypes.ENUM('Cash', 'Credit', 'Bank Transfer'),
    defaultValue: 'Cash'
  },
  total_orders: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  total_spent: {
    type: DataTypes.DECIMAL(15, 2),
    defaultValue: 0
  },
  last_order_date: {
    type: DataTypes.DATE
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive', 'suspended'),
    defaultValue: 'active'
  },
  salesman_id: {
    type: DataTypes.UUID,
    references: {
      model: 'salesmen',
      key: 'salesman_id'
    }
  }
}, {
  tableName: 'customers'
});

module.exports = Customer;
