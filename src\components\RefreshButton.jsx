import React, { useState } from 'react';
import { RefreshCwIcon } from 'lucide-react';

const RefreshButton = ({ 
  onRefresh, 
  size = 'md', 
  variant = 'primary',
  label = 'Refresh',
  showLabel = true,
  className = '',
  disabled = false
}) => {
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    if (isRefreshing || disabled) return;

    setIsRefreshing(true);
    try {
      await onRefresh();
    } catch (error) {
      console.error('Refresh failed:', error);
      // You could add a toast notification here
    } finally {
      setIsRefreshing(false);
    }
  };

  // Size classes
  const sizeClasses = {
    sm: 'px-2 py-1 text-sm',
    md: 'px-3 py-2',
    lg: 'px-4 py-3 text-lg'
  };

  // Icon sizes
  const iconSizes = {
    sm: 14,
    md: 16,
    lg: 20
  };

  // Variant classes
  const variantClasses = {
    primary: 'bg-blue-600 hover:bg-blue-700 text-white',
    secondary: 'bg-gray-600 hover:bg-gray-700 text-white',
    outline: 'border border-gray-300 hover:bg-gray-50 text-gray-700',
    ghost: 'hover:bg-gray-100 text-gray-600'
  };

  const baseClasses = `
    inline-flex items-center space-x-2 rounded-md font-medium
    transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
    ${disabled || isRefreshing ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
    ${sizeClasses[size]}
    ${variantClasses[variant]}
    ${className}
  `.trim().replace(/\s+/g, ' ');

  return (
    <button
      onClick={handleRefresh}
      disabled={disabled || isRefreshing}
      className={baseClasses}
      title={isRefreshing ? 'Refreshing...' : `${label} data`}
    >
      <RefreshCwIcon 
        size={iconSizes[size]} 
        className={isRefreshing ? 'animate-spin' : ''} 
      />
      {showLabel && (
        <span>{isRefreshing ? 'Refreshing...' : label}</span>
      )}
    </button>
  );
};

export default RefreshButton;
